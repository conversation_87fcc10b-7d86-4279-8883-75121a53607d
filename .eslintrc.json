{"extends": ["next/core-web-vitals", "next/typescript", "prettier"], "plugins": ["prettier"], "rules": {"prettier/prettier": ["warn"], "arrow-body-style": ["off"], "prefer-arrow-callback": ["off"], "react/display-name": ["off"], "@typescript-eslint/no-explicit-any": ["warn"], "@typescript-eslint/no-unused-vars": ["warn", {"argsIgnorePattern": "^_", "varsIgnorePattern": "^_"}], "no-console": ["warn", {"allow": ["warn", "error", "info"]}], "react/no-unescaped-entities": "off"}, "ignorePatterns": ["node_modules/**/*", ".next/**/*", "dist/**/*", "build/**/*"]}