import {
  addDays,
  addHours,
  differenceInHours,
  endOfDay,
  isSameDay,
  isValid as isValidDate,
  startOfDay,
} from "date-fns"
import { getCookie, setCookie } from "./cookies"

// Normalize timestamp to milliseconds - example: normalizeTimestamp(1640995200) returns 1640995200000
export const normalizeTimestamp = (
  timestamp: number | null | undefined,
): number | null => {
  if (!timestamp || timestamp <= 0) return null

  // Convert seconds to milliseconds if timestamp is before year 2100
  if (timestamp < 4102444800) {
    return timestamp * 1000
  }

  // Already in milliseconds format
  return timestamp
}

// Function to get date data from localStorage or cookies.
export const getDateData = (): Record<string, unknown> | null => {
  if (typeof window === "undefined") return null

  try {
    if (typeof window == "undefined") return null
    const localData = localStorage?.getItem("dateData")
    if (localData) return JSON.parse(localData)

    const cookieData = getCookie("dateData")
    if (cookieData) return JSON.parse(cookieData)
  } catch (error) {
    console.error("Error parsing dateData", error)
  }
  return null
}

// Function to get one day after a given date.
export const getOneDayAfter = (date: string | number | Date): string => {
  const myDate = new Date(date)
  if (isValidDate(myDate)) {
    return addDays(myDate, 1).toDateString()
  }
  return ""
}

// Function to get one day before a given date.
export const getOneDayBefore = (date: string | number | Date): string => {
  const myDate = new Date(date)
  if (isValidDate(myDate)) {
    return addDays(myDate, -1).toDateString()
  }
  return ""
}

// Function to calculate the transit date after a minimum threshold of hours.
export const calculateTransitDate = (minTh: number): Date => {
  const now = new Date()
  const futureDate = addHours(now, minTh)

  // If the time goes beyond 24 hours, reset to start of next day
  if (futureDate.getHours() >= 24) {
    return startOfDay(addDays(now, 1))
  }

  return futureDate
}

// Update local date data in cookies and localStorage with dispatch
export const updateLocalDateData = (
  newDateData: Record<string, unknown>,
  dispatch: (action: {
    type: string
    payload: Record<string, unknown>
  }) => void,
): void => {
  const serializedData = JSON.stringify(newDateData)
  setCookie("dateData", serializedData, {
    expires: 60 * 60 * 24 * 365,
  }) // 1 year max-age
  localStorage?.setItem("dateData", serializedData)
  dispatch({ type: "dateUpdate", payload: newDateData })
}

// Convert Unix timestamp to human-readable date - example: convertTimeIntoDate(1640995200000) returns "December 31, 2021"
export const convertTimeIntoDate = (time: number): string => {
  const dateObject = new Date(time)
  return dateObject.toLocaleDateString("en-US", {
    day: "numeric",
    month: "long",
    year: "numeric",
  })
}

// Function to format a date into a specific string format (e.g., DD-MMM-YYYY).
export const formattedDate = (date: string | number | Date): string =>
  new Date(date)
    .toLocaleDateString("en-GB", {
      day: "numeric",
      month: "short",
      year: "numeric",
    })
    .replace(/ /g, "-")

// Function to calculate the difference in days between two dates.
export const getDateDifference = (
  delivery_date: string | Date,
  pickup_date: string | Date,
): number | null => {
  const start = new Date(delivery_date)
  const end = new Date(pickup_date)

  if (!isValidDate(start) || !isValidDate(end)) {
    return null
  }

  return Math.floor((end.getTime() - start.getTime()) / (1000 * 60 * 60 * 24)) // Difference in days
}

// Format timestamp to readable date and time with AM/PM - example: formatTimestampToDateTime(1640995200000) returns "Dec 31, 2021, 4:00 PM"
export const formatTimestampToDateTime = (
  timestamp: number | null,
  options?: {
    includeYear?: boolean
    includeSeconds?: boolean
    timeZone?: string
  },
): string => {
  const normalizedTimestamp = normalizeTimestamp(timestamp)
  if (!normalizedTimestamp) return "Not selected"

  const date = new Date(normalizedTimestamp)
  const { includeYear = true, includeSeconds = false, timeZone } = options || {}

  const dateOptions: Intl.DateTimeFormatOptions = {
    month: "short",
    day: "numeric",
    ...(includeYear && { year: "numeric" }),
    hour: "numeric",
    minute: "2-digit",
    ...(includeSeconds && { second: "2-digit" }),
    hour12: true,
    ...(timeZone && { timeZone }),
  }

  return date.toLocaleDateString("en-US", dateOptions)
}

// Format timestamp to readable date only - example: formatTimestampToDate(1640995200000) returns "Dec 31, 2021"
export const formatTimestampToDate = (
  timestamp: number | null,
  options?: {
    includeYear?: boolean
    format?: "short" | "long"
  },
): string => {
  const normalizedTimestamp = normalizeTimestamp(timestamp)
  if (!normalizedTimestamp) return "Not selected"

  const date = new Date(normalizedTimestamp)
  const { includeYear = true, format = "short" } = options || {}

  const dateOptions: Intl.DateTimeFormatOptions = {
    month: format,
    day: "numeric",
    ...(includeYear && { year: "numeric" }),
  }

  return date.toLocaleDateString("en-US", dateOptions)
}

// Format timestamp to readable time only with AM/PM
export const formatTimestampToTime = (
  timestamp: number | null,
  options?: {
    includeSeconds?: boolean
    timeZone?: string
  },
): string => {
  const normalizedTimestamp = normalizeTimestamp(timestamp)
  if (!normalizedTimestamp) return "Not selected"

  const date = new Date(normalizedTimestamp)
  const { includeSeconds = false, timeZone } = options || {}

  const timeOptions: Intl.DateTimeFormatOptions = {
    hour: "numeric",
    minute: "2-digit",
    ...(includeSeconds && { second: "2-digit" }),
    hour12: true,
    ...(timeZone && { timeZone }),
  }

  return date.toLocaleTimeString("en-US", timeOptions)
}

// Format duration in hours to human readable format
export const formatDurationFromHours = (hours: number | null): string => {
  if (!hours || hours <= 0) return "0 Hours"

  if (hours < 24) {
    // For less than 24 hours, show hours and minutes if applicable
    const wholeHours = Math.floor(hours)
    const minutes = Math.round((hours - wholeHours) * 60)

    if (minutes === 0) {
      return `${wholeHours} Hour${wholeHours !== 1 ? "s" : ""}`
    } else if (wholeHours === 0) {
      return `${minutes} Minute${minutes !== 1 ? "s" : ""}`
    } else {
      return `${wholeHours} Hour${wholeHours !== 1 ? "s" : ""}, ${minutes} Minute${minutes !== 1 ? "s" : ""}`
    }
  } else {
    const days = Math.floor(hours / 24)
    const remainingHours = hours % 24
    const wholeRemainingHours = Math.floor(remainingHours)
    const minutes = Math.round((remainingHours - wholeRemainingHours) * 60)

    let result = `${days} Day${days !== 1 ? "s" : ""}`

    if (wholeRemainingHours > 0 && minutes > 0) {
      result += `, ${wholeRemainingHours} Hour${wholeRemainingHours !== 1 ? "s" : ""}, ${minutes} Minute${minutes !== 1 ? "s" : ""}`
    } else if (wholeRemainingHours > 0) {
      result += `, ${wholeRemainingHours} Hour${wholeRemainingHours !== 1 ? "s" : ""}`
    } else if (minutes > 0) {
      result += `, ${minutes} Minute${minutes !== 1 ? "s" : ""}`
    }

    return result
  }
}

// Format duration between two timestamps
export const formatDurationFromTimestamps = (
  startTimestamp: number | null,
  endTimestamp: number | null,
): string => {
  const normalizedStart = normalizeTimestamp(startTimestamp)
  const normalizedEnd = normalizeTimestamp(endTimestamp)

  if (!normalizedStart || !normalizedEnd) return "0 hours"

  const startDate = new Date(normalizedStart)
  const endDate = new Date(normalizedEnd)
  const durationHours = differenceInHours(endDate, startDate)

  return formatDurationFromHours(durationHours)
}

// Format date and time from separate date and time values (legacy support)
export const formatDateTime = (
  date: Date | null,
  time: string | null,
): string => {
  if (!date || !time) return "Not selected"

  // Handle both "HH:mm" and "HH" formats
  const timeFormatted = time.includes(":")
    ? time
    : `${time.padStart(2, "0")}:00`

  // Parse time to get 12-hour format with AM/PM
  const [hours, minutes] = timeFormatted.split(":").map(Number)
  const date12Hour = new Date(date)
  date12Hour.setHours(hours, minutes, 0, 0)

  const dateStr = date.toLocaleDateString("en-US", {
    month: "short",
    day: "numeric",
    year: "numeric",
  })

  const timeStr = date12Hour.toLocaleTimeString("en-US", {
    hour: "numeric",
    minute: "2-digit",
    hour12: true,
  })

  return `${dateStr} at ${timeStr}`
}

// Get relative time description (e.g., "in 2 hours", "tomorrow")
export const getRelativeTimeDescription = (
  timestamp: number | null,
): string => {
  const normalizedTimestamp = normalizeTimestamp(timestamp)
  if (!normalizedTimestamp) return ""

  const now = Date.now()
  const diffHours = (normalizedTimestamp - now) / (1000 * 60 * 60)
  const diffDays = diffHours / 24

  if (diffHours < 1) {
    const diffMinutes = Math.round((normalizedTimestamp - now) / (1000 * 60))
    if (diffMinutes <= 0) return "Now"
    return `in ${diffMinutes} minute${diffMinutes !== 1 ? "s" : ""}`
  } else if (diffHours < 24) {
    const hours = Math.round(diffHours)
    return `in ${hours} hour${hours !== 1 ? "s" : ""}`
  } else if (diffDays < 7) {
    const days = Math.round(diffDays)
    if (days === 1) return "Tomorrow"
    return `in ${days} days`
  } else {
    return formatTimestampToDate(timestamp, { includeYear: false })
  }
}

// Validate if a timestamp is in the future
export const isTimestampInFuture = (timestamp: number | null): boolean => {
  const normalizedTimestamp = normalizeTimestamp(timestamp)
  if (!normalizedTimestamp) return false
  const now = Date.now()
  return normalizedTimestamp > now
}

// Validate if a timestamp is within business hours (8 AM - 10 PM)
export const isTimestampInBusinessHours = (
  timestamp: number | null,
): boolean => {
  const normalizedTimestamp = normalizeTimestamp(timestamp)
  if (!normalizedTimestamp) return false

  const date = new Date(normalizedTimestamp)
  const hour = date.getHours()
  return hour >= 8 && hour <= 22
}

// Get the current timestamp in milliseconds
export const getCurrentTimestamp = (): number => {
  return Date.now()
}

// Add hours to a timestamp
export const addHoursToTimestamp = (
  timestamp: number | null,
  hours: number,
): number | null => {
  const normalizedTimestamp = normalizeTimestamp(timestamp)
  if (!normalizedTimestamp) return null

  const date = new Date(normalizedTimestamp)
  const newDate = addHours(date, hours)
  return newDate.getTime()
}

// Get start of day timestamp for a given timestamp
export const getStartOfDayTimestamp = (
  timestamp: number | null,
): number | null => {
  const normalizedTimestamp = normalizeTimestamp(timestamp)
  if (!normalizedTimestamp) return null

  const date = new Date(normalizedTimestamp)
  return startOfDay(date).getTime()
}

// Get end of day timestamp for a given timestamp
export const getEndOfDayTimestamp = (
  timestamp: number | null,
): number | null => {
  const normalizedTimestamp = normalizeTimestamp(timestamp)
  if (!normalizedTimestamp) return null

  const date = new Date(normalizedTimestamp)
  return endOfDay(date).getTime()
}

// Check if two timestamps are on the same day
export const areTimestampsOnSameDay = (
  timestamp1: number | null,
  timestamp2: number | null,
): boolean => {
  const normalizedTimestamp1 = normalizeTimestamp(timestamp1)
  const normalizedTimestamp2 = normalizeTimestamp(timestamp2)

  if (!normalizedTimestamp1 || !normalizedTimestamp2) return false

  const date1 = new Date(normalizedTimestamp1)
  const date2 = new Date(normalizedTimestamp2)

  return isSameDay(date1, date2)
}

// Format timestamp to display format - example: formatTimestampToDisplayFormat(1640995200000) returns "31 Dec | 04:00 PM"
export const formatTimestampToDisplayFormat = (
  timestamp: number | null,
): string => {
  const normalizedTimestamp = normalizeTimestamp(timestamp)
  if (!normalizedTimestamp) return "Not selected"

  const date = new Date(normalizedTimestamp)

  const dateStr = date.toLocaleDateString("en-GB", {
    day: "2-digit",
    month: "short",
  })

  const timeStr = date.toLocaleTimeString("en-US", {
    hour: "2-digit",
    minute: "2-digit",
    hour12: true,
  })

  return `${dateStr} | ${timeStr}`
}

// Format timestamp to separate date and time objects for rental display
export const formatTimestampForRental = (
  timestamp: number | null,
): { date: string; time: string; displayFormat: string } => {
  const normalizedTimestamp = normalizeTimestamp(timestamp)

  if (!normalizedTimestamp) {
    return {
      date: "Invalid Date",
      time: "Invalid Time",
      displayFormat: "Not selected",
    }
  }

  try {
    const date = new Date(normalizedTimestamp)

    const dateStr = date.toLocaleDateString("en-GB", {
      day: "2-digit",
      month: "short",
    })

    const timeStr = date.toLocaleTimeString("en-US", {
      hour: "2-digit",
      minute: "2-digit",
      hour12: true,
    })

    const displayFormat = `${dateStr} | ${timeStr}`

    return {
      date: dateStr,
      time: timeStr,
      displayFormat,
    }
  } catch (error) {
    console.error("Error formatting timestamp for rental:", error)
    return {
      date: "Invalid Date",
      time: "Invalid Time",
      displayFormat: "Invalid Date",
    }
  }
}
