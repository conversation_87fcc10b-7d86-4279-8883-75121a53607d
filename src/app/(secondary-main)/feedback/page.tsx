"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Typography } from "@/components/ui/typography"
import SpImage from "@/shared/SpImage/sp-image"
import { motion } from "framer-motion"
import { useState } from "react"
import { toast } from "sonner"

export default function FeedbackPage() {
  const [bikeModel, setBikeModel] = useState("")
  const [isSubmitting, setIsSubmitting] = useState(false)

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!bikeModel.trim()) {
      toast.error("Please enter a bike model")
      return
    }

    setIsSubmitting(true)

    // Simulate API call
    try {
      await new Promise((resolve) => setTimeout(resolve, 1000))
      toast.success(
        "Thank you for your feedback! We'll consider your suggestion.",
      )
      setBikeModel("")
    } catch (error) {
      toast.error("Something went wrong. Please try again.")
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <div className='min-h-screen px-4 py-8 md:py-24'>
      <div className='container mx-auto rounded-3xl bg-gray-100 p-10 md:py-16'>
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className='text-center'
        >
          {/* Main Title */}
          <Typography
            as='h1'
            className='mb-8 font-ubuntu text-d6 font-bold text-gray-900 md:text-d4'
          >
            Next Ride: Whatever You Say, Pal!
          </Typography>

          {/* Motorcycle Illustration */}
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className='mb-12'
          >
            {/* <MotorcycleIcon /> */}
            <SpImage
              src='https://images.sharepal.in/bike-rental/guess-bike.webp'
              className='w-[400px]'
              height={800}
              width={1960}
              containerClassName='w-full h-auto flex items-center justify-center'
            />
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
            className='mx-auto mb-8 max-w-2xl'
          >
            {/* Question */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.4 }}
              className='mb-8'
            >
              <Typography
                as='h2'
                className='mb-6 font-ubuntu text-h3 font-semibold text-primary-500 md:text-h2'
              >
                What Should We Launch Next?
              </Typography>
            </motion.div>

            {/* Form */}
            <motion.form
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.6 }}
              onSubmit={handleSubmit}
              className='max-w-2xl space-y-6'
            >
              <div className='text-left'>
                <Typography
                  as='label'
                  className='mb-2 block text-b3 font-medium text-gray-700'
                >
                  Type/Model <span className='text-red-500'>*</span>
                </Typography>
                <Input
                  type='text'
                  placeholder='e.g. Himalayan 450'
                  value={bikeModel}
                  onChange={(e) => setBikeModel(e.target.value)}
                  className='h-12 text-b3 placeholder:text-gray-400'
                  disabled={isSubmitting}
                />
              </div>

              <Button
                type='submit'
                variant='default'
                size='extra-lg'
                disabled={isSubmitting || !bikeModel.trim()}
                className='w-full max-w-xs rounded-full bg-primary-900 text-bt2 font-semibold text-white hover:bg-primary-800 disabled:opacity-50'
              >
                {isSubmitting ? "Submitting..." : "Submit"}
              </Button>
            </motion.form>
          </motion.div>
        </motion.div>
      </div>
    </div>
  )
}
