"use client"
import { Stepper } from "@/components/checkout/checkout-stepper"
import { ContactDetailsForm } from "@/components/checkout/contact-details-form"
import { BikeOrderSummaryNew } from "@/components/checkout/order-summary-new"
import { ReviewAndPay } from "@/components/checkout/review-and-pay"
import { SectionHeader } from "@/components/checkout/section-header"
import { parseCheckoutUrlParams } from "@/lib/bike-rental-url-utils"
import { useCheckoutStore } from "@/store/checkout-store"
import { useCityStore } from "@/store/city-store"
import { useSearchParams } from "next/navigation"
import { Suspense, useEffect, useState } from "react"
import { toast } from "sonner"

import { CarepalForm } from "@/components/checkout/carepal/carepal-form"
import { CheckoutError } from "@/components/checkout/checkout-error"
import { RentalPeriodDisplay } from "@/components/checkout/rental-period-display"
import CheckoutSkeleton from "@/components/skeletons/checkout-skeleton"
import { roundValue } from "@/functions/business-logics"
import { moneyFormatter } from "@/functions/small-functions"
import {
  checkBikeAvailability,
  fetchBikeDetailsForCheckout,
} from "@/services/bikes"
import { initializeSEON } from "@/services/tools"
import { getUserWallet } from "@/services/user"
import { useBikeRentalStore } from "@/store/bike-rental-store"
import { useOnboardingStore } from "@/store/onboarding-store"
import { useUserStore } from "@/store/user-store"
import { useQuery } from "@tanstack/react-query"
import { motion } from "framer-motion"
import { CircleXIcon } from "lucide-react"
import dynamic from "next/dynamic"
import { VerifiedTickFilledIcon } from "sharepal-icons"

// Import BikeRentalSummary component

const CheckoutRedirectLoading = dynamic(
  () => import("@/components/loadings/checkout-redirect-loading"),
  {
    ssr: false,
  },
)

// Animation variants
const sectionVariants = {
  hidden: { height: 0, opacity: 0 },
  visible: { height: "auto", opacity: 1 },
}

function CheckoutPageContent() {
  const { openModal, closeModal, setIsNonCloseable } = useOnboardingStore()
  const { isLoggedIn } = useUserStore()
  const searchParams = useSearchParams()
  const { setFromUrlParams } = useBikeRentalStore()

  const {
    active_section,
    is_contact_completed,
    is_review_completed,
    is_carepal_completed,
    goToNextSection,
    goToPrevSection,
    setWalletBalance,
    setContactDetails,
    checkout_redirect_loading,
    setSeonFingerprint,
    setCouponDiscount,
    setAppliedCouponCode,
    setHandlingCharges,
    contact_details,
    selected_bike,
    rental_params,
    total_hours,
    total_rent,
    initializeBikeRental,
    final_amount,
    carepal_selected,
    carepal_coverage,
  } = useCheckoutStore()

  const { setWallet, user, userLoading } = useUserStore()
  const { selectedCity } = useCityStore()

  // Parse URL parameters for bike rental data
  const [urlParamsError, setUrlParamsError] = useState<string | null>(null)
  const [parsedUrlData, setParsedUrlData] = useState<{
    bike_code: string
    hub_code: string
    pickup_timestamp: number
    dropoff_timestamp: number
  } | null>(null)

  useEffect(() => {
    const parseResult = parseCheckoutUrlParams(searchParams)
    // console.log(parseResult, "parse")
    if (!parseResult.isValid) {
      setUrlParamsError(
        `Invalid checkout parameters: ${parseResult.errors.join(", ")}`,
      )
      return
    }
    setParsedUrlData(parseResult.data || null)
    if (parseResult.data) {
      setFromUrlParams(parseResult.data)
    }
    setUrlParamsError(null)
  }, [searchParams])

  useEffect(() => {
    if (user) {
      setContactDetails({
        calling_number: user.calling_number,
        country_code_calling: user.country_code_calling,
        country_code_whatsapp: user.country_code_whatsapp,
        email: user.email,
        first_name: user.first_name,
        last_name: user.last_name,
        whatsapp_number: user.whatsapp_number,
      })
    }
  }, [user, isLoggedIn, setContactDetails])

  // Fetch bike details using useQuery
  const {
    data: bikeDetails,
    isLoading: bikeDetailsLoading,
    error: bikeDetailsError,
  } = useQuery({
    queryKey: ["bikeDetails", parsedUrlData?.bike_code, selectedCity.city_url],
    queryFn: () =>
      fetchBikeDetailsForCheckout(
        parsedUrlData!.bike_code,
        selectedCity.city_url,
      ),
    enabled: !!parsedUrlData && !urlParamsError,
    retry: false,
    refetchOnWindowFocus: false,
  })

  // Check bike availability using useQuery
  const {
    data: isAvailable,
    isLoading: availabilityLoading,
    error: availabilityError,
  } = useQuery({
    queryKey: [
      "bikeAvailability",
      parsedUrlData?.bike_code,
      parsedUrlData?.pickup_timestamp,
      parsedUrlData?.dropoff_timestamp,
      parsedUrlData?.hub_code,
      selectedCity.city_url,
    ],
    queryFn: () =>
      checkBikeAvailability(
        parsedUrlData!.pickup_timestamp,
        parsedUrlData!.dropoff_timestamp,
        parsedUrlData!.bike_code,
        selectedCity.city_url,
        parsedUrlData!.hub_code,
      ),
    enabled: !!parsedUrlData && !urlParamsError,
    retry: false,
    refetchOnWindowFocus: false,
  })

  // Initialize bike rental data when both queries succeed
  useEffect(() => {
    // console.log("bike details", bikeDetails)
    if (bikeDetails && parsedUrlData && !selected_bike) {
      try {
        // Use the new streamlined initialization method
        initializeBikeRental(bikeDetails, {
          pickup_timestamp: parsedUrlData.pickup_timestamp,
          dropoff_timestamp: parsedUrlData.dropoff_timestamp,
          hub_code: parsedUrlData.hub_code,
        })

        if (!availabilityLoading && !isAvailable) {
          toast.error(
            "Bike is not available for the selected time. Please choose a different time or bike.",
          )
          return
        }
        // Bike rental successfully initialized
      } catch (error) {
        console.error("Error initializing bike rental:", error)
        toast.error("Failed to initialize checkout. Please try again.")
      }
    }
  }, [
    bikeDetails,
    isAvailable,
    parsedUrlData,
    selected_bike,
    initializeBikeRental,
  ])

  // Handle errors from queries
  useEffect(() => {
    if (bikeDetailsError) {
      toast.error("Unable to fetch bike details. Please try again.")
    }
    if (availabilityError) {
      toast.error("Unable to check bike availability. Please try again.")
    }
  }, [bikeDetailsError, availabilityError])

  // For bike rentals, we don't need cart items - we have bike rental data
  const cartItemsLoading = bikeDetailsLoading || availabilityLoading
  // const cartError = bikeDetailsError || availabilityError ? true : null

  // Cart-related error handling for bike rental context
  const bikeRentalError = bikeDetailsError || availabilityError ? true : null

  const {} = useQuery({
    queryKey: ["user_wallet"], // Unique query key
    queryFn: async () => {
      const wallet = await getUserWallet()
      setWalletBalance(roundValue(wallet.amount))
      setWallet(wallet)
      return wallet
    }, // Async function to fetch SEON session

    refetchOnWindowFocus: true, // Prevent refetch on window focus
    enabled: !userLoading, // Enable the query when user data is loaded
  })

  const {} = useQuery({
    queryKey: ["seon_session"], // Unique query key
    queryFn: async () => {
      const session = await initializeSEON()
      setSeonFingerprint(session)
      return session
    },
    // Async function to fetch SEON session
    // retry: false,
    // // Optionally disable retries if initialization fails
    // Prevent refetch on window focus
    refetchOnWindowFocus: false,
    // Enable the query when user data is loaded
    enabled: !userLoading,
  })

  // Bike rental specific tracking
  useEffect(() => {
    if (selected_bike && rental_params) {
      // TODO: Add bike rental specific tracking
      // Track checkout progress for analytics
    }
  }, [selected_bike, rental_params, final_amount, active_section])

  useEffect(() => {
    if (!isLoggedIn && !cartItemsLoading && !userLoading) openModal(true)
    else {
      setIsNonCloseable(false)
      closeModal()
    }
    // Clean up function
    return () => {
      setIsNonCloseable(false)
      closeModal()
    }
  }, [
    cartItemsLoading, // Keep this for now as it represents bike data loading
    closeModal,
    isLoggedIn,
    openModal,
    setIsNonCloseable,
    userLoading,
  ])

  // For bike rentals, reset coupon and charges when no bike rental data
  useEffect(() => {
    if (!selected_bike || !rental_params) {
      setCouponDiscount(0)
      setAppliedCouponCode("", 0)
      setHandlingCharges(0)
    }
  }, [
    selected_bike,
    rental_params,
    setAppliedCouponCode,
    setCouponDiscount,
    setHandlingCharges,
  ])

  if (cartItemsLoading || userLoading) {
    return <CheckoutSkeleton />
  } else if (urlParamsError) {
    return <CheckoutError cartError={true} />
  } else if (bikeRentalError) {
    return <CheckoutError cartError={bikeRentalError} />
  } else if (checkout_redirect_loading) {
    return <CheckoutRedirectLoading />
  } else if (!isLoggedIn) {
    return <></>
  } else {
    return (
      <>
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className='w-full py-16 pb-32 md:py-24'
        >
          {/* {!isAvailable && (
            // highligh not available
            <div className='fixed left-0 right-0 top-0 w-full bg-warning-300 p-3'>
              This bike is not available
            </div>
          )} */}
          {/* <div className="container grid grid-cols-1 gap-3 lg:gap-6 lg:grid-cols-3"> */}
          <div className='container grid grid-cols-1 gap-3 lg:grid-cols-[1fr,400px] lg:gap-6'>
            <Stepper
              currentSection={active_section}
              is_contact_completed={is_contact_completed}
              is_review_completed={is_review_completed}
              is_carepal_completed={is_carepal_completed}
              onNext={goToNextSection}
              onPrev={goToPrevSection}
            />

            {selected_bike && rental_params && (
              <div className='mb-4 lg:hidden'>
                <RentalPeriodDisplay
                  showLocation={true}
                  showRentPeriodOnly={true}
                />
              </div>
            )}
            <div className='lg:col-span- relative rounded-2xl bg-gray-100 p-4 md:rounded-radius'>
              {/* Mobile View */}

              <div className='md:hidden'>
                {[
                  { name: "contact", fullName: "Contact Details" },
                  { name: "carepal", fullName: "Damage Waiver" },
                  { name: "review", fullName: "Review & Pay" },
                ].map((section, index) => (
                  <motion.div
                    key={section.name}
                    variants={sectionVariants}
                    initial='hidden'
                    animate={
                      active_section === section.name ? "visible" : "hidden"
                    }
                    transition={{ duration: 0.3 }}
                    className='overflow-hidden'
                  >
                    <h2 className='mb-4 text-center text-xl font-semibold'>
                      {`${index + 1}. ${section.fullName}`}
                    </h2>
                    {section.name === "contact" && <ContactDetailsForm />}
                    {section.name === "carepal" && <CarepalForm />}
                    {section.name === "review" && <ReviewAndPay />}
                  </motion.div>
                ))}
              </div>

              {/* Desktop View */}
              <div className='hidden md:block'>
                {/* Contact Details Section */}
                <div className='border-b border-neutral-150'>
                  <SectionHeader
                    number={1}
                    title='Contact Details'
                    section='contact'
                    isCompleted={is_contact_completed}
                    canOpen={true}
                  >
                    {is_contact_completed && (
                      <div className='text-b2 text-neutral-500'>
                        <p>
                          {contact_details?.first_name}{" "}
                          {contact_details?.last_name}
                        </p>
                        <p>{contact_details?.calling_number}</p>
                        <p>{contact_details?.whatsapp_number} (Whatsapp)</p>
                        <p>{contact_details?.email}</p>
                      </div>
                    )}
                  </SectionHeader>
                  <motion.div
                    initial={{ height: 0, opacity: 0 }}
                    animate={{
                      height: active_section === "contact" ? "auto" : 0,
                      opacity: active_section === "contact" ? 1 : 0,
                    }}
                    transition={{ duration: 0.3 }}
                    className='overflow-hidden'
                  >
                    <ContactDetailsForm />
                  </motion.div>
                </div>

                {/* Carepal Section */}
                <div className='border-b border-neutral-150'>
                  <SectionHeader
                    number={2}
                    title={`CarePal - Get a damage waiver of ${moneyFormatter(carepal_coverage)}`}
                    section='carepal'
                    isCompleted={is_carepal_completed}
                    canOpen={true}
                  >
                    {is_carepal_completed && (
                      <div className='text-b2 text-neutral-500'>
                        {carepal_selected ? (
                          <VerifiedTickFilledIcon className='size-4 text-success-500 md:size-8' />
                        ) : (
                          <CircleXIcon className='size-4 text-destructive-500 md:size-8' />
                        )}
                      </div>
                    )}
                  </SectionHeader>
                  <motion.div
                    initial={{ height: 0, opacity: 0 }}
                    animate={{
                      height: active_section === "carepal" ? "auto" : 0,
                      opacity: active_section === "carepal" ? 1 : 0,
                    }}
                    transition={{ duration: 0.3 }}
                    className='overflow-hidden'
                  >
                    <CarepalForm />
                  </motion.div>
                </div>

                {/* Review & Pay Section */}
                <div>
                  <SectionHeader
                    number={3}
                    title='Review & Pay'
                    section='review'
                    isCompleted={is_review_completed}
                    canOpen={true}
                    updateLabel='Review Items'
                  >
                    {selected_bike && (
                      <div className='text-b2 text-neutral-500'>
                        <p>
                          {selected_bike.name} ({selected_bike.bike_code})
                        </p>
                        <p>{total_hours} hours rental</p>
                        <p>₹{total_rent}</p>
                      </div>
                    )}
                  </SectionHeader>
                  <motion.div
                    initial={{ height: 0, opacity: 0 }}
                    animate={{
                      height: active_section === "review" ? "auto" : 0,
                      opacity: active_section === "review" ? 1 : 0,
                    }}
                    transition={{ duration: 0.3 }}
                    className='overflow-hidden'
                  >
                    <ReviewAndPay />
                  </motion.div>
                </div>
              </div>
            </div>

            <div className='hidden self-start md:block lg:sticky lg:top-5 lg:col-span-1'>
              <BikeOrderSummaryNew
                onDateChange={(range) => {
                  console.info("Date range changed:", range)
                }}
                onApplyCoupon={async (code) => {
                  console.info("Applyng coupon:", code)
                }}
                onRemoveCoupon={() => {
                  // console.log('Removing coupon')
                }}
                onPlaceOrder={async () => {
                  // console.log('Placing order')
                }}
              />
            </div>
          </div>
        </motion.div>
      </>
    )
  }
}

// Wrap CheckoutPageContent in Suspense to handle useSearchParams() properly
export default function CheckoutPage() {
  return (
    <Suspense fallback={<CheckoutSkeleton />}>
      <CheckoutPageContent />
    </Suspense>
  )
}
