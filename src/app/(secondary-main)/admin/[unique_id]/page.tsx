"use client"
import { setCookie } from "@/functions/cookies"
import generateUniqueId from "@/functions/generate-uuid"
import { useCityStore } from "@/store/city-store"
import { useUserStore } from "@/store/user-store"
import { useQuery } from "@tanstack/react-query"
import { useParams, useRouter, useSearchParams } from "next/navigation"
import { Suspense } from "react"
import { LoadingAnimationBlueStrokeIcon } from "sharepal-icons"

const MainPage = () => (
  <Suspense>
    <Page />
  </Suspense>
)

const Page = () => {
  const query = useSearchParams()
  const router = useRouter()
  const { unique_id } = useParams<{ unique_id: string }>()
  const { fetchUser } = useUserStore()
  const { selectedCity } = useCityStore()
  useQuery({
    queryKey: ["backend-login-update"],
    queryFn: async () => {
      const data = await fetch(
        "https://api.sharepal.in/api:AIoqxnqr/admin/backend/login",
        {
          method: "POST",
          body: JSON.stringify({
            unique_login_id: unique_id,
          }),
          headers: {
            Authorization: `Bearer ${query.get("token")}`,
            "content-type": "application/json",
          },
        },
      )
      const response = await data.json()
      if (response) {
        // Store token in local storage
        localStorage.setItem("token", response)

        // Set a cookie with a unique ID
        setCookie("uid", generateUniqueId(), { expires: 365 })

        // Store a flag in session storage
        sessionStorage.setItem("backend_order", "true")
        await fetchUser(selectedCity.city_url)

        // Replace current URL with home page
        router.refresh()
        router.replace("/")
      }
      return data
    },
  })

  // Loading state while processing
  // return <div className='flex h-[200px] w-screen items-center justify-center' />
  return (
    <div className='flex h-screen w-screen items-center justify-center'>
      <LoadingAnimationBlueStrokeIcon className='animate-spin' />
    </div>
  )
}

export default MainPage
