"use client"

import { useEffect, useState } from "react"

import { IconWhatsapp } from "@/components/icons/common"
// import WhatsappSupportModal from "@/components/modals/whatsapp-support"
import WhatsappSupportModal from "@/components/modals/whatsapp-support"
import { Button } from "@/components/ui/button"
import VerificationCards from "@/components/verification/verification-cards"
import { useOnboardingStore } from "@/store/onboarding-store"
import { useUserStore } from "@/store/user-store"
import dynamic from "next/dynamic"

const VerificationStepsNew = dynamic(
  () => import("@/components/verification/verification-steps-new/index"),
  {
    ssr: false,
  },
)

export default function CompleteVerification() {
  // const [step, setStep] = useState<"welcome" | "steps">("welcome")
  const { user, userLoading } = useUserStore()
  const { openModal, closeModal, setIsNonCloseable } = useOnboardingStore()
  const [openAboutVerification, setOpenAboutVerification] = useState(false)
  const [isWhatsappSupportModalOpen, setIsWhatsappSupportModalOpen] =
    useState(false)

  // const searchParams = useSearchParams()

  // const action = searchParams.get("action")
  // console.log("action", action)

  // const handleStart = () => {
  //   setStep("steps")
  //   if (user) trackCompleteVerificationInitiated(user)
  // }

  useEffect(() => {
    if (!user && !userLoading) {
      openModal(true)
    }
    //clean up function
    return () => {
      setIsNonCloseable(false)
      closeModal()
    }
  }, [closeModal, openModal, setIsNonCloseable, user, userLoading])

  return (
    <section className='flex min-h-screen items-start justify-center bg-neutral-100 py-16 md:py-24'>
      <Button
        onClick={() => setIsWhatsappSupportModalOpen(true)}
        className='fixed bottom-20 right-3 z-50 h-12 w-12 rounded-full bg-secondary-600 p-2 hover:bg-secondary-500 md:bottom-8 md:right-5 md:min-h-16 md:min-w-16'
      >
        <IconWhatsapp className='scale-125 md:scale-150' />
      </Button>

      <WhatsappSupportModal
        openView={isWhatsappSupportModalOpen}
        setOpenView={setIsWhatsappSupportModalOpen}
      />
      {/* <AnimatePresence mode='wait'>
        {step === "welcome" ? (
          <VerificationScreen key='welcome' onStart={handleStart} />
        ) : (
          <VerificationSteps key='steps' onBack={() => setStep("welcome")} />
        )}
      </AnimatePresence> */}

      <div className='container flex flex-col-reverse gap-4 lg:flex-row'>
        <div className='w-full space-y-6 md:pr-4 lg:max-w-lg'>
          <VerificationCards
            openAboutVerification={openAboutVerification}
            setOpenAboutVerification={setOpenAboutVerification}
          />
        </div>

        <div className='w-full md:p-4 lg:max-w-2xl'>
          <VerificationStepsNew
            key='steps'
            setOpenAboutVerification={setOpenAboutVerification}
          />
        </div>
      </div>
    </section>
  )
}
