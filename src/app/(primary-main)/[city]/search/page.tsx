import { BikeSearchPage } from "@/components/pages/bike-search-page"
import { generateStaticMetadata } from "@/lib/metadata"
import { Metadata } from "next"

// export const metadata = generateStaticMetadata({
//   title: `Rent Bikes in {city} | Search Premium Adventure Bikes on rent`,
// })
interface ListingPageProps {
  params: Promise<{ city: string }>
  searchParams?: { [key: string]: string | string[] | undefined }
}
export async function generateMetadata(
  { params }: ListingPageProps,
  // parent: ResolvingMetadata,
): Promise<Metadata> {
  const { city } = await params
  return generateStaticMetadata({
    title: `Rent Bikes in ${city} | Search Premium Adventure Bikes on rent`,
    description: `Search and rent premium adventure bikes in ${city}. Choose your pickup and drop-off dates to find the perfect ride for your trip. Flexible hourly and daily rentals with instant online bookings.`,
  })
}

// Simple client-side search page that passes city to BikeSearchPage
export default function SearchPage() {
  return <BikeSearchPage />
}
