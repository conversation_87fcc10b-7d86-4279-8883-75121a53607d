import CityHomePage from "@/components/pages/home-page"
import { generateCityMetadata } from "@/lib/metadata-generators"
import { Metadata, ResolvingMetadata } from "next"

interface ListingPageProps {
  params: Promise<{ city: string }>
  searchParams?: { [key: string]: string | string[] | undefined }
}

export async function generateMetadata(
  { params }: ListingPageProps,
  parent: ResolvingMetadata,
): Promise<Metadata> {
  const { city } = await params
  return generateCityMetadata({ city, parent })
}

export default async function Page({ params, searchParams }: ListingPageProps) {
  const { city } = await params

  return (
    <CityHomePage
      city={city}
      searchParams={searchParams}
      showSeoContent={true}
    />
  )
}
