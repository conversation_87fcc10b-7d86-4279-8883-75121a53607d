import Footer from "@/components/layout/footer"
import Header from "@/components/layout/header/primary-header"
import { fetchCities } from "@/services/city"

export default async function PrimaryLayout({
  children,
}: Readonly<{ children: React.ReactNode }>) {
  const cities = await fetchCities()

  return (
    <>
      <Header cities={cities} />
      <main className='min-h-svh'>{children}</main>
      <Footer />
    </>
  )
}
