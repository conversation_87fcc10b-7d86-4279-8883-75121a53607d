"use client"

import dynamic from "next/dynamic"
import { useParams } from "next/navigation"

const OrderDetailsPage = dynamic(
  () => import("@/components/dashboard/pages/order-details"),
  {
    ssr: false,
  },
)

export default function Page() {
  const params = useParams()
  return (
    <>
      <OrderDetailsPage orderId={params.orderId as string} />
    </>
  )
}

// export default function Page({ params }: { params: { orderId: string } }) {
//   return <OrderDetailsPage orderId={params.orderId} />
// }
