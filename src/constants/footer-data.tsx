import {
  FacebookFilledIcon,
  InstagramFilledIcon,
  LinkedInFilledIcon,
  MailOutlinedIcon,
  PhoneCallFilledIcon,
} from "sharepal-icons"

export const icons = {
  call: <PhoneCallFilledIcon />,
  mail: <MailOutlinedIcon />,
  facebook: <FacebookFilledIcon />,
  instagram: <InstagramFilledIcon />,
  linkedin: <LinkedInFilledIcon />,
}

export const Categoies = [
  {
    title: "Cameras",
    subcategoies: [
      {
        title: "subcategory",
        link: "/subcategory",
      },
      {
        title: "subcategory",
        link: "/subcategory",
      },
      {
        title: "subcategory",
        link: "/subcategory",
      },
      {
        title: "subcategory",
        link: "/subcategory",
      },
    ],
  },
  {
    title: "Cameras",
    subcategoies: [
      {
        title: "subcategory",
        link: "/subcategory",
      },
      {
        title: "subcategory",
        link: "/subcategory",
      },
      {
        title: "subcategory",
        link: "/subcategory",
      },
      {
        title: "subcategory",
        link: "/subcategory",
      },
    ],
  },
  {
    title: "Cameras",
    subcategoies: [
      {
        title: "subcategory",
        link: "/subcategory",
      },
      {
        title: "subcategory",
        link: "/subcategory",
      },
      {
        title: "subcategory",
        link: "/subcategory",
      },
      {
        title: "subcategory",
        link: "/subcategory",
      },
    ],
  },
  {
    title: "Cameras",
    subcategoies: [
      {
        title: "subcategory",
        link: "/subcategory",
      },
      {
        title: "subcategory",
        link: "/subcategory",
      },
      {
        title: "subcategory",
        link: "/subcategory",
      },
      {
        title: "subcategory",
        link: "/subcategory",
      },
    ],
  },
  {
    title: "Cameras",
    subcategoies: [
      {
        title: "subcategory",
        link: "/subcategory",
      },
      {
        title: "subcategory",
        link: "/subcategory",
      },
      {
        title: "subcategory",
        link: "/subcategory",
      },
      {
        title: "subcategory",
        link: "/subcategory",
      },
    ],
  },
  {
    title: "Cameras",
    subcategoies: [
      {
        title: "subcategory",
        link: "/subcategory",
      },
      {
        title: "subcategory",
        link: "/subcategory",
      },
      {
        title: "subcategory",
        link: "/subcategory",
      },
      {
        title: "subcategory",
        link: "/subcategory",
      },
    ],
  },
]

// footer static urls

type FooterURLType = {
  title: string
  urls: {
    title?: string
    url?: string
    icon?: React.JSX.Element
    socialmedia?: {
      url: string
      icon: React.JSX.Element
    }[]
  }[]
}

// Contact information type
type ContactInfo = {
  phone: {
    number: string
    formattedNumber: string
    icon: React.JSX.Element
  }
  email: {
    address: string
    icon: React.JSX.Element
  }
  social: {
    url: string
    icon: React.JSX.Element
    platform: string
  }[]
}

export const contactInfo: ContactInfo = {
  phone: {
    number: "+917619220543",
    formattedNumber: "+91 76192 20543",
    icon: icons.call,
  },
  email: {
    address: "<EMAIL>",
    icon: icons.mail,
  },
  social: [
    {
      platform: "Facebook",
      url: "https://www.facebook.com/Sharepal.in",
      icon: icons.facebook,
    },
    {
      platform: "Instagram",
      url: "https://www.instagram.com/sharepal.in/",
      icon: icons.instagram,
    },
    {
      platform: "LinkedIn",
      url: "https://www.linkedin.com/company/sharepal/",
      icon: icons.linkedin,
    },
  ],
}

export const footerurls: FooterURLType[] = [
  {
    title: "Sharepal",
    urls: [
      {
        title: "About",
        url: "about-us",
      },
      {
        title: "Why SharePal",
        url: "why-sharepal",
      },
      // {
      //   title: "Life at SharePal",
      //   url: "life-at-sharepal",
      // },

      {
        title: "Sitemap",
        url: "sitemap",
      },
      // {
      //   title: "CarePal",
      //   url: "carepal",
      // },
    ],
  },
  {
    title: "Information",
    urls: [
      {
        title: "How it works?",
        url: "how-sharepal-works",
      },
      {
        title: "FAQs",
        url: "faq",
      },
      {
        title: "Verification",
        url: "complete-verification",
      },
    ],
  },
  {
    title: "Policies",
    urls: [
      {
        title: "Rental Terms & Condition",
        url: "terms-and-conditions",
      },

      // {
      //   title: "Damage Policy",
      //   url: "damage-policy",
      // },
      {
        title: "Terms of Use",
        url: "terms-of-use",
      },
      {
        title: "Privacy Policy",
        url: "privacy-policy",
      },
    ],
  },
  {
    title: "Need Help",
    urls: [], // Empty array since we'll handle this section separately
  },
]
