export type BikeCouponValidityParams = {
  pickup_timestamp: number // unix seconds
  dropoff_timestamp: number // unix seconds
  total_hours: number
  total_amount: number
  coupon_active: boolean
  show_on_website: boolean
  cart_active: boolean
  cart_minimum_value: number
}

export const check_coupon_validity = ({
  pickup_timestamp,
  dropoff_timestamp,
  total_hours,
  total_amount,
  coupon_active,
  show_on_website,
  cart_active,
  cart_minimum_value,
}: BikeCouponValidityParams): boolean => {
  if (!pickup_timestamp || !dropoff_timestamp || !coupon_active) return false
  if (
    typeof pickup_timestamp !== "number" ||
    typeof dropoff_timestamp !== "number"
  ) {
    return false
  }

  // const { isValid } = validateRentalPeriod(pickup_timestamp, dropoff_timestamp)

  // Delivery coupon: must be active, shown, valid rental, minimum cart value, and minimum hours
  const is_delivery_coupon_valid =
    coupon_active &&
    show_on_website &&
    // isValid &&
    total_amount > cart_minimum_value &&
    total_hours >= 1

  // Cart coupon: must be active, cart active, minimum cart value
  const is_cart_coupon_valid =
    cart_active && coupon_active && total_amount > cart_minimum_value

  // Delivery discount: must be active, minimum cart value

  return is_delivery_coupon_valid || is_cart_coupon_valid
}
