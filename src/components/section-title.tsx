import { cn } from "@/lib/utils"
import { Typography } from "./ui/typography"

type Props = {
  nText: string
  cText: string
  className?: string
  cTColor?: string
  nTColor?: string
}

const SectionTitle = ({ cText, className, nText, cTColor, nTColor }: Props) => (
  <Typography
    as={"h1"}
    className={cn("font-ubuntu text-d7 md:max-w-full md:text-d4", className)}
  >
    <span className={`${nTColor}`}>{nText}</span>{" "}
    <span className={`${cTColor}`}>{cText}</span>
  </Typography>
)

export default SectionTitle
