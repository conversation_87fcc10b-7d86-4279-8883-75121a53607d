"use client"

import { HubSelector } from "@/components/bike-rental/hub-selector"
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON>, Card<PERSON>ontent, CardFooter, CardHeader } from "@/components/ui/card"
import { Typography } from "@/components/ui/typography"
import { getImage, moneyFormatter } from "@/functions/small-functions"
import { useBikeRentalHandlers } from "@/hooks/use-bike-rental-handlers"
import { createBikeRentalCheckoutUrl } from "@/lib/bike-rental-url-utils"
import { BUSINESS_HOURS } from "@/lib/bike-rental-utils"
import SpImage from "@/shared/SpImage/sp-image"
import { Bike } from "@/types/common"
import { calculateBikePricing } from "@/utils/bike-pricing-utils"
import { TrendingUpIcon } from "lucide-react"
import Link from "next/link"
import { useState } from "react"

interface EnhancedBikeCardProps extends Bike {
  context?: "home" | "search" | "generic"
  onBookNowClick?: () => void
  total_hours?: number
  // Hub fetching parameters (required for search context)
  pickup_time?: string
  dropoff_time?: string
  city_url?: string
}

export function EnhancedBikeCard({
  context = "search",
  onBookNowClick,
  pickup_time: _pickup_time,
  dropoff_time: _dropoff_time,
  city_url: _city_url,
  ...bike
}: EnhancedBikeCardProps) {
  const { getTimestamps, validateRental, total_hours } = useBikeRentalHandlers()
  const { name, bike_code, bike_images, price_per_hour, surge } = bike

  // Hub selection state
  const [selectedHubCode, setSelectedHubCode] = useState<string>()

  // Calculate pricing once at the top
  const pricing = calculateBikePricing({
    price_per_hour,
    total_hours: total_hours || undefined,
    surge,
  })
  const hasSurge = (surge || 1) > 1

  const renderActionButton = () => {
    if (context === "home") {
      return (
        <Button
          variant='outline-bike'
          className='md:size-lg w-full flex-1 bg-bike-100 text-b5 md:text-b4 lg:min-w-[150px]'
          onClick={onBookNowClick}
        >
          Book Now
        </Button>
      )
    }

    // For search page, show rent now button with Link
    if (context === "search" && !selectedHubCode) {
      return (
        <Button
          variant='outline-bike'
          className='md:size-lg w-full flex-1 bg-gray-100 text-b5 md:text-b4 lg:min-w-[150px]'
          disabled
        >
          Select Location
        </Button>
      )
    }

    const { pickup_timestamp, dropoff_timestamp } = getTimestamps()
    const validation = validateRental()

    if (!validation.isValid || !pickup_timestamp || !dropoff_timestamp) {
      // Check if the issue is minimum rental hours
      const hasMinimumHoursError = validation.errors.some((error) =>
        error.includes(
          `Minimum rental duration is ${BUSINESS_HOURS.MIN_RENTAL_HOURS} hour`,
        ),
      )

      const buttonText = hasMinimumHoursError
        ? `${BUSINESS_HOURS.MIN_RENTAL_HOURS}h Minimum`
        : "Invalid Times"

      return (
        <Button
          variant='outline-bike'
          size='sm'
          className='md:size-lg w-full flex-1 bg-gray-100 text-b5 md:text-b4 lg:min-w-[150px]'
          disabled
        >
          {buttonText}
        </Button>
      )
    }

    const checkoutUrl = createBikeRentalCheckoutUrl(
      pickup_timestamp,
      dropoff_timestamp,
      bike_code,
      selectedHubCode || "",
    )

    return (
      <Button
        variant='outline-bike'
        // size='sm'
        className='md:size-lg w-full flex-1 bg-bike-100 text-b5 md:text-b4 lg:min-w-[150px]'
        asChild
      >
        <Link href={checkoutUrl}>Rent Now</Link>
      </Button>
    )
  }

  return (
    <Card className='transition-duration-300 w-full rounded-2xl border-none bg-gray-100 shadow-sm transition-shadow hover:shadow-lg md:rounded-3xl'>
      <CardHeader className='flex flex-row items-center justify-between gap-3 px-3 pb-2 pt-3 text-left md:px-5 md:pb-2 md:pt-4'>
        <Typography
          as='h3'
          className='text-sh2 font-semibold text-gray-900 md:text-h4'
        >
          {name}
        </Typography>
        <Typography
          as='p'
          className='mt-1 flex items-center justify-end gap-1 text-b7 font-semibold text-primary/70 text-success-600 md:text-b6'
        >
          <TrendingUpIcon className='w-4 text-success-500 md:w-5' />
          Unlimited Kms
        </Typography>
      </CardHeader>

      <CardContent className='px-3 pb-0 pt-2 md:px-5 md:pt-2'>
        <div className='relative flex h-36 w-full items-center justify-center overflow-hidden rounded-lg bg-neutral-50 md:h-48'>
          <SpImage
            src={getImage(bike_images)}
            alt={name}
            containerClassName='w-full h-full'
            width={500}
            height={500}
            className='h-full w-full object-contain'
          />
        </div>
      </CardContent>

      <CardFooter className='flex flex-col gap-3 px-3 pb-3 pt-3 md:gap-4 md:px-5 md:pb-5 md:pt-4'>
        {/* Hub Selection - show for search context */}
        {context === "search" && (
          <HubSelector
            bike_code={bike_code}
            selectedHubCode={selectedHubCode}
            onHubSelect={setSelectedHubCode}
            className='w-full'
          />
        )}

        <div className='flex w-full flex-wrap items-end justify-between gap-3 md:gap-4 lg:gap-8'>
          <div className='flex flex-1 flex-col items-start justify-start'>
            {total_hours ? (
              <>
                <Typography
                  as='span'
                  className='text-h6 font-bold text-primary md:text-h4'
                >
                  {moneyFormatter(pricing.totalPrice)}
                </Typography>
                <Typography
                  as='span'
                  className='text-b7 font-medium text-gray-500 md:text-b6'
                >
                  @₹{pricing.effectiveHourlyRate}/hour
                  {hasSurge ? ` (${surge}x surge)` : ""}
                </Typography>
              </>
            ) : (
              <>
                <Typography
                  as='span'
                  className='text-h6 font-bold text-primary md:text-h4'
                >
                  {moneyFormatter(pricing.totalPrice)}/day
                </Typography>
                <Typography
                  as='span'
                  className='text-b7 font-medium text-gray-500 md:text-b6'
                >
                  {hasSurge ? `${surge}x surge applied` : ""}
                </Typography>
              </>
            )}
          </div>

          {renderActionButton()}
        </div>
      </CardFooter>
    </Card>
  )
}
