"use client"

import {
  <PERSON>,
  <PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON><PERSON>,
  CardTitle,
} from "@/components/ui/card"
import { getImage, moneyFormatter } from "@/functions/small-functions"
import { useBikeRentalHandlers } from "@/hooks/use-bike-rental-handlers"
import { BUSINESS_HOURS } from "@/lib/bike-rental-utils"
import SpImage from "@/shared/SpImage/sp-image"
import { useBikeRentalStore } from "@/store/bike-rental-store"
import { Bike } from "@/types/common"
import { useRouter } from "next/navigation"
import { Typography } from "../ui/typography"

export function BikeCard(bike: Bike) {
  const router = useRouter()
  const { getTimestamps, validateRental } = useBikeRentalHandlers()
  const {
    name,
    type,
    fuel_type,
    status,
    out_of_stack,
    bike_code,
    city,
    bike_images,
    bike_modal,
    price_per_hour,
    surge,
    goods_value,
  } = bike
  const { openSelector } = useBikeRentalStore()

  // const handleRentNow = () => {
  //   // Validate that pickup and dropoff times are selected
  //   const validation = validateRental()
  //   if (!validation.isValid) {
  //     toast.error("Please select pickup and dropoff times first")
  //     return
  //   }

  //   const { pickup_timestamp, dropoff_timestamp } = getTimestamps()
  //   if (!pickup_timestamp || !dropoff_timestamp) {
  //     toast.error("Please select valid pickup and dropoff times")
  //     return
  //   }

  //   // Generate checkout URL with timestamps and bike details
  //   const checkoutUrl = createBikeRentalCheckoutUrl(
  //     pickup_timestamp,
  //     dropoff_timestamp,
  //     bike_code,
  //     String(current_hub_id),
  //   )

  //   // Navigate to checkout
  //   router.push(checkoutUrl)
  // }

  return (
    <Card
      onClick={openSelector}
      className='transition-duration-300 w-full rounded-3xl border-none bg-gray-100 shadow-sm transition-shadow hover:shadow-lg'
    >
      <CardHeader className='px-5 pb-2 pt-5 text-left'>
        <CardTitle className='!text-h4 font-semibold text-gray-900'>
          {name}
        </CardTitle>
        {/* <p className='mt-1 text-xs font-medium text-primary/70'>Customizable</p> */}
      </CardHeader>
      <CardContent className='px-5 pb-0 pt-2'>
        <div className='relative flex h-48 w-full items-center justify-center overflow-hidden rounded-lg bg-neutral-50'>
          <SpImage
            src={getImage(bike_images)}
            alt={name}
            containerClassName='w-full h-full'
            width={500}
            height={500}
            className='h-full w-full object-contain'
          />
        </div>
      </CardContent>
      <CardFooter className='flex flex-col gap-4 px-5 pb-5 pt-4'>
        {/* <div className='flex items-center gap-2 text-sm text-gray-600'>
          <svg
            xmlns='http://www.w3.org/2000/svg'
            width='18'
            height='18'
            viewBox='0 0 24 24'
            fill='none'
            stroke='currentColor'
            strokeWidth='2'
            strokeLinecap='round'
            strokeLinejoin='round'
            className='text-primary'
          >
            <path d='M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z'></path>
            <circle cx='12' cy='10' r='3'></circle>
          </svg>
          <span className='font-medium'>{city}</span>
        </div> */}
        <div className='flex w-full flex-wrap items-end justify-between gap-4 lg:gap-8'>
          <div className='flex flex-1 flex-col items-start justify-start'>
            <Typography
              as='span'
              className='text-h6 font-bold text-primary md:text-h4'
            >
              {moneyFormatter(price_per_hour * BUSINESS_HOURS.MIN_RENTAL_HOURS)}
              /day
            </Typography>
            <Typography
              as='span'
              className='text-b7 font-medium text-gray-500 md:text-b6'
            >
              @{moneyFormatter(price_per_hour)}/hour
            </Typography>
          </div>
        </div>
      </CardFooter>
    </Card>
  )
}