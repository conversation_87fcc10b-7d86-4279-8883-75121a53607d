import { Typography } from "@/components/ui/typography"
import { moneyFormatter } from "@/functions/small-functions"
import SpImage from "@/shared/SpImage/sp-image"
import { Bike } from "@/types/common"
import { BikeOrder } from "@/types/order"
import { motion } from "framer-motion"

interface OrderItemCardProps {
  item: Bike
  bikeOrder: BikeOrder
  index?: number
}

export function OrderItemCard({
  item,
  bikeOrder,
  index = 1,
}: OrderItemCardProps) {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3, delay: index * 0.1 }}
      className='flex items-start gap-2 rounded-lg p-2 hover:bg-neutral-50 md:items-center md:gap-6 md:p-2 md:px-4'
    >
      <SpImage
        src={item.bike_images}
        alt={item.name}
        width={68}
        height={68}
        className='h-full w-full rounded-lg object-contain'
        containerClassName='w-[60px] flex items-center justify-center h-[60px] md:w-20 md:h-20 bg-neutral-150 rounded-lg flex-shrink-0'
      />

      <div className='flex flex-1 flex-col gap-1'>
        <Typography as={"h3"} className='text-sh6 text-neutral-900 md:text-sh4'>
          {item.name}
        </Typography>
        <Typography
          as={"p"}
          className='text-o4 capitalize text-neutral-600 md:text-b6 md:text-sm'
        >
          {item.type}
        </Typography>
      </div>

      <div className='text-right'>
        <Typography as={"p"} className={`} text-sh6 font-bold md:text-sh2`}>
          {bikeOrder?.total_rent}
        </Typography>
        <Typography as={"p"} className={`} text-b6 font-bold md:text-b4`}>
          {moneyFormatter(bikeOrder?.rent_per_hour)}/hour
        </Typography>
      </div>
    </motion.div>
  )
}
