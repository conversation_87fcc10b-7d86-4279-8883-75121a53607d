"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import { cn } from "@/lib/utils"
import { AnimatePresence, motion } from "framer-motion"
import { <PERSON>ertCircle, Loader2, RefreshCcw, Upload, X } from "lucide-react"
import * as React from "react"

interface FileUploadProps {
  label: string
  onChange: (file: File | null) => void
  value?: File | null
  progress?: number
  error?: string
  isUploading?: boolean
  onRetry?: () => void
}

export function FileUpload({
  label,
  onChange,
  value,
  progress,
  error,
  isUploading,
  onRetry,
}: FileUploadProps) {
  const inputRef = React.useRef<HTMLInputElement>(null)

  const handleClick = () => {
    inputRef.current?.click()
  }

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0] || null
    onChange(file)
  }

  const handleRemove = () => {
    onChange(null)
    if (inputRef.current) {
      inputRef.current.value = ""
    }
  }

  return (
    <div className='w-full space-y-2 overflow-hidden'>
      <input
        type='file'
        ref={inputRef}
        onChange={handleChange}
        accept='.pdf,.jpg,.jpeg,.png'
        className='hidden'
      />

      <AnimatePresence mode='wait'>
        {!value ? (
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
          >
            <Button
              type='button'
              variant='outline'
              onClick={handleClick}
              className='hover:bg-primary-50 w-full max-w-full break-words border-primary-500 text-bt2 text-primary-500'
              disabled={isUploading}
            >
              {isUploading ? (
                <Loader2 className='mr-2 h-4 w-4 animate-spin' />
              ) : (
                <Upload className='mr-2 h-4 w-4' />
              )}
              {label}
            </Button>
          </motion.div>
        ) : (
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
          >
            <Card className={error ? "border-destructive-200" : ""}>
              <CardContent className='p-3'>
                <div className='flex items-start gap-3'>
                  <div className='min-w-0 flex-1 space-y-1'>
                    <div className='flex w-full items-center justify-between'>
                      <div className='flex max-w-full items-center gap-2 whitespace-nowrap break-words'>
                        <div className='shrink-0'>
                          {isUploading ? (
                            <Loader2 className='h-5 w-5 animate-spin text-primary-500' />
                          ) : error ? (
                            <AlertCircle className='h-5 w-5 text-destructive-500' />
                          ) : (
                            <Upload className='h-5 w-5 text-primary-500' />
                          )}
                        </div>
                        <p className='max-w-48 truncate break-words text-sh5 font-medium lg:max-w-56'>
                          {value.name}
                        </p>
                      </div>
                      <Button
                        type='button'
                        variant='ghost'
                        size='icon'
                        onClick={handleRemove}
                        className='h-8 w-8 shrink-0'
                      >
                        <X className='h-4 w-4' />
                      </Button>
                    </div>

                    {typeof progress === "number" && !error && (
                      <Progress
                        value={progress}
                        // className="h-1"
                        className={cn(
                          "h-0.5",
                          progress === 100
                            ? "[&>div]:bg-success-500"
                            : "[&>div]:bg-primary-500",
                        )}
                      />
                    )}

                    {error && (
                      <div className='space-y-2'>
                        {typeof progress === "number" && (
                          <Progress
                            value={progress}
                            // className="h-1"
                            // indicatorClassName="bg-destructive-500"
                            className={cn("h-0.5 bg-destructive-500")}
                          />
                        )}
                        <div className='flex items-center gap-2 text-sm'>
                          <span className='text-destructive-500'>{error}</span>
                          <Button
                            type='button'
                            variant='link'
                            size='sm'
                            onClick={onRetry}
                            className='h-auto p-0 text-primary-500'
                          >
                            <RefreshCcw className='mr-1 h-3 w-3' />
                            Retry File Upload
                          </Button>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}
