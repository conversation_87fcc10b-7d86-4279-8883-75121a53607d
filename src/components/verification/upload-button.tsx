"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  FormControl,
  FormField,
  FormItem,
  FormMessage,
} from "@/components/ui/form"
import { Loader2 } from "lucide-react"
import { Control, UseFormSetValue } from "react-hook-form"
import { FileUpload } from "./file-upload"

interface UploadButtonProps {
  control: Control<any>
  fieldName: string
  label: string
  uploadLabel: string
  uploadProgress: number
  uploadError: string
  isUploading: boolean
  isUploadSuccess: boolean
  setValue: UseFormSetValue<any>
  onUpload: (file: File) => void
  isPending: boolean
}

export function UploadButton({
  control,
  fieldName,
  label,
  uploadLabel,
  uploadProgress,
  uploadError,
  isUploading,
  isUploadSuccess,
  setValue,
  onUpload,
  isPending,
}: UploadButtonProps) {
  return (
    <div className='space-y-3'>
      <FormField
        control={control}
        name={fieldName}
        render={({ field }) => (
          <FormItem>
            <FormControl>
              <FileUpload
                label={label}
                onChange={(file: File | null) => {
                  field.onChange(file)
                  setValue(fieldName, file)
                }}
                value={field.value}
                progress={uploadProgress}
                error={uploadError}
                isUploading={isUploading}
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      {control._getWatch(fieldName) && !isUploadSuccess && (
        <Button
          type='button'
          className='w-full bg-primary-500 hover:bg-primary-600'
          onClick={() => {
            const file = control._getWatch(fieldName)
            if (file) {
              onUpload(file)
            }
          }}
          disabled={isPending}
        >
          {isPending ? (
            <>
              <Loader2 className='mr-2 h-4 w-4 animate-spin' />
              Uploading...
            </>
          ) : (
            uploadLabel
          )}
        </Button>
      )}

      {isUploadSuccess && (
        <div className='text-center text-sm font-medium text-green-600'>
          ✓ Uploaded successfully
        </div>
      )}
    </div>
  )
}
