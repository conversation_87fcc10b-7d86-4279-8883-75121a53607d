"use client"

import { IconVerification } from "@/components/icons/common"
import { Card } from "@/components/ui/card"
import { Typography } from "@/components/ui/typography"
import SpImage from "@/shared/SpImage/sp-image"
import { useUserStore } from "@/store/user-store"
import { fadeIn } from "@/utils/animation-variants"
import { motion } from "framer-motion"

export default function VerifiedState() {
  const { user } = useUserStore()

  return (
    <Card className='relative space-y-1 border-0 bg-gray-100 p-3 md:space-y-2 md:px-16 md:py-6'>
      {/* Header */}

      <motion.div {...fadeIn} className='space-y-4'>
        <motion.div {...fadeIn} className='space-y-4 text-center'>
          <div className='flex justify-center'>
            <div className='relative p-5'>
              <div className='flex h-20 w-20 items-center justify-center rounded-full'>
                <IconVerification />
              </div>
            </div>
          </div>
          <Typography as='h1' className='text-sh5 text-gray-900 md:text-sh3'>
            Hi, {user?.first_name || "there"}!
          </Typography>
          <Typography
            as='h3'
            className='mx-auto max-w-md text-h3 text-gray-900 md:text-h1'
          >
            Enjoy Seamless Checkouts.You are now
          </Typography>
        </motion.div>
      </motion.div>
      {/* Footer */}
      <motion.div {...fadeIn} transition={{ delay: 0.5 }} className='w-full'>
        <SpImage
          src={`https://images.sharepal.in/verification-images//verified.svg`}
          alt='Verified'
          width={300}
          height={300}
          containerClassName='mx-auto'
        />
      </motion.div>
    </Card>
  )
}
