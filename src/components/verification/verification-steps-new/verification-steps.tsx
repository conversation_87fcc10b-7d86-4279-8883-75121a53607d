import Faqs from "@/components/modals/faqs-modal"
import { Card } from "@/components/ui/card"
import { trackCompleteVerificationStarted } from "@/lib/gtag-event"
import { useUserStore } from "@/store/user-store"
import { USER_VERIFICATION } from "@/types/verification"
import { motion } from "framer-motion"
import { Suspense, useCallback, useState } from "react"

import { VerificationStepsSkeleton } from "@/components/skeletons/verification-steps-skeleton"
import { FaqType } from "@/types"

import { fadeIn } from "@/utils/animation-variants"
import { isStatusNotRequested } from "@/utils/verification"
import { MainHeader } from "./utils/header-content"
import VerificationStepsToRender from "./utils/render-steps"

interface VerificationStepsProps {
  userVerification: USER_VERIFICATION | null | undefined
  faqs: FaqType[] | null | undefined
  isFaqsLoading: boolean
  setOpenAboutVerification: (open: boolean) => void
}

export interface VERIFICATION_STATUSES {
  isIdentityVerified: boolean
  isOccupationVerified: boolean
  isPanVerified: boolean
}

const VerificationSteps = ({
  setOpenAboutVerification,
  faqs,
  isFaqsLoading,
}: VerificationStepsProps) => {
  const [activeStep, setActiveStep] = useState<number | null>(null)
  const [openFaq, setOpenFaq] = useState(false)
  const { userVerification } = useUserStore()

  const { user } = useUserStore()

  const handleVerify = useCallback(
    (stepId: number, tag: string) => {
      const stepStatusMap = {
        1: userVerification?.identity_status,
        3: userVerification?.occupation_status,
        4: userVerification?.credit_status,
      }

      if (
        stepId === 1 &&
        isStatusNotRequested(userVerification?.identity_status || "") &&
        isStatusNotRequested(userVerification?.dl_status || "")
      ) {
        return
      }

      if (stepId === 2) {
        if (
          isStatusNotRequested(userVerification?.occupation_status || "") &&
          isStatusNotRequested(userVerification?.credit_status || "")
        ) {
          return
        } else {
          setActiveStep((prev) => (prev === stepId ? null : stepId))
          return
        }
      }

      // Check if step is in requested status
      if (
        stepId !== 1 &&
        isStatusNotRequested(
          stepStatusMap[stepId as keyof typeof stepStatusMap] || "",
        )
      ) {
        return
      }

      // Toggle active step
      setActiveStep((prev) => (prev === stepId ? null : stepId))

      // Track event if user exists
      if (user) {
        trackCompleteVerificationStarted(user, tag)
      }
    },
    [user, userVerification],
  )

  const unableToVerify = useCallback(() => {
    window.open(
      "https://api.whatsapp.com/send?phone=+************&text=Hi, I am unable to verify my account. Can you help me with the process?",
      "_blank",
    )
  }, [])

  if (!userVerification) {
    return <VerificationStepsSkeleton />
  }

  return (
    <Suspense fallback={<VerificationStepsSkeleton />}>
      <Card className='relative space-y-4 border-0 bg-gray-100 p-4 md:space-y-8 md:px-12 md:py-8'>
        {/* Header */}
        <motion.div {...fadeIn} className='mb-6 w-full md:mb-3'>
          <MainHeader
            // userVerification={userVerification}
            user={user}
            setOpenFaq={setOpenFaq}
            isFaqsLoading={isFaqsLoading}
            setOpenAboutVerification={setOpenAboutVerification}
          />
        </motion.div>

        {/* Steps */}
        <motion.div
          {...fadeIn}
          transition={{ delay: 0.2 }}
          className='space-y-6'
        >
          <VerificationStepsToRender
            // userVerification={userVerification}
            activeStep={activeStep}
            setActiveStep={setActiveStep}
            handleVerify={handleVerify}
            unableToVerify={unableToVerify}
          />
        </motion.div>
      </Card>

      {/* FAQs Modal */}
      {!isFaqsLoading && (
        <Faqs
          openSideView={openFaq}
          setOpenSideView={setOpenFaq}
          faqs={faqs || []}
        />
      )}
    </Suspense>
  )
}

export default VerificationSteps
