"use client"

import { motion } from "framer-motion"

import { useQuery } from "@tanstack/react-query"

import { getUserVerification } from "@/services/user"

import { fetchFaqs } from "@/services/common"
import { useUserStore } from "@/store/user-store"
import VerificationSteps from "./verification-steps"

interface VerificationStepsNewProps {
  setOpenAboutVerification: (open: boolean) => void
}

export default function VerificationStepsNew({
  setOpenAboutVerification,
}: VerificationStepsNewProps) {
  const { setUserVerification } = useUserStore()

  const { data: userVerification } = useQuery({
    queryKey: ["user_verification"],
    queryFn: async () => {
      const data = await getUserVerification()
      setUserVerification(data)
      return data
    },
    refetchOnWindowFocus: true,
    refetchOnMount: true,
    refetchInterval: 0, // Disable automatic refetching
    staleTime: 0, // Consider data immediately stale
  })

  // useEffect(() => {
  //   setUserVerification(userVerification)
  // }, [userVerification])

  const { data: faqs, isLoading: isFaqsLoading } = useQuery({
    queryKey: ["fetch/faqs"],
    queryFn: async () => await fetchFaqs("verification", "", "Verification"),
    staleTime: 1000 * 60 * 5, // Cache for 5 minutes
  })

  return (
    <motion.div initial='initial' animate='animate' exit='exit'>
      {/* {isAllVerified(userVerification) ? (
        <VerifiedState />
      ) : ( */}
      <VerificationSteps
        setOpenAboutVerification={setOpenAboutVerification}
        userVerification={userVerification}
        faqs={faqs}
        isFaqsLoading={isFaqsLoading}
      />
    </motion.div>
  )
}
