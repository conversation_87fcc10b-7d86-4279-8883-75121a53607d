import { Badge } from "@/components/ui/badge"
import { Card, CardContent } from "@/components/ui/card"
import { FormControl, FormField, FormItem } from "@/components/ui/form"
import { Label } from "@/components/ui/label"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Typography } from "@/components/ui/typography"
import { AnimatePresence, motion } from "framer-motion"
import { FieldPath, FieldValues, UseFormReturn } from "react-hook-form"

interface VerificationTypeSelectorProps<T extends FieldValues> {
  form: UseFormReturn<T>
  name: FieldPath<T>
  onlineLabel: string
  offlineLabel: string
  onlineContent: React.ReactNode
  offlineContent: React.ReactNode
}

export function VerificationTypeSelector<T extends FieldValues>({
  form,
  name,
  onlineLabel,
  offlineLabel,
  onlineContent,
  offlineContent,
}: VerificationTypeSelectorProps<T>) {
  return (
    <FormField
      control={form.control}
      name={name}
      render={({ field }) => (
        <FormItem>
          <FormControl>
            <Card className='border-0 shadow-none'>
              <CardContent className='p-0'>
                <RadioGroup
                  onValueChange={field.onChange}
                  value={field.value}
                  className='space-y-4'
                >
                  {/* Online Option */}
                  <div className='rounded-lg bg-neutral-150'>
                    <div className='flex items-center space-x-2 p-4'>
                      <RadioGroupItem value='online' id='online' />
                      <Label
                        htmlFor='online'
                        className='flex items-center gap-2'
                      >
                        <Typography as='p' className='text-center text-sh5'>
                          {onlineLabel}
                        </Typography>
                        <Badge
                          variant='secondary'
                          className='ml-2 min-w-max rounded-full bg-primary-100 text-b6 text-primary-500 hover:bg-primary-100'
                        >
                          Safer & Faster
                        </Badge>
                      </Label>
                    </div>

                    <AnimatePresence>
                      {field.value === "online" && (
                        <motion.div
                          key='online'
                          initial={{ height: 0, opacity: 0 }}
                          animate={{ height: "auto", opacity: 1 }}
                          exit={{ height: 0, opacity: 0 }}
                          transition={{ duration: 0.2 }}
                          className='overflow-hidden'
                        >
                          <div className='ml-2 space-y-4 p-4'>
                            {onlineContent}
                          </div>
                        </motion.div>
                      )}
                    </AnimatePresence>
                  </div>

                  {/* Offline Option */}
                  <div className='rounded-lg bg-neutral-150'>
                    <div className='flex items-center space-x-2 p-4'>
                      <RadioGroupItem value='offline' id='offline' />
                      <Label htmlFor='offline' className='flex'>
                        <Typography as='p' className='text-sh5'>
                          {offlineLabel}
                        </Typography>
                      </Label>
                    </div>

                    <AnimatePresence>
                      {field.value === "offline" && (
                        <motion.div
                          key='offline'
                          initial={{ height: 0, opacity: 0 }}
                          animate={{ height: "auto", opacity: 1 }}
                          exit={{ height: 0, opacity: 0 }}
                          transition={{ duration: 0.2 }}
                          className='overflow-hidden'
                        >
                          <div className='ml-2 space-y-4 p-4'>
                            {offlineContent}
                          </div>
                        </motion.div>
                      )}
                    </AnimatePresence>
                  </div>
                </RadioGroup>
              </CardContent>
            </Card>
          </FormControl>
        </FormItem>
      )}
    />
  )
}
