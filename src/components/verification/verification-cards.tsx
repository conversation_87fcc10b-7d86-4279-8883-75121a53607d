import {
  <PERSON>,
  CardContent,
  Card<PERSON>escription,
  <PERSON><PERSON><PERSON><PERSON>,
  CardTitle,
} from "@/components/ui/card"
import SpImage from "@/shared/SpImage/sp-image"
import { ClipboardCheck, Clock, Lock, LucideIcon, Shield } from "lucide-react"
import { AdaptiveWrapper } from "../common/adaptive-wrapper"

// interface FeatureItemProps {
//   icon: LucideIcon
//   title: string
//   description: string
//   iconBgColor?: string
//   iconColor?: string
// }

// const FeatureItem = ({
//   icon: Icon,
//   title,
//   description,
//   iconBgColor = "bg-purple-100",
//   iconColor = "text-purple-600",
// }: FeatureItemProps) => (
//   <div className='flex items-center gap-4 rounded-xl bg-gray-100 p-4'>
//     <div
//       className={`flex h-12 w-12 items-center justify-center rounded-full ${iconBgColor}`}
//     >
//       <Icon className={`h-6 w-6 ${iconColor}`} />
//     </div>
//     <div>
//       <p className='font-medium text-neutral-800'>{title}</p>
//       <p className='text-sm text-neutral-500'>{description}</p>
//     </div>
//   </div>
// )

interface VerificationCardProps {
  title: string
  description: React.ReactNode
  icon: LucideIcon
  iconBgColor: string
  iconColor: string
  gradientColor: string
  image: string
  children: React.ReactNode
}

const VerificationCard = ({
  title,
  description,
  // icon: Icon,
  // iconBgColor,
  // iconColor,
  // gradientColor,
  image,
  children,
}: VerificationCardProps) => (
  <Card
    className={`group overflow-hidden rounded-2xl border border-primary-100 from-white to-primary-100 transition-all duration-300 max-md:max-w-max max-md:p-0 md:rounded-3xl md:bg-gradient-to-b`}
  >
    <CardHeader className='flex-row-reverse items-start gap-2 max-md:p-4 max-md:px-3 md:flex-row md:gap-6'>
      <div className='flex-1 space-y-2 md:space-y-3'>
        <CardTitle className='!text-sh2 text-primary-900 max-md:p-0 md:!text-h2'>
          {title}
        </CardTitle>
        <CardDescription className='!text-b6 text-neutral-600 max-md:p-0 md:!text-b2'>
          {description}
        </CardDescription>
      </div>
      <div className='relative flex h-14 w-14 flex-shrink-0 items-center justify-center'>
        <SpImage
          src={image}
          alt='Verification Card'
          width={112}
          className='aspect-square md:h-[112px] md:w-[112px]'
          height={112}
        />
      </div>
    </CardHeader>
    <CardContent className='space-y-4 max-md:p-0'>{children}</CardContent>
  </Card>
)

const verificationCards = [
  {
    title: "Why Verify?",
    description: (
      <>
        Verification keeps rentals{" "}
        <span className='!text-b6 !font-bold text-neutral-500 md:!text-b2'>
          safe and fraud-free
        </span>{" "}
        by confirming identity and ensuring secure transactions.
      </>
    ),
    icon: Shield,
    iconBgColor: "bg-green-100",
    iconColor: "text-green-600",
    gradientColor: "to-primary-100",
    image: "https://images.sharepal.in/verification-images//image 3.svg",
    content: (
      // <div className='mx-auto flex max-w-[432px] items-center gap-2 rounded-lg bg-neutral-150 p-2 transition-colors max-md:hidden md:gap-4 md:p-2'>
      //   <div className='relative flex aspect-video h-[102px] w-[297px] items-center justify-center overflow-hidden rounded-lg bg-gray-100'>
      //     <PlayCircleFilledIcon className='absolute h-8 w-8 text-category-red' />
      //   </div>
      //   <div className='flex gap-0.5'>
      //     <Typography as={"h3"} className='!text-sh6 text-gray-900 md:!text-h7'>
      //       Watch video to know more about our verification process{" "}
      //     </Typography>{" "}
      //     <PlayCircleFilledIcon className='h-4 w-4 self-start text-neutral-300 md:!min-h-8 md:!min-w-8' />
      //   </div>
      // </div>
      <></>
    ),
  },
  {
    title: "Your Privacy & Data Security",
    description: (
      <>
        Your details are used{" "}
        <span className='!text-b6 !font-bold text-neutral-500 md:!text-b2'>
          only
        </span>{" "}
        for verification and{" "}
        <span className='!text-b6 !font-bold text-neutral-500 md:!text-b2'>
          never shared or sold
        </span>
        . We employ bank-grade encryption to protect your information.
      </>
    ),
    icon: Lock,
    iconBgColor: "bg-blue-100",
    iconColor: "text-blue-600",
    gradientColor: "to-blue-50",
    image: "https://images.sharepal.in/verification-images//image 4.svg",
    // content: (
    //   <FeatureItem
    //     icon={Shield}
    //     title='Secure Storage'
    //     description='End-to-end encrypted data protection'
    //   />
    // ),
  },
  {
    title: "How long does it take?",
    description:
      "Our streamlined verification process is quick and efficient, designed to get you renting as soon as possible.",
    icon: Clock,
    iconBgColor: "bg-orange-100",
    iconColor: "text-orange-600",
    gradientColor: "to-orange-50",
    image: "https://images.sharepal.in/verification-images//image 5.svg",
    // content: (
    //   <>
    //     <FeatureItem
    //       icon={CheckCircle}
    //       title='Standard Orders'
    //       description='Processed within 24-48 hours'
    //       iconBgColor='bg-green-100'
    //       iconColor='text-green-600'
    //     />
    //     <FeatureItem
    //       icon={Play}
    //       title='Same-Day Delivery'
    //       description='30 minutes to 1 hour with proper documents'
    //       iconBgColor='bg-blue-100'
    //       iconColor='text-blue-600'
    //     />
    //   </>
    // ),
  },
  {
    title: "You Only Verify Once!",
    description:
      "Complete verification once and enjoy quick, hassle-free rentals every time you return.",
    icon: ClipboardCheck,
    iconBgColor: "bg-green-100",
    iconColor: "text-green-600",
    gradientColor: "to-green-50",
    image: "https://images.sharepal.in/verification-images//image 6.svg",
    // content: (
    //   <>
    //     <FeatureItem
    //       icon={Lock}
    //       title='One-time Process'
    //       description='Verify once, rent multiple times'
    //     />
    //     <FeatureItem
    //       icon={ExternalLink}
    //       title='Quick Access'
    //       description='Faster checkouts on future rentals'
    //       iconBgColor='bg-orange-100'
    //       iconColor='text-orange-600'
    //     />
    //   </>
    // ),
  },
]

interface VerificationCardsProps {
  openAboutVerification: boolean
  setOpenAboutVerification: (open: boolean) => void
}

export default function VerificationCards({
  openAboutVerification,
  setOpenAboutVerification,
}: VerificationCardsProps) {
  // const [openAboutVerification, setOpenAboutVerification] = useState(false)

  return (
    <div className=''>
      <div className='w-full space-y-6 p-4 max-md:hidden'>
        {verificationCards.map((card, index) => (
          <VerificationCard key={index} {...card}>
            {card.content}
          </VerificationCard>
        ))}
      </div>

      <AdaptiveWrapper
        title='About Verification'
        className='md:hidden'
        open={openAboutVerification}
        onOpenChange={setOpenAboutVerification}
      >
        <div className='max-w-screen flex min-h-[80vh] flex-col gap-4 p-4'>
          {verificationCards.map((card, index) => (
            <VerificationCard key={index} {...card}>
              {card.content}
            </VerificationCard>
          ))}
        </div>
      </AdaptiveWrapper>
    </div>
  )
}
