import { Loader2 } from "lucide-react"

import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Label } from "@/components/ui/label"
import { RadioGroupItem } from "@/components/ui/radio-group"
import { AnimatePresence, motion } from "framer-motion"

import { fetchWithAuthPost } from "@/utils/fetchWithAuth"
import { useMutation } from "@tanstack/react-query"
import { toast } from "sonner"

import { Typography } from "@/components/ui/typography"
// import { useThrottle } from "@/hooks/use-throttle"
import { useUserStore } from "@/store/user-store"
import { ZoopFailureResponse, ZoopInitResponse } from "@/types/zoop-digilocker"

type Props = {
  isRetry?: boolean
  retryType?: "govid" | "dl"
  selectedValue?: string
}

const DigilockerVerification = ({
  isRetry,
  retryType,
  selectedValue,
}: Props) => {
  const { user } = useUserStore()

  const digiLockerType = isRetry
    ? retryType === "govid"
      ? ["ADHAR"]
      : ["DRVLC"]
    : ["ADHAR", "DRVLC"]
  // DigiLocker verification
  const {
    mutate: handleDigiLockerVerification,
    isPending: isDigilockerPending,
  } = useMutation({
    mutationFn: async (): Promise<ZoopInitResponse | ZoopFailureResponse> => {
      if (!user) {
        throw new Error("User not authenticated")
      }
      const response = (await fetchWithAuthPost(
        "https://api.sharepal.in/api:YeisLDqw/digilocker/init/v1",
        {
          type: digiLockerType,
        },
      )) as ZoopInitResponse
      return response
    },
    onSuccess: (response) => {
      if (response?.success) {
        window.open(response.sdk_url, "_self", "noopener,noreferrer")
      } else {
        toast.error("Failed to initialize DigiLocker")
      }
    },
    onError: (error) => {
      console.error("DigiLocker verification error:", error)
      toast.error(
        error instanceof Error
          ? error.message
          : "Failed to initialize DigiLocker",
      )
    },
  })
  return (
    <Card className='border-0 shadow-none'>
      <CardContent className='p-0'>
        <div className='rounded-lg bg-neutral-150'>
          <div className='mt-4 flex items-center space-x-2 p-4'>
            <RadioGroupItem value='digilocker' id='digilocker' />
            <Label htmlFor='digilocker' className='flex'>
              <Typography as={"p"} className='text-sh5'>
                {!isRetry ? "Verify with DigiLocker" : "Retry with DigiLocker"}
              </Typography>
              <Badge
                variant='secondary'
                className='ml-2 min-w-max rounded-full bg-primary-100 text-b6 text-primary-500 hover:bg-primary-100'
              >
                Recommended
              </Badge>
            </Label>
          </div>

          <AnimatePresence>
            {selectedValue === "digilocker" && (
              <motion.div
                key='digilocker'
                initial={{ height: 0, opacity: 0 }}
                animate={{ height: "auto", opacity: 1 }}
                exit={{ height: 0, opacity: 0 }}
                transition={{ duration: 0.2 }}
                className='overflow-hidden'
              >
                <div className='ml-2 space-y-4 p-4'>
                  <Button
                    type='button'
                    className='w-full bg-primary-500 hover:bg-primary-600'
                    onClick={() => handleDigiLockerVerification()}
                    disabled={isDigilockerPending}
                  >
                    {isDigilockerPending ? (
                      <>
                        <Loader2 className='mr-2 h-4 w-4 animate-spin' />
                        Initializing DigiLocker...
                      </>
                    ) : isRetry ? (
                      "Retry with DigiLocker"
                    ) : (
                      "Verify with DigiLocker"
                    )}
                  </Button>
                  <Typography as={"p"} className='text-bt3'>
                    {isRetry
                      ? retryType === "dl"
                        ? "Try verifying Driving License using DigiLocker."
                        : retryType === "govid"
                          ? "Try verifying Goverment Id  using DigiLocker."
                          : "Try verifying your reamaining Documents using Digilocker"
                      : ` This will verify your identity documents through DigiLocker. Please complete the verification in the new tab.`}
                  </Typography>
                </div>
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      </CardContent>
    </Card>
  )
}

export default DigilockerVerification
