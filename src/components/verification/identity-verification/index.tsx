"use client"

import { zod<PERSON><PERSON>olver } from "@hookform/resolvers/zod"
import { Loader2 } from "lucide-react"
import {
  useEffect,
  // useMemo,
  useState,
} from "react"
import { useForm } from "react-hook-form"

import { Button } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Form, FormControl, FormField, FormItem } from "@/components/ui/form"
import { Label } from "@/components/ui/label"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { FILE_TYPE, useMultiFileUpload } from "@/hooks/use-file-upload"
import { AnimatePresence, motion } from "framer-motion"
import { DocumentUpload } from "./document-upload"
import {
  identityVerificationSchema,
  type IdentityVerificationForm,
} from "./schema"

import { useUserStore } from "@/store/user-store"
import { fetchWithAuthPost } from "@/utils/fetchWithAuth"
import { useQueryClient } from "@tanstack/react-query"
import { toast } from "sonner"

import { Typography } from "@/components/ui/typography"
// import { useThrottle } from "@/hooks/use-throttle"
import { VERIFICATION_STATUS } from "@/types/verification"
import { isStatusNotRequested, isStatusRequested } from "@/utils/verification"
import { useRouter, useSearchParams } from "next/navigation"
import { WhatsAppLogoFilledIcon } from "sharepal-icons"
import VerificationReceivedModal from "../verification-received"
import DigilockerVerification from "./digilocker"
import SubmissionStatus from "./submission-status"

interface IdentityVerificationDialogProps {
  isOpen: boolean
  onClose: (value: boolean) => void
  unableToVerify: () => void
}

export function IdentityVerification({
  onClose,
  unableToVerify,
}: IdentityVerificationDialogProps) {
  const [open, setOpen] = useState(false)
  const { userVerification } = useUserStore()
  const queryClient = useQueryClient()
  const router = useRouter()
  const { user, setUser } = useUserStore()

  const params = useSearchParams()
  const success = params?.get("success")
  const requestId = params.get("id")
  const errCode = params?.get("errCode")

  // Check verification status - only show submitted, never verified in UI
  const isGovIdSubmitted = isStatusNotRequested(
    userVerification?.identity_status ?? VERIFICATION_STATUS.Requested,
  )
  const isDlSubmitted = isStatusNotRequested(
    userVerification?.dl_status ?? VERIFICATION_STATUS.Requested,
  )

  const form = useForm<IdentityVerificationForm>({
    resolver: zodResolver(identityVerificationSchema),
    defaultValues: {
      verificationType: "digilocker",
      dlFrontFile: null,
      dlBackFile: null,
      govIdFrontFile: null,
      govIdBackFile: null,
    },
    mode: "onChange",
  })

  // const verificationType = form.watch("verificationType")

  // Handle DigiLocker redirect
  useEffect(() => {
    const getAadharDetailsUsingRequestId = async () => {
      try {
        const data = await fetchWithAuthPost(
          "https://api.sharepal.in/api:YeisLDqw/digilocker_init",
          { request_id: requestId },
        )

        if (data) {
          queryClient.invalidateQueries({ queryKey: ["user_verification"] })
          toast.success("Aadhaar Verified Successfully!")
        }
      } catch {
        toast.error("Failed to get Aadhar details")
      } finally {
        router.replace("/complete-verification")
      }
    }

    if (success && success === "True" && requestId) {
      getAadharDetailsUsingRequestId()
    } else if (success && success === "False" && errCode) {
      toast.error("Please Verify your Aadhaar through Digilocker!")
      router.replace("/complete-verification")
    } else if (success && success === "False" && !errCode) {
      toast.error("Aadhar Verification Failed")
      router.replace("/complete-verification")
    }
  }, [success, requestId, errCode, router, queryClient])

  // Multi-file upload hooks
  const dlUpload = useMultiFileUpload({
    fileType: FILE_TYPE.DL,
    onSuccess: (res: any) => {
      queryClient.invalidateQueries({ queryKey: ["user_verification"] })
      toast.success("Driving License uploaded successfully")
      setOpen(true)
    },
  })

  const govIdUpload = useMultiFileUpload({
    fileType: FILE_TYPE.IDENTITY,
    onSuccess: (res: any) => {
      queryClient.invalidateQueries({ queryKey: ["user_verification"] })
      toast.success("Government ID uploaded successfully")
      setOpen(true)
    },
  })

  // Upload function for both DL and Government ID
  const uploadIdentity = () => {
    const dlFront = form.getValues("dlFrontFile")
    const dlBack = form.getValues("dlBackFile")
    const govIdFront = form.getValues("govIdFrontFile")
    const govIdBack = form.getValues("govIdBackFile")

    // Check if both DL files are provided
    const hasDlFiles = dlFront && dlBack
    // Check if both Government ID files are provided
    const hasGovIdFiles = govIdFront && govIdBack

    if (!hasDlFiles && !hasGovIdFiles) {
      toast.error(
        "Please upload either Driving License or Government ID documents",
      )
      return
    }

    // Upload DL if files are provided
    if (hasDlFiles) {
      dlUpload.uploadFiles({ frontFile: dlFront, backFile: dlBack })
    }

    // Upload Government ID if files are provided
    if (hasGovIdFiles) {
      govIdUpload.uploadFiles({ frontFile: govIdFront, backFile: govIdBack })
    }
  }

  return (
    <>
      <VerificationReceivedModal
        onClose={onClose}
        messages={{
          buttonText: isStatusRequested(
            userVerification?.occupation_status ?? "",
          )
            ? "Verify Occupation"
            : isStatusRequested(userVerification?.credit_status ?? "")
              ? "Verify Pan"
              : "Continue",
        }}
        open={open}
        setOpen={setOpen}
      />

      <Form {...form}>
        <div className='min-h-[60vh] space-y-6 max-md:p-4 md:min-h-max'>
          <FormField
            control={form.control}
            name='verificationType'
            render={({ field }) => (
              <FormItem>
                <FormControl>
                  <RadioGroup
                    onValueChange={field.onChange}
                    value={field.value}
                    className='space-y-4'
                  >
                    {/* DigiLocker Option - Only show when nothing is submitted */}
                    {!isDlSubmitted && !isGovIdSubmitted && (
                      <DigilockerVerification selectedValue={field.value} />
                    )}

                    {/* Manual Verification Option - Show when nothing is submitted */}
                    {!isDlSubmitted && !isGovIdSubmitted && (
                      <Card className='border-0 shadow-none'>
                        <CardContent className='p-0'>
                          <div className='rounded-lg bg-neutral-150'>
                            <div className='mt-4 flex items-center space-x-2 p-4'>
                              <RadioGroupItem value='offline' id='manual' />
                              <Label htmlFor='manual' className='flex'>
                                <Typography as={"p"} className='text-sh5'>
                                  Verify Manually via File Upload
                                </Typography>
                              </Label>
                            </div>

                            <AnimatePresence>
                              {field.value === "offline" && (
                                <motion.div
                                  key='manual'
                                  initial={{ height: 0, opacity: 0 }}
                                  animate={{ height: "auto", opacity: 1 }}
                                  exit={{ height: 0, opacity: 0 }}
                                  transition={{ duration: 0.2 }}
                                  className='overflow-hidden'
                                >
                                  <div className='ml-2 space-y-6 p-4'>
                                    {/* Driving License Section */}
                                    <div className='space-y-4'>
                                      <Typography
                                        as='h4'
                                        className='text-sh5 font-medium'
                                      >
                                        Driving License
                                      </Typography>
                                      <DocumentUpload
                                        form={form}
                                        documentType='dl'
                                        title=''
                                      />
                                    </div>

                                    {/* Government ID Section */}
                                    <div className='space-y-4'>
                                      <Typography
                                        as='h4'
                                        className='text-sh5 font-medium'
                                      >
                                        Government ID
                                      </Typography>
                                      <DocumentUpload
                                        form={form}
                                        documentType='govId'
                                        title=''
                                      />
                                    </div>

                                    <Button
                                      type='button'
                                      variant={"primary"}
                                      onClick={uploadIdentity}
                                      disabled={
                                        dlUpload.isPending ||
                                        govIdUpload.isPending
                                      }
                                      className='w-full'
                                    >
                                      {dlUpload.isPending ||
                                      govIdUpload.isPending ? (
                                        <>
                                          <Loader2 className='mr-2 h-4 w-4 animate-spin' />
                                          Uploading...
                                        </>
                                      ) : (
                                        "Verify Identity"
                                      )}
                                    </Button>
                                  </div>
                                </motion.div>
                              )}
                            </AnimatePresence>
                          </div>
                        </CardContent>
                      </Card>
                    )}

                    {/* Show separate DL option when Government ID is submitted but DL is not */}
                    {isGovIdSubmitted && !isDlSubmitted && (
                      <>
                        {/* Show Government ID submitted status */}
                        <SubmissionStatus title='Government Id' submitted />

                        <SubmissionStatus
                          title='Driving License'
                          submitted={false}
                          description=' Could not verify your driving license. Please upload your driving license on DigiLocker and retry or verify manually below.'
                        />

                        {/* Retry with DigiLocker option */}
                        <DigilockerVerification
                          isRetry
                          retryType='dl'
                          selectedValue={field.value}
                        />

                        <span className='w-full text-center text-b4 font-bold'>
                          OR
                        </span>

                        {/* Upload Driving License option */}
                        <Card className='border-0 shadow-none'>
                          <CardContent className='p-0'>
                            <div className='rounded-lg bg-neutral-150'>
                              <div className='mt-4 flex items-center space-x-2 p-4'>
                                <RadioGroupItem
                                  value='upload-dl'
                                  id='upload-dl'
                                />
                                <Label htmlFor='upload-dl' className='flex'>
                                  <Typography as={"p"} className='text-sh5'>
                                    Upload Driving License Manually
                                  </Typography>
                                </Label>
                              </div>

                              <AnimatePresence>
                                {field.value === "upload-dl" && (
                                  <motion.div
                                    key='upload-dl'
                                    initial={{ height: 0, opacity: 0 }}
                                    animate={{ height: "auto", opacity: 1 }}
                                    exit={{ height: 0, opacity: 0 }}
                                    transition={{ duration: 0.2 }}
                                    className='overflow-hidden'
                                  >
                                    <div className='ml-2 space-y-4 p-4'>
                                      <DocumentUpload
                                        form={form}
                                        documentType='dl'
                                        title=''
                                      />
                                      <Button
                                        type='button'
                                        variant={"primary"}
                                        onClick={() => {
                                          const dlFront =
                                            form.getValues("dlFrontFile")
                                          const dlBack =
                                            form.getValues("dlBackFile")
                                          if (dlFront && dlBack) {
                                            dlUpload.uploadFiles({
                                              frontFile: dlFront,
                                              backFile: dlBack,
                                            })
                                          } else {
                                            toast.error(
                                              "Please upload both front and back of your Driving License",
                                            )
                                          }
                                        }}
                                        disabled={dlUpload.isPending}
                                        className='w-full'
                                      >
                                        {dlUpload.isPending ? (
                                          <>
                                            <Loader2 className='mr-2 h-4 w-4 animate-spin' />
                                            Uploading...
                                          </>
                                        ) : (
                                          "Upload Driving License"
                                        )}
                                      </Button>
                                    </div>
                                  </motion.div>
                                )}
                              </AnimatePresence>
                            </div>
                          </CardContent>
                        </Card>
                      </>
                    )}

                    {/* Show separate Government ID option when DL is submitted but Government ID is not */}
                    {isDlSubmitted && !isGovIdSubmitted && (
                      <>
                        {/* Show Driving License submitted status */}
                        <SubmissionStatus title='Driving License' submitted />

                        <SubmissionStatus
                          title='Government Id'
                          submitted={false}
                          description=' Could not verify your Aadhar. Please upload your Aadhar on DigiLocker and retry or verify manually below.'
                        />

                        {/* Retry with DigiLocker option for Government ID */}
                        <DigilockerVerification
                          isRetry
                          retryType='govid'
                          selectedValue={field.value}
                        />

                        <span className='w-full text-center text-b4 font-bold'>
                          OR
                        </span>
                        {/* Upload Government ID option */}
                        <Card className='border-0 shadow-none'>
                          <CardContent className='p-0'>
                            <div className='rounded-lg bg-neutral-150'>
                              <div className='mt-4 flex items-center space-x-2 p-4'>
                                <RadioGroupItem
                                  value='upload-govid'
                                  id='upload-govid'
                                />
                                <Label htmlFor='upload-govid' className='flex'>
                                  <Typography as={"p"} className='text-sh5'>
                                    Upload Government ID Manually
                                  </Typography>
                                </Label>
                              </div>

                              <AnimatePresence>
                                {field.value === "upload-govid" && (
                                  <motion.div
                                    key='upload-govid'
                                    initial={{ height: 0, opacity: 0 }}
                                    animate={{ height: "auto", opacity: 1 }}
                                    exit={{ height: 0, opacity: 0 }}
                                    transition={{ duration: 0.2 }}
                                    className='overflow-hidden'
                                  >
                                    <div className='ml-2 space-y-4 p-4'>
                                      <DocumentUpload
                                        form={form}
                                        documentType='govId'
                                        title=''
                                      />
                                      <Button
                                        type='button'
                                        variant={"primary"}
                                        onClick={() => {
                                          const govIdFront =
                                            form.getValues("govIdFrontFile")
                                          const govIdBack =
                                            form.getValues("govIdBackFile")
                                          if (govIdFront && govIdBack) {
                                            govIdUpload.uploadFiles({
                                              frontFile: govIdFront,
                                              backFile: govIdBack,
                                            })
                                          } else {
                                            toast.error(
                                              "Please upload both front and back of your Government ID",
                                            )
                                          }
                                        }}
                                        disabled={govIdUpload.isPending}
                                        className='w-full'
                                      >
                                        {govIdUpload.isPending ? (
                                          <>
                                            <Loader2 className='mr-2 h-4 w-4 animate-spin' />
                                            Uploading...
                                          </>
                                        ) : (
                                          "Upload Government ID"
                                        )}
                                      </Button>
                                    </div>
                                  </motion.div>
                                )}
                              </AnimatePresence>
                            </div>
                          </CardContent>
                        </Card>
                      </>
                    )}

                    {/* Show status when both are submitted */}
                    {isDlSubmitted && isGovIdSubmitted && (
                      <>
                        {/* Driving License submitted status */}
                        <SubmissionStatus title='Driving License' submitted />

                        {/* Government ID submitted status */}
                        <SubmissionStatus title='Government Id' submitted />

                        {/* Completion message */}
                        <Card className='border-0 shadow-none'>
                          <CardContent className='p-6'>
                            <div className='space-y-2 text-center'>
                              <Typography
                                as='p'
                                className='text-bt3 text-neutral-600'
                              >
                                Your documents are under review. You&apos;ll be
                                notified once they&apos;re processed.
                              </Typography>
                            </div>
                          </CardContent>
                        </Card>
                      </>
                    )}
                  </RadioGroup>
                </FormControl>
              </FormItem>
            )}
          />
        </div>

        {/* Help Button */}
        <div className='max-sm:sticky max-sm:bottom-0 max-sm:w-full max-sm:bg-gray-100 max-sm:p-4 max-sm:shadow-sm md:space-y-3'>
          <Button
            type='button'
            variant='link'
            className='w-full text-center text-primary-500'
            onClick={unableToVerify}
          >
            <WhatsAppLogoFilledIcon /> Need help?
          </Button>
        </div>
      </Form>
    </>
  )
}
