import { Badge } from "@/components/ui/badge"
import { Card, CardContent } from "@/components/ui/card"
import { Typography } from "@/components/ui/typography"
import { cn } from "@/lib/utils"
import { InfoIcon } from "lucide-react"

type SubmissionStatusProps = {
  title: string
  submitted: boolean
  description?: string
}

const SubmissionStatus = ({
  title,
  submitted,
  description,
}: SubmissionStatusProps) => {
  return (
    <Card className={cn("rounded-lg border-0 shadow-none")}>
      <CardContent className='p-0'>
        <div
          className={cn(
            "rounded-lg bg-neutral-150 p-4",
            submitted ? "!bg-secondary-250" : "bg-destructive-200",
          )}
        >
          <div className='flex items-center justify-between'>
            <Typography as={"p"} className={cn("text-sh5")}>
              {title}
            </Typography>
            <Badge
              className={cn(
                "text-primary-900",
                submitted
                  ? "bg-secondary-600 hover:bg-secondary-600"
                  : "bg-destructive-500 text-gray-100 hover:bg-destructive-400",
              )}
            >
              {submitted ? "Submitted" : "Not Submitted"}
            </Badge>
          </div>
        </div>
        {description && (
          <p
            className={cn(
              "flex items-center gap-2 p-2 text-b6 text-xs",
              submitted ? "text-secondary-850" : "text-destructive-700",
            )}
          >
            <InfoIcon className='size-3 min-w-3 text-inherit' />
            {description}
          </p>
        )}
      </CardContent>
    </Card>
  )
}

export default SubmissionStatus
