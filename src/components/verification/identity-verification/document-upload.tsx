import {
  FormControl,
  FormField,
  FormItem,
  FormMessage,
} from "@/components/ui/form"
import { Typography } from "@/components/ui/typography"
import { FileUpload } from "../file-upload"

interface DocumentUploadProps {
  form: any
  documentType: "dl" | "govId"
  title: string
}

export function DocumentUpload({
  form,
  documentType,
  title,
}: DocumentUploadProps) {
  const frontFieldName =
    documentType === "dl" ? "dlFrontFile" : "govIdFrontFile"
  const backFieldName = documentType === "dl" ? "dlBackFile" : "govIdBackFile"

  const frontLabel =
    documentType === "dl"
      ? "Upload front of Driving License"
      : "Upload front of Government ID"
  const backLabel =
    documentType === "dl"
      ? "Upload back of Driving License"
      : "Upload back of Government ID"

  return (
    <div className='space-y-3'>
      <Typography as='h4' className='text-sh5 font-medium'>
        {title}
      </Typography>

      <FormField
        control={form.control}
        name={frontFieldName}
        render={({ field }) => (
          <FormItem>
            <FormControl>
              <FileUpload
                label={frontLabel}
                onChange={(file: File | null) => {
                  field.onChange(file)
                  form.setValue(frontFieldName, file)
                }}
                value={field.value}
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      <FormField
        control={form.control}
        name={backFieldName}
        render={({ field }) => (
          <FormItem>
            <FormControl>
              <FileUpload
                label={backLabel}
                onChange={(file: File | null) => {
                  field.onChange(file)
                  form.setValue(backFieldName, file)
                }}
                value={field.value}
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
    </div>
  )
}
