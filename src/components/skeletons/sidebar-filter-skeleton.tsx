"use client"

import { Skeleton } from "@/components/ui/skeleton"
import { motion } from "framer-motion"

export default function SidebarFilterSkeleton() {
  return (
    <motion.div
      initial={{ opacity: 0, x: -20 }}
      animate={{ opacity: 1, x: 0 }}
      transition={{ duration: 0.3 }}
      className='rounded-2xl border border-gray-200 bg-white p-6 shadow-sm'
    >
      {/* Header */}
      <div className='mb-6 flex items-center justify-between'>
        <div className='flex items-center gap-2'>
          <Skeleton className='h-5 w-5 bg-neutral-200' />
          <Skeleton className='h-6 w-16 bg-neutral-200' />
        </div>
        <Skeleton className='h-8 w-20 bg-neutral-200' />
      </div>

      <div className='space-y-6'>
        {/* Bike Category Tabs */}
        <div className='space-y-3'>
          <Skeleton className='h-5 w-24 bg-neutral-200' />
          <div className='grid grid-cols-2 gap-1 rounded-lg bg-neutral-150 p-1'>
            <Skeleton className='h-8 rounded-md bg-neutral-200' />
            <Skeleton className='h-8 rounded-md bg-neutral-200' />
          </div>
        </div>

        {/* Price Sorting */}
        <div className='space-y-3'>
          <div className='flex items-center gap-2'>
            <Skeleton className='h-4 w-4 bg-neutral-200' />
            <Skeleton className='h-5 w-20 bg-neutral-200' />
          </div>
          <Skeleton className='h-10 w-full rounded-2xl bg-neutral-200' />
        </div>

        {/* Separator */}
        <div className='h-px bg-neutral-200' />

        {/* Availability filter */}
        <div className='space-y-3'>
          <Skeleton className='h-5 w-24 bg-neutral-200' />
          <div className='space-y-3'>
            {[1, 2, 3].map((i) => (
              <div key={i} className='flex items-center gap-3'>
                <Skeleton className='h-4 w-4 bg-neutral-200' />
                <Skeleton className='h-4 w-20 bg-neutral-200' />
              </div>
            ))}
          </div>
        </div>

        {/* Hub locations filter */}
        <div className='space-y-3'>
          <div className='flex items-center gap-2'>
            <Skeleton className='h-4 w-4 bg-neutral-200' />
            <Skeleton className='h-5 w-28 bg-neutral-200' />
          </div>
          <div className='rounded-lg border border-gray-100 p-3'>
            <div className='space-y-3'>
              {[1, 2, 3, 4].map((i) => (
                <div key={i} className='flex items-center gap-3'>
                  <Skeleton className='h-4 w-4 bg-neutral-200' />
                  <Skeleton className='h-4 w-24 bg-neutral-200' />
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </motion.div>
  )
}
