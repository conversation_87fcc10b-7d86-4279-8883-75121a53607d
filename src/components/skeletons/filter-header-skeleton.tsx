"use client"

import { Skeleton } from "@/components/ui/skeleton"
import { motion } from "framer-motion"

export default function FilterHeaderSkeleton() {
  return (
    <motion.div
      initial={{ opacity: 0, y: -10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      className='mb-6'
    >
      <div className='flex flex-col gap-4 md:flex-row md:items-center md:justify-between'>
        <div>
          {/* Title skeleton */}
          <Skeleton className='mb-2 h-8 w-64 bg-neutral-200' />
          {/* Subtitle skeleton */}
          <Skeleton className='h-4 w-48 bg-neutral-200' />
        </div>

        {/* Sorting skeleton */}
        <div className='flex items-center gap-2'>
          <Skeleton className='h-4 w-16 bg-neutral-200' />
          <Skeleton className='h-10 w-32 bg-neutral-200' />
        </div>
      </div>
    </motion.div>
  )
}
