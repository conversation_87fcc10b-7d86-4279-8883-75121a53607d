"use client"

import { Skeleton } from "@/components/ui/skeleton"
import { motion } from "framer-motion"

export default function SearchHeaderSkeleton() {
  return (
    <motion.div
      initial={{ opacity: 0, y: -10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      className='bg-white shadow-sm'
    >
      <div className='container mx-auto px-4 py-6'>
        <div className='flex flex-col gap-4 md:flex-row md:items-center md:justify-between'>
          {/* Left side - Search info */}
          <div className='space-y-2'>
            <Skeleton className='h-6 w-48 bg-neutral-200' />
            <Skeleton className='h-4 w-64 bg-neutral-200' />
          </div>

          {/* Right side - Modify search button */}
          <Skeleton className='h-10 w-32 bg-neutral-200' />
        </div>

        {/* Search details */}
        <div className='mt-4 flex flex-wrap gap-4'>
          <div className='flex items-center gap-2'>
            <Skeleton className='h-4 w-4 bg-neutral-200' />
            <Skeleton className='h-4 w-24 bg-neutral-200' />
          </div>
          <div className='flex items-center gap-2'>
            <Skeleton className='h-4 w-4 bg-neutral-200' />
            <Skeleton className='h-4 w-20 bg-neutral-200' />
          </div>
          <div className='flex items-center gap-2'>
            <Skeleton className='h-4 w-4 bg-neutral-200' />
            <Skeleton className='h-4 w-16 bg-neutral-200' />
          </div>
        </div>
      </div>
    </motion.div>
  )
}
