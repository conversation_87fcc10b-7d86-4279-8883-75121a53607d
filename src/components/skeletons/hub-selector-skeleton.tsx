import { Skeleton } from "@/components/ui/skeleton"
import { MapPin } from "lucide-react"

export default function HubSelectorSkeleton() {
  return (
    <div className='w-full'>
      <div className='flex items-center justify-between rounded-md border border-gray-200 bg-white px-3 py-2'>
        <div className='flex items-center gap-2'>
          <MapPin className='h-4 w-4 text-gray-400' />
          <Skeleton className='h-4 w-32' />
        </div>
        <Skeleton className='h-4 w-4' />
      </div>
    </div>
  )
}
