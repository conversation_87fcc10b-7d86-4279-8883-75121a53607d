"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>eader } from "@/components/ui/card"
import { Skeleton } from "@/components/ui/skeleton"
import { motion } from "framer-motion"

export default function BikeCardSkeleton() {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
    >
      <Card className='transition-duration-300 w-full rounded-3xl border-none bg-gray-100 shadow-sm'>
        <CardHeader className='px-5 pb-2 pt-5 text-left'>
          {/* Title skeleton */}
          <Skeleton className='h-6 w-3/4 bg-neutral-200' />
          {/* Subtitle skeleton */}
          <Skeleton className='mt-1 h-3 w-20 bg-neutral-200' />
        </CardHeader>

        <CardContent className='px-5 pb-0 pt-2'>
          {/* Image skeleton */}
          <div className='relative flex h-48 w-full items-center justify-center overflow-hidden rounded-lg bg-neutral-50'>
            <Skeleton className='h-full w-full bg-neutral-200' />
          </div>
        </CardContent>

        <CardFooter className='flex flex-col gap-4 px-5 pb-5 pt-4'>
          {/* Location skeleton */}
          {/* <div className='flex items-center gap-2'>
            <Skeleton className='h-[18px] w-[18px] bg-neutral-200' />
            <Skeleton className='h-4 w-24 bg-neutral-200' />
          </div> */}

          {/* Hub selector skeleton */}
          <div className='w-full space-y-2'>
            {/* <Skeleton className='h-4 w-32 bg-neutral-200' /> */}
            <Skeleton className='h-8 w-full rounded-3xl bg-neutral-200' />
          </div>

          {/* Price and button row */}
          <div className='flex w-full flex-wrap items-start justify-between gap-4 lg:gap-8'>
            {/* Price skeleton */}
            <div className='flex flex-1 flex-col items-start justify-start'>
              <Skeleton className='h-8 w-24 bg-neutral-200' />
              <Skeleton className='mt-1 h-3 w-20 bg-neutral-200' />
            </div>

            {/* Button skeleton */}
            <Skeleton className='h-10 w-full flex-1 bg-neutral-200 lg:min-w-[150px]' />
          </div>
        </CardFooter>
      </Card>
    </motion.div>
  )
}
