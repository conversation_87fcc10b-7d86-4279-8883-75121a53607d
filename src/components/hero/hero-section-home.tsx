"use client"
import { cn } from "@/lib/utils"
import SpImage from "@/shared/SpImage/sp-image"
import { useBikeRentalStore } from "@/store/bike-rental-store"
import { Typography } from "../ui/typography"
import StaticText from "./static-text"

const HeroSectionHome = () => {
  const { total_hours } = useBikeRentalStore()
  return (
    <section
      className={cn(
        "relative z-[1] flex h-full w-full flex-col overflow-hidden bg-bike-gradient md:h-auto md:max-h-[900px] lg:pt-40 2xl:max-h-[850px]",
        total_hours && total_hours > 0 ? "pt-14" : "",
      )}
    >
      {/* Main hero section with gradient background */}
      {/* <div
      className={cn(
        "bg-bike-gradient absolute inset-0 z-[-1] h-[750px] w-full md:h-[880px]",
      )}
    ></div> */}

      <div className='h-20'></div>

      {/* Center text content */}
      <div className='relative z-10 mt-6 flex flex-col items-center justify-center gap-4 px-0 py-2 text-center text-gray-100 md:gap-5 md:p-5'>
        {/* Title */}
        <div className='flex flex-wrap items-center justify-center gap-1 px-4 font-ubuntu text-d7 font-bold tracking-tight text-primary-900 md:text-d5 lg:text-d3'>
          <div className='relative flex items-center justify-center'>
            Real
            <SpImage
              src='https://images.sharepal.in/bike-rental/rent-vector.svg'
              width={350}
              height={80}
              alt='Waiver Line'
              className='inline-block h-auto w-full'
              containerClassName='absolute bottom-[-4px] md:bottom-[-4px] w-[80px] md:w-[170px]'
            />
          </div>
          <Typography
            as='h1'
            className='mx-2 font-ubuntu text-d7 font-bold tracking-tight md:text-d5 lg:text-d3'
          >
            Adventure Begins
          </Typography>
          <div className='relative flex items-center justify-center'>
            on the ride
            <SpImage
              src='https://images.sharepal.in/carepal/waiver-line.svg'
              width={250}
              height={80}
              alt='Waiver Line'
              className='inline-block h-auto w-[150px] md:w-[250px]'
              containerClassName='absolute bottom-[-10px] md:bottom-[-22px]'
            />
          </div>
        </div>

        <StaticText />
      </div>

      {/* Coverage cards section - using the new component */}
      <div className='mx-auto w-full max-w-[1340px] px-2'>
        {/* full width image differne for both mobil eand dkesto */}
        <div className='mt-10 h-auto w-full'>
          <SpImage
            src='https://images.sharepal.in/bike-rental/bike-hero-desktop.webp'
            width={1900}
            height={600}
            alt='Hero Section Image'
            className='hidden h-full w-full object-cover md:block'
          />
          <SpImage
            src='https://images.sharepal.in/bike-rental/bike-hero-mobile.webp'
            width={375}
            height={300}
            alt='Hero Section Image'
            className='block h-full w-full object-cover md:hidden'
          />
        </div>
      </div>
    </section>
  )
}

export default HeroSectionHome
