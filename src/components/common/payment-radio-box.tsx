import { Label } from "@/components/ui/label"
import { RadioGroupItem } from "@/components/ui/radio-group"
import { Typography } from "../ui/typography"

interface RadioCardProps {
  value: string
  id: string
  icon?: React.ReactNode
  label: string
  sublabel?: string
  description?: string
}

export function PaymentCard({
  value,
  id,
  icon,
  label,
  sublabel,
  description,
}: RadioCardProps) {
  return (
    <div className='relative flex w-full items-center gap-2 rounded-2xl border-2 border-input p-4 has-[[data-state=checked]]:border-secondary-500'>
      <RadioGroupItem
        value={value}
        id={id}
        aria-describedby={`${id}-description`}
        className='after:absolute after:inset-0'
      />
      <div className='flex grow items-start gap-3'>
        {icon && <div className='hidden shrink-0'>{icon}</div>}
        <div className='grid grow gap-2'>
          <Label
            htmlFor={id}
            className='flex w-max items-center justify-center shadow-red-400'
          >
            <Typography
              as={"span"}
              className='inline-flex !text-sh4 text-gray-900'
            >
              {label}
            </Typography>{" "}
            {sublabel && (
              <Typography
                as={"span"}
                className='inline-flex !text-b4 !text-gray-600'
              >
                ({sublabel})
              </Typography>
            )}
          </Label>
          {description && (
            <Typography
              as={"p"}
              id={`${id}-description`}
              className='hidden text-sh4 text-gray-900'
            >
              {description}
            </Typography>
          )}
        </div>
      </div>
    </div>
  )
}
