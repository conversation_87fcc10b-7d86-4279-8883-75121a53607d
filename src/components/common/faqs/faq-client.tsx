"use client"

import { useState } from "react"

import { ProductPageTitle } from "@/components/heading/prouduct-page"
import { But<PERSON> } from "@/components/ui/button"
import { Faq, FAQS } from "./faqs"
import Faqs from "./faqs-sideview"

interface FAQClientComponentProps {
  faqs: Faq[] // Adjust the type according to your FAQ structure
}

const FAQClientComponent = ({ faqs }: FAQClientComponentProps) => {
  const limit = 5
  const [showMore, setShowMore] = useState(false)

  return (
    <div className='flex h-full w-full flex-col items-start gap-2 p-4 !pt-0 md:p-6'>
      <ProductPageTitle heading='Frequently Asked Questions (FAQs)' />

      <FAQS faqs={faqs} limit={limit} />
      {/* Below one is side view faq */}
      {/* Below one is side view faq */}
      <Faqs faqs={faqs} openSideView={showMore} setOpenSideView={setShowMore} />
      {faqs.length > limit && (
        <Button
          onClick={() => setShowMore(true)}
          variant={"neutral"}
          className='w-full font-semibold md:text-base'
          size={"lg"}
        >
          View more FAQ&apos;s
        </Button>
      )}
    </div>
  )
}

export default FAQClientComponent
