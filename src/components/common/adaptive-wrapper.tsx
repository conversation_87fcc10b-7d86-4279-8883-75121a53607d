"use client"

import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>onte<PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>itle,
  <PERSON><PERSON>Trigger,
} from "@/components/ui/dialog"
import {
  <PERSON>er,
  Drawer<PERSON>ontent,
  <PERSON>er<PERSON><PERSON><PERSON>,
  DrawerTrigger,
} from "@/components/ui/drawer"
import {
  Sheet,
  She<PERSON><PERSON>ontent,
  She<PERSON><PERSON>itle,
  Sheet<PERSON>rigger,
} from "@/components/ui/sheet-new"
import useMediaQuery from "@/hooks/use-media-query"
import { cn } from "@/lib/utils"
import type React from "react"
import { useCallback, useMemo, useState } from "react"

import { XIcon } from "lucide-react"
import { Typography } from "../ui/typography"

type WrapperType = "sheet" | "drawer" | "dialog"

interface WrapperConfig {
  type: WrapperType
  side?: "top" | "right" | "bottom" | "left"
}

interface AdaptiveWrapperProps {
  title?: string
  children: React.ReactNode
  trigger?: React.ReactNode
  desktop?: WrapperConfig
  tablet?: WrapperConfig
  mobile?: WrapperConfig
  badgeLabel?: string
  open?: boolean
  onOpenChange?: (open: boolean) => void
  className?: string
  only?: "desktop" | "tablet" | "mobile"
  autoClose?: boolean
  titleClassName?: string
}

// 🏆 Reusable Title Wrapper (Uses correct title component)
const WrapperTitle = ({
  title,
  type,
  onClose,
  badgeLabel,
  titleClassName,
}: {
  title: string
  type: WrapperType
  onClose: () => void
  badgeLabel?: string
  titleClassName: string
}) => {
  if (!title) return null

  const isSheet = type === "sheet"
  const isDrawer = type === "drawer"
  const isDialog = type === "dialog"
  const isSticky = isDialog ? false : true // Sticky for Sheet & Drawer

  const TitleComponent =
    type === "sheet"
      ? SheetTitle
      : type === "drawer"
        ? DrawerTitle
        : DialogTitle

  return (
    <div
      className={cn(
        `flex items-center justify-start gap-3 border-b border-neutral-200 bg-gray-100 px-4 pb-5 pt-3 md:gap-6 md:p-6`,
        isSticky ? "sticky top-0 z-10" : "",
        isDialog ? "border-none bg-transparent !pb-1" : "",
        titleClassName,
      )}
    >
      {isSheet && (
        <button
          onClick={onClose}
          className='cursor-pointer md:scale-[1.5]'
          aria-label='Close'
        >
          <XIcon />
        </button>
      )}
      <TitleComponent
        className={cn(
          "flex-1 text-start font-bold",
          isDialog
            ? "pb-0 text-center text-h2 text-gray-900 md:text-h1"
            : "md:text-xl",
        )}
      >
        {title}
      </TitleComponent>
      {badgeLabel && (
        <div className='flex items-center justify-center rounded-full bg-neutral-150 px-4 py-2'>
          <Typography as='span' className='text-sh7 text-neutral-500'>
            {badgeLabel}
          </Typography>
        </div>
      )}

      {isDrawer && (
        <button
          onClick={onClose}
          className='cursor-pointer md:scale-[1.5]'
          aria-label='Close'
        >
          <XIcon />
        </button>
      )}
      {isDialog && (
        <button
          onClick={onClose}
          className='absolute right-3 top-3 cursor-pointer md:right-6 md:top-6 md:scale-[1.3]'
          aria-label='Close'
        >
          <XIcon />
        </button>
      )}
    </div>
  )
}

export function AdaptiveWrapper({
  title = "",
  children,
  trigger,
  desktop = { type: "sheet", side: "right" },
  tablet = { type: "sheet", side: "right" },
  mobile = { type: "drawer", side: "bottom" },
  open: controlledOpen,
  onOpenChange,
  autoClose,
  only,
  badgeLabel,
  className,
  titleClassName = "",
}: AdaptiveWrapperProps) {
  const isDesktop = useMediaQuery("(min-width: 1024px)")
  const isTablet = useMediaQuery("(min-width: 768px) and (max-width: 1023px)")
  const isMobile = useMediaQuery("(max-width: 767px)")

  const [internalOpen, setInternalOpen] = useState(false)
  const open = controlledOpen ?? internalOpen
  const setOpen = onOpenChange ?? setInternalOpen

  // 🏆 Memoized Wrapper Config
  const wrapperConfig = useMemo((): WrapperConfig => {
    if (isDesktop) return desktop
    if (isTablet) return tablet
    if (isMobile) return mobile
    return desktop // Fallback to desktop config
  }, [isDesktop, isTablet, isMobile, desktop, tablet, mobile])

  const { type, side } = wrapperConfig

  const closeWrapper = useCallback(() => setOpen(false), [setOpen])

  if (only === `desktop` && !isDesktop) return null
  if (only === `tablet` && !isTablet) return null
  if (only === `mobile` && !isMobile) return null

  const renderWrapper = () => {
    switch (type) {
      case "sheet":
        return (
          <Sheet open={open} onOpenChange={setOpen}>
            {trigger && <SheetTrigger asChild>{trigger}</SheetTrigger>}
            <SheetContent
              onInteractOutside={(event) => {
                if (autoClose) {
                  event.preventDefault()
                }
              }}
              className={cn(
                "h-screen overflow-hidden rounded-none p-0 md:min-w-[606px] md:rounded-bl-3xl md:rounded-tl-3xl",
                className,
                only === "desktop" && !isDesktop && "hidden",
                only === "tablet" && !isTablet && "hidden",
                only === "mobile" && !isMobile && "hidden",
              )}
              side={side}
            >
              <div className='h-screen overflow-hidden'>
                <WrapperTitle
                  title={title}
                  type={type}
                  badgeLabel={badgeLabel}
                  onClose={closeWrapper}
                  titleClassName={titleClassName}
                />

                <div className='h-[calc(100vh-3.8rem)] overflow-auto md:h-[calc(100vh-5rem)]'>
                  {children}
                </div>
                {/* {children} */}
              </div>
            </SheetContent>
          </Sheet>
        )
      case "drawer":
        return (
          <Drawer open={open} onOpenChange={setOpen}>
            {trigger && <DrawerTrigger asChild>{trigger}</DrawerTrigger>}
            <DrawerContent
              className={cn(
                "max-h-[calc(100vh-2rem)] overflow-hidden rounded-tl-2xl rounded-tr-2xl p-0",
                className,
              )}
              onInteractOutside={(event) => {
                if (autoClose) {
                  event.preventDefault()
                }
              }}
            >
              <div className='h-full overflow-auto'>
                <WrapperTitle
                  title={title}
                  type={type}
                  titleClassName={titleClassName}
                  onClose={closeWrapper}
                />

                {children}
              </div>
            </DrawerContent>
          </Drawer>
        )
      case "dialog":
        return (
          <Dialog modal={false} open={open} onOpenChange={setOpen}>
            {trigger && <DialogTrigger asChild>{trigger}</DialogTrigger>}
            <DialogContent
              className={cn(
                "hide-scrollbar max-h-[90vh] overflow-y-auto rounded-2xl p-4 md:rounded-3xl",
                className,
              )}
              onInteractOutside={(event) => {
                if (autoClose) {
                  event.preventDefault()
                }
              }}
            >
              <DialogHeader>
                <WrapperTitle
                  titleClassName={titleClassName}
                  title={title}
                  badgeLabel={badgeLabel}
                  type={type}
                  onClose={closeWrapper}
                />
              </DialogHeader>
              {/* {children} */}

              {children}
            </DialogContent>
          </Dialog>
        )
      default:
        return null
    }
  }

  return renderWrapper()
}
