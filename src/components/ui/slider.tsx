"use client"

import { cn } from "@/lib/utils"
import * as React from "react"

interface SliderProps {
  value: number[]
  onValueChange: (value: number[]) => void
  min: number
  max: number
  step?: number
  className?: string
}

const Slider = React.forwardRef<HTMLDivElement, SliderProps>(
  ({ value, onValueChange, min, max, step = 1, className }, ref) => {
    const [isDragging, setIsDragging] = React.useState<number | null>(null)
    const sliderRef = React.useRef<HTMLDivElement>(null)

    const getValueFromPosition = (clientX: number) => {
      if (!sliderRef.current) return min

      const rect = sliderRef.current.getBoundingClientRect()
      const percentage = Math.max(
        0,
        Math.min(1, (clientX - rect.left) / rect.width),
      )
      const rawValue = min + percentage * (max - min)
      return Math.round(rawValue / step) * step
    }

    const handleMouseDown = (index: number) => (e: React.MouseEvent) => {
      e.preventDefault()
      setIsDragging(index)
    }

    const handleMouseMove = React.useCallback(
      (e: MouseEvent) => {
        if (isDragging === null) return

        const newValue = getValueFromPosition(e.clientX)
        const newValues = [...value]
        newValues[isDragging] = newValue

        // Ensure values don't cross over
        if (isDragging === 0 && newValue <= newValues[1]) {
          newValues[0] = newValue
        } else if (isDragging === 1 && newValue >= newValues[0]) {
          newValues[1] = newValue
        }

        onValueChange(newValues)
      },
      [isDragging, value, onValueChange, getValueFromPosition],
    )

    const handleMouseUp = React.useCallback(() => {
      setIsDragging(null)
    }, [])

    React.useEffect(() => {
      if (isDragging !== null) {
        document.addEventListener("mousemove", handleMouseMove)
        document.addEventListener("mouseup", handleMouseUp)
        return () => {
          document.removeEventListener("mousemove", handleMouseMove)
          document.removeEventListener("mouseup", handleMouseUp)
        }
      }
    }, [isDragging, handleMouseMove, handleMouseUp])

    const getThumbPosition = (val: number) => {
      return ((val - min) / (max - min)) * 100
    }

    return (
      <div
        ref={ref}
        className={cn(
          "relative flex w-full touch-none select-none items-center",
          className,
        )}
      >
        <div
          ref={sliderRef}
          className='relative h-2 w-full grow cursor-pointer overflow-hidden rounded-full bg-gray-200'
          onClick={(e) => {
            const newValue = getValueFromPosition(e.clientX)
            // Find closest thumb and update it
            const distances = value.map((v) => Math.abs(v - newValue))
            const closestIndex = distances.indexOf(Math.min(...distances))
            const newValues = [...value]
            newValues[closestIndex] = newValue
            onValueChange(newValues)
          }}
        >
          <div
            className='absolute h-full rounded-full bg-bike-600'
            style={{
              left: `${getThumbPosition(value[0])}%`,
              width: `${getThumbPosition(value[1]) - getThumbPosition(value[0])}%`,
            }}
          />
          {value.map((val, index) => (
            <div
              key={index}
              className='absolute top-1/2 block h-5 w-5 -translate-x-1/2 -translate-y-1/2 transform cursor-grab rounded-full border-2 border-bike-600 bg-white shadow-md active:cursor-grabbing'
              style={{ left: `${getThumbPosition(val)}%` }}
              onMouseDown={handleMouseDown(index)}
            />
          ))}
        </div>
      </div>
    )
  },
)

Slider.displayName = "Slider"

export { Slider }
