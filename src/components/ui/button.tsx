import { cn } from "@/lib/utils"
import { Slot } from "@radix-ui/react-slot"
import { cva, type VariantProps } from "class-variance-authority"
import * as React from "react"

const buttonVariants = cva(
  "inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-4xl text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",
  {
    variants: {
      variant: {
        default:
          "bg-primary-900 text-white hover:bg-primary-800 active:opacity-90 ",
        blue: "bg-primary-500 text-white hover:bg-primary-400 active:opacity-90 disabled:bg-neutral-200 disabled:text-neutral-500",
        primary:
          "bg-primary-500 text-gray-100 hover:bg-primary-400 active:opacity-90 disabled:bg-neutral-200 disabled:text-neutral-500",
        bike: "bg-bike-500 text-white hover:bg-bike-400 active:opacity-90 disabled:bg-neutral-200 disabled:text-neutral-500",
        secondary:
          "bg-secondary-600 text-white hover:bg-secondary-500 active:opacity-90",
        destructive:
          "bg-destructive-600 text-white hover:bg-destructive-500 active:opacity-90",
        warning:
          "bg-warning-600 text-white hover:bg-warning-500 active:opacity-90",
        success:
          "bg-success-600 text-white hover:bg-success-500 active:opacity-90",
        neutral:
          "bg-neutral-150 text-primary-900 hover:bg-neutral-200 active:opacity-90",
        outline:
          "border-2 border-neutral-600 text-neutral-600 hover:bg-neutral-100 bg-neutral-100 active:opacity-90",

        "outline-primary":
          "border-2 border-primary-500 text-primary-500 hover:bg-primary-100 active:opacity-90",

        "outline-bike":
          "border-2 border-bike-600 text-bike-600 hover:bg-bike-100 bg-bike-100 active:opacity-90",

        "outline-secondary":
          "border-2 border-secondary-600 text-secondary-600 hover:bg-secondary-100 active:opacity-90",
        "outline-destructive":
          "border-2 border-destructive-600 text-destructive-600 hover:bg-destructive-100 active:opacity-90",
        "outline-warning":
          "border-2 border-warning-600 text-warning-600 hover:bg-warning-100 active:opacity-90",
        "outline-success":
          "border-2 border-success-600 text-success-600 hover:bg-success-100 active:opacity-90",
        ghost: "bg-transparent hover:bg-neutral-150 active:opacity-90",
        "ghost-primary":
          "text-primary-500 hover:bg-primary-100 active:opacity-90",
        "ghost-destructive":
          "text-destructive-600 hover:bg-destructive-100 active:opacity-90",
        link: "text-primary-500 underline-offset-4 hover:underline",
        normal:
          "bg-transparent text-neutral-900 hover:bg-transparent p-0 m-0 [&_svg]:size-5 cursor-pointer",
        icon: "bg-transparent text-neutral-900 hover:bg-transparent p-0 m-0  cursor-pointer border  border-gray-200",
      },
      size: {
        default: "h-9 px-4 py-2",
        sm: "h-8 px-3 py-1.5 text-xs",
        lg: "h-11 px-6 py-3 text-sm",
        "extra-lg": "h-12 px-6 py-3 text-base",
        icon: "h-10 w-10 p-2",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  },
)

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  asChild?: boolean
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant, size, asChild = false, ...props }, ref) => {
    const Comp = asChild ? Slot : "button"
    return (
      <Comp
        className={cn(buttonVariants({ variant, size, className }))}
        ref={ref}
        {...props}
      />
    )
  },
)
Button.displayName = "Button"

export { Button, buttonVariants }
