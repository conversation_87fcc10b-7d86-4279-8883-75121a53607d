"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  <PERSON>over,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import { cn } from "@/lib/utils"
import { ChevronDown, Clock as ClockIcon } from "lucide-react"
import { useState } from "react"
import { Label } from "./label"

interface TimeSlot {
  value: string
  label: string
  disabled: boolean
  cost?: number
}

interface TimeSelectorProps {
  label: string
  placeholder: string
  value: string | null
  onValueChange: (value: string) => void
  timeSlots: TimeSlot[]
  disabled?: boolean
  className?: string
  contentClassName?: string
  buttonClassName?: string
  gridClassName?: string
  open?: boolean
  setOpen?: (open: boolean) => void
}

export function TimeSelector({
  label,
  placeholder,
  value,
  onValueChange,
  timeSlots,
  disabled = false,
  className,
  contentClassName,
  buttonClassName,
  gridClassName,
  open: controlledOpen,
  setOpen: controlledSetOpen,
}: TimeSelectorProps) {
  const [internalOpen, internalSetOpen] = useState(false)
  const open = controlledOpen !== undefined ? controlledOpen : internalOpen
  const setOpen =
    controlledSetOpen !== undefined ? controlledSetOpen : internalSetOpen
  const selectedSlot = timeSlots.find((slot) => slot.value === value)
  // console.log(value, selectedSlot)

  return (
    <div className={cn("space-y-2", className)}>
      {label && <Label className={cn("font-medium")}>{label}</Label>}
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            variant='outline'
            className={cn(
              "flex h-11 w-full items-center justify-between rounded-2xl border-2 border-neutral-200 bg-gray-100 pl-4",

              buttonClassName,
            )}
            disabled={disabled}
          >
            <div className='flex items-center'>
              <ClockIcon className={cn("mr-2 h-4 w-4")} />
              {selectedSlot ? (
                <span className={cn("font-medium")}>{selectedSlot.label}</span>
              ) : (
                <span className='text-gray-700'>{placeholder}</span>
              )}
            </div>
            <ChevronDown
              className={cn(
                "h-4 w-4 transition-transform duration-200",
                open && "rotate-180",
              )}
            />
          </Button>
        </PopoverTrigger>
        <PopoverContent
          className={cn("w-full p-4", contentClassName)}
          align='start'
        >
          <div
            className={cn(
              "hide-scrollbar grid max-h-80 grid-cols-2 gap-2 overflow-y-auto",
              gridClassName,
            )}
          >
            {timeSlots.map((slot) => (
              <Button
                key={slot.value}
                variant={value === slot.value ? "default" : "outline"}
                disabled={slot.disabled}
                onClick={(e) => {
                  e.stopPropagation()
                  onValueChange(slot.value)
                  setOpen(false)
                }}
                className={cn(
                  "flex h-10 items-center justify-center text-xs transition-colors",
                  value === slot.value
                    ? "bg-primary text-primary-foreground hover:bg-primary/90"
                    : "border-gray-200 hover:border-gray-400 hover:bg-gray-50",
                  slot.disabled && "cursor-not-allowed opacity-50",
                )}
              >
                <span className={cn("truncate font-medium")}>{slot.label}</span>
              </Button>
            ))}
          </div>
          {timeSlots.length === 0 && (
            <div className='flex h-20 items-center justify-center text-sm text-gray-500'>
              No time slots available
            </div>
          )}
        </PopoverContent>
      </Popover>
    </div>
  )
}
