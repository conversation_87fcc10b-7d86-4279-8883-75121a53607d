"use client"

import { But<PERSON> } from "@/components/ui/button"
import { Calendar } from "@/components/ui/calendar"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import { cn } from "@/lib/utils"
import { format } from "date-fns"
import { Calendar as CalendarIcon, ChevronDown } from "lucide-react"
import { useState } from "react"
import { Label } from "./label"

interface DateSelectorProps {
  label: string
  placeholder: string
  value: Date | null
  onValueChange: (date: Date | undefined) => void
  disabled?: (date: Date) => boolean
  className?: string
  contentClassName?: string
  buttonClassName?: string
  dateFormat?: string
  open?: boolean
  setOpen?: (open: boolean) => void
}

export function DateSelector({
  label,
  placeholder,
  value,
  onValueChange,
  disabled,
  className,
  contentClassName,
  buttonClassName,
  dateFormat = "MMM dd, yyyy",
  open: controlledOpen,
  setOpen: controlledSetOpen,
}: DateSelectorProps) {
  const [internalOpen, internalSetOpen] = useState(false)
  const open = controlledOpen !== undefined ? controlledOpen : internalOpen
  const setOpen =
    controlledSetOpen !== undefined ? controlledSetOpen : internalSetOpen

  return (
    <div className={cn("space-y-1", className)}>
      {label && <Label className={cn("font-semibold")}>{label}</Label>}
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            variant='outline'
            className={cn(
              "flex h-11 w-full items-center justify-between rounded-2xl border-2 border-neutral-200 bg-gray-100 pl-4",
              buttonClassName,
            )}
          >
            <div className='flex items-center'>
              <CalendarIcon className={cn("mr-2 h-4 w-4")} />
              {value ? (
                <span className=''>{format(value, dateFormat)}</span>
              ) : (
                <span className='text-gray-700'>{placeholder}</span>
              )}
            </div>
            <ChevronDown
              className={cn(
                "h-4 w-4 transition-transform duration-200",
                open && "rotate-180",
              )}
            />
          </Button>
        </PopoverTrigger>
        <PopoverContent
          className={cn("w-auto p-0", contentClassName)}
          align='start'
        >
          <Calendar
            mode='single'
            selected={value || undefined}
            defaultMonth={value || undefined}
            onSelect={(date) => {
              onValueChange(date)
              setOpen(false)
            }}
            disabled={disabled}
            className='rounded-md'
            initialFocus
          />
        </PopoverContent>
      </Popover>
    </div>
  )
}
