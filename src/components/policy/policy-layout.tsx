"use client"

import { motion, useScroll, useSpring } from "framer-motion"
import type { ReactNode } from "react"
import { TableOfContents } from "./table-of-contents"

interface PolicyLayoutProps {
  title: string
  subtitle: string
  sections: Array<{ id: string; title: string }>
  children: ReactNode
}

export function PolicyLayout({
  title,
  subtitle,
  sections,
  children,
}: PolicyLayoutProps) {
  const { scrollYProgress } = useScroll()
  const scaleX = useSpring(scrollYProgress, {
    stiffness: 100,
    damping: 30,
    restDelta: 0.001,
  })

  return (
    <div className='container relative mx-auto flex min-h-screen flex-col gap-4 py-24 md:gap-6 md:py-52 lg:flex-row'>
      <motion.div
        className='fixed left-0 top-0 z-50 h-1 w-full origin-left bg-primary-500'
        style={{ scaleX }}
      />

      <TableOfContents sections={sections} />

      <main className='flex-1 rounded-3xl bg-gray-100 p-4 pb-10 md:p-6'>
        <div className='mx-auto max-w-3xl'>
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className='mb-8 flex flex-col items-center justify-center gap-2 border-b border-neutral-200 pb-6'
          >
            <h1 className='md:text-3xlr text-2xl font-bold text-gray-900'>
              {title}
            </h1>
            <p className='text-base text-neutral-900 md:text-lg'>{subtitle}</p>
          </motion.div>

          {children}
        </div>
      </main>
    </div>
  )
}
