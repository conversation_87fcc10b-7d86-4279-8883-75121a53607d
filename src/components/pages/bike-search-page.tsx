"use client"

import { BikeSearchContent } from "@/components/bike-rental/bike-search-content"
import { BikeSearchContentLoading } from "@/components/bike-rental/bike-search-content-loading"
import { BikeSearchError } from "@/components/bike-rental/bike-search-error"
import { useBikeSearch } from "@/hooks/use-bike-search"
import { parseBikeRentalUrlParams } from "@/lib/bike-rental-url-utils"
import { BUSINESS_HOURS } from "@/lib/bike-rental-utils"
import { fetchBikesInCity } from "@/services/bikes"
import { useBikeRentalStore } from "@/store/bike-rental-store"
import { useQuery } from "@tanstack/react-query"
import { useParams, useRouter, useSearchParams } from "next/navigation"
import { useCallback, useEffect, useState } from "react"
import HowItWorks from "../common/how-it-works"

export const BikeSearchPage: React.FC = () => {
  const { setFromUrlParams } = useBikeRentalStore()
  const params = useParams()
  const city = params.city as string
  const router = useRouter()
  const searchParams = useSearchParams()

  // State for parsed URL parameters
  const [parsedParams, setParsedParams] = useState<any>(null)
  const [urlError, setUrlError] = useState<string | null>(null)

  // Parse URL parameters
  useEffect(() => {
    console.log("📋 [BikeSearchPage] Parsing URL parameters...")

    const parseResult = parseBikeRentalUrlParams(searchParams)

    if (!parseResult.isValid) {
      console.error(
        "❌ [BikeSearchPage] URL parsing failed:",
        parseResult.errors,
      )

      // Provide user-friendly error messages
      let errorMessage = "Invalid search parameters"
      if (
        parseResult.errors.includes("Missing pickup timestamp") ||
        parseResult.errors.includes("Missing dropoff timestamp")
      ) {
        errorMessage =
          "Please select both pickup and dropoff times to search for bikes."
      } else if (
        parseResult.errors.includes("Pickup time must be in the future")
      ) {
        errorMessage =
          "Pickup time must be in the future. Please select a valid time."
      } else if (
        parseResult.errors.includes("Pickup time must be before dropoff time")
      ) {
        errorMessage =
          "Pickup time must be before dropoff time. Please check your selection."
      } else if (
        parseResult.errors.some((error) =>
          error.includes(
            `Minimum rental duration is ${BUSINESS_HOURS.MIN_RENTAL_HOURS} hour`,
          ),
        )
      ) {
        errorMessage = `Minimum rental duration is ${BUSINESS_HOURS.MIN_RENTAL_HOURS} hours. Please select a longer period to search for bikes.`
      } else if (
        parseResult.errors.some((error) => error.includes("business hours"))
      ) {
        errorMessage = `Please select pickup and dropoff times between ${BUSINESS_HOURS.START_HOUR}:00 AM and ${BUSINESS_HOURS.END_HOUR}:00 PM.`
      } else {
        errorMessage = `Search error: ${parseResult.errors.join(", ")}`
      }

      setUrlError(errorMessage)
      setParsedParams(null)
      return
    }

    console.log("✅ [BikeSearchPage] URL parsing successful:", parseResult.data)
    setParsedParams(parseResult.data)
    setUrlError(null)

    // Initialize store with parsed params
    setFromUrlParams(parseResult.data!)
  }, [searchParams, setFromUrlParams])

  // Fetch bikes using TanStack Query
  // const {
  //   data: bikes = [],
  //   isLoading,
  //   error: bikesError,
  //   isError,
  //   isPending,
  // } = useBikesQuery({
  //   pickup_timestamp: parsedParams?.pickup_timestamp || null,
  //   dropoff_timestamp: parsedParams?.dropoff_timestamp || null,
  //   city,
  //   enabled: !!parsedParams && !urlError,
  // })

  const {
    data: bikes = [],
    isLoading,
    error: bikesError,
    isError,
    isPending,
  } = useQuery({
    queryKey: [],
    queryFn: () => fetchBikesInCity(city),
    enabled: !!parsedParams && !urlError,
  })

  // Use bike search hook for filtering
  const {
    filteredBikes,
    showMobileFilters,
    setShowMobileFilters,
    handleFiltersChange,
  } = useBikeSearch({ initialBikes: bikes })

  // // Calculate total hours from parsed params
  // const total_hours = useMemo(() => {
  //   if (!parsedParams) return undefined
  //   return (
  //     (parsedParams.dropoff_timestamp - parsedParams.pickup_timestamp) / 3600
  //   )
  // }, [parsedParams])

  // Memoized handlers
  const handleGoBack = useCallback(() => router.back(), [router])
  const handleToggleMobileFilters = useCallback(
    () => setShowMobileFilters(!showMobileFilters),
    [showMobileFilters, setShowMobileFilters],
  )

  // Show loading state
  if (isPending || isLoading) {
    return (
      <div className='min-h-screen py-24 pt-36 lg:pt-48'>
        <BikeSearchContentLoading />
      </div>
    )
  }

  // Show error state (URL parsing error or bikes fetch error)
  const errorMessage = urlError || (isError && bikesError?.message) || null
  if (errorMessage) {
    // Determine error type for better UI
    let errorType: "validation" | "fetch" | "expired" | "network" | "general" =
      "general"

    if (urlError) {
      if (urlError.includes(`${BUSINESS_HOURS.MIN_RENTAL_HOURS} hours`)) {
        errorType = "validation"
      } else if (
        urlError.includes("time has passed") ||
        urlError.includes("future")
      ) {
        errorType = "expired"
      } else {
        errorType = "validation"
      }
    } else if (isError) {
      errorType = "fetch"
    }

    return (
      <div className='min-h-screen py-24 pt-36 lg:pt-52'>
        <div className='container'>
          <BikeSearchError
            error={errorMessage}
            onGoBack={handleGoBack}
            retryable={false}
            type={errorType}
          />
        </div>
      </div>
    )
  }

  // Show error if no parsed params (shouldn't happen after loading)
  if (!parsedParams) {
    return (
      <div className='min-h-screen py-24 pt-36 lg:pt-52'>
        <div className='container'>
          <BikeSearchError
            error='Missing search parameters. Please select pickup and dropoff times to search for bikes.'
            onGoBack={handleGoBack}
            retryable={false}
          />
        </div>
      </div>
    )
  }

  // Main content with bikes
  return (
    <div className='container min-h-screen py-24 pt-36 lg:pt-52'>
      <BikeSearchContent
        bikes={bikes}
        filteredBikes={filteredBikes}
        showMobileFilters={showMobileFilters}
        onToggleMobileFilters={handleToggleMobileFilters}
        onFiltersChange={handleFiltersChange}
        // total_hours={total_hours}
        city={city}
      />

      <section className='py-16'>
        <HowItWorks />
      </section>
    </div>
  )
}
