// import { SampleSlider } from '@/components/sliders/sample-slider'

import NotFound from "@/app/not-found"
import { fetchBikesInCity } from "@/services/bikes"
import { fetchCities } from "@/services/city"
import { BikeCard } from "../cards/bike-card"
import FAQServerComponent from "../common/faqs"
import HowItWorks from "../common/how-it-works"
import HeroSectionHome from "../hero/hero-section-home"
import Reviews from "../reviews"
import SectionTitle from "../section-title"

interface CityHomePageProps {
  city: string
  searchParams?: { [key: string]: string | string[] | undefined }
  showSeoContent?: boolean // to differentiate between main page and city page
}
export default async function Page({ city, searchParams }: CityHomePageProps) {
  const cities = await fetchCities()
  //find city in cities and if not found then redirect to 404
  const cityFound = cities.find((item) => item.city_url === city)
  if (!cityFound) {
    return NotFound()
  }

  // console.log('trendings', trendings)

  const bikes = await fetchBikesInCity(city)
  console.log("bikes", bikes)
  // if (!bikes) {
  //   return NotFound()
  // }

  return (
    <>
      <HeroSectionHome />

      <section className='container py-16'>
        <div className='flex items-center justify-between border-b border-neutral-200 py-4 md:py-5'>
          <SectionTitle
            nText='All Bikes & Combos'
            cText=''
            className='font-inter text-h6 capitalize md:text-h2'
          />
          <p className='flex items-center justify-center gap-1 text-b2 text-neutral-300'>
            <span className='hidden md:inline-flex'>Total items: </span>
            <span className='text-neutral-500'>{bikes?.length} items</span>
          </p>
        </div>

        {/* product list */}

        <div className='mt-6 grid grid-cols-1 gap-4 sm:grid-cols-2 md:mt-10 md:gap-6 lg:grid-cols-3 lg:gap-8'>
          {/* {Array.from({ length: 10 }).map((item, index) => (
            <BikeCard key={index} />
          ))} */}

          {bikes.map((bike) => (
            <BikeCard key={bike.id} {...bike} />
          ))}
        </div>
      </section>
      <section className='container py-16'>
        <HowItWorks />
      </section>

      <Reviews />

      <section className='container py-16'>
        <div className='rounded-3xl bg-gray-100 p-2 pt-3 md:p-4 md:pt-6'>
          <FAQServerComponent />
        </div>
      </section>
    </>
  )
}
