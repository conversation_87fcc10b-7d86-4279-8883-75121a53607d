"use client"

import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"

const countries = [
  {
    value: 91,
    label: "India",
    flag: "🇮🇳",
  },
  {
    value: 1,
    label: "United States",
    flag: "🇺🇸",
  },
  {
    value: 44,
    label: "United Kingdom",
    flag: "🇬🇧",
  },
]

interface CountryCodeSelectProps {
  value: string
  onChange: (value: string) => void
  disabled?: boolean
}

export function CountryCodeSelect({
  value,
  onChange,
  disabled,
}: CountryCodeSelectProps) {
  return (
    <Select
      onValueChange={onChange}
      disabled={disabled}
      defaultValue={value?.toString()}
    >
      <SelectTrigger
        disabled={disabled}
        className='h-12 max-w-max rounded-none rounded-l-md border-0 !bg-neutral-150 bg-transparent px-3 font-medium shadow-transparent hover:bg-neutral-100 focus:border-0'
      >
        <SelectValue />
        {/* <ChevronDown className="h-4 w-4 opacity-50" /> */}
      </SelectTrigger>
      <SelectContent>
        {countries.map((country) => (
          <SelectItem
            key={country.value}
            value={`${country.value}`}
            disabled={disabled}
            className='border-0 font-medium ring-0'
          >
            {/* <span className="mr-2">{country.flag}</span> */}+{country.value}
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  )
}
