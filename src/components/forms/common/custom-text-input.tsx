import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { cn } from "@/lib/utils"
import React from "react"
import { FieldPath, FieldValues, UseFormReturn } from "react-hook-form"

interface CustomTextInputProps {
  value?: string
  onChange?: (value: string) => void
  className?: string
  placeholder?: string
  type?: string
  disabled?: boolean
  readOnly?: boolean
  maxLength?: number
  pattern?: string
  required?: boolean
}

interface FormTextInputProps<TFieldValues extends FieldValues>
  extends Omit<CustomTextInputProps, "value" | "onChange"> {
  form: UseFormReturn<TFieldValues>
  name: FieldPath<TFieldValues>
  label?: string
  description?: string
}

// Standalone input component
export const CustomTextInput: React.FC<CustomTextInputProps> = ({
  value,
  onChange,
  className,
  type = "text",
  placeholder,
  disabled,
  readOnly,
  max<PERSON>ength,
  pattern,
  ...props
}) => (
  <Input
    type={type}
    className={cn(
      "h-12 !rounded-none border-0 focus-visible:border-0",
      className,
    )}
    value={value}
    onChange={(e) => onChange?.(e.target.value)}
    placeholder={placeholder}
    disabled={disabled}
    readOnly={readOnly}
    maxLength={maxLength}
    pattern={pattern}
    {...props}
  />
)

// Form-integrated version
export function FormTextInput<TFieldValues extends FieldValues>({
  form,
  name,
  label,
  required = false,
  className,
  ...props
}: FormTextInputProps<TFieldValues>) {
  return (
    <FormField
      control={form.control}
      name={name}
      render={({ field }) => (
        <FormItem className='w-full'>
          {label && (
            <FormLabel className='!text-b4'>
              {label} {required && <span className='text-red-500'>*</span>}
            </FormLabel>
          )}
          <FormControl>
            <CustomTextInput
              {...props}
              value={field.value}
              onChange={field.onChange}
              className={className}
            />
          </FormControl>
          <FormMessage />
        </FormItem>
      )}
    />
  )
}
