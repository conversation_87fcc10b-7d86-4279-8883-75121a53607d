"use client"

import { useState } from "react"
import Career from "./career"
import Culture from "./culture"
import MeetYourPals from "./meetyourpals"

const tabs = ["Culture", "Meet your Pals", "Careers"]

export function TabbedContentSection() {
  const [activeTab, setActiveTab] = useState("Culture")

  return (
    <section className='bg-white pt-16 md:pt-24'>
      <div className='w-full overflow-x-hidden'>
        {/* Tab Navigation - Exact Figma specs: 1440×60px */}
        <div className='flex w-full justify-center'>
          <div
            className='flex items-start justify-center bg-neutral-150'
            style={{
              width: "1440px",
              height: "60px",
              paddingTop: "20px",
              paddingRight: "120px",
              paddingLeft: "120px",
              gap: "40px",
            }}
          >
            <div className='flex gap-[40px]'>
              {tabs.map((tab) => (
                <button
                  key={tab}
                  onClick={() => setActiveTab(tab)}
                  className={`whitespace-nowrap px-3 py-2 font-inter text-lg font-bold leading-6 tracking-[0%] transition-all duration-300 ${
                    activeTab === tab
                      ? "text-primary-500 underline decoration-primary-500 decoration-2 underline-offset-4"
                      : "text-neutral-500 hover:text-neutral-700"
                  }`}
                >
                  {tab}
                </button>
              ))}
            </div>
          </div>
        </div>

        {activeTab === "Culture" && <Culture />}
        {activeTab === "Meet your Pals" && <MeetYourPals />}
        {activeTab === "Careers" && <Career />}
      </div>
    </section>
  )
}
