"use client"

import SpImage from "@/shared/SpImage/sp-image"
import { motion } from "framer-motion"

export function HeroSection() {
  return (
    <section className='relative bg-white'>
      {/* Hero Container - Exact Figma Specs */}
      <div className='mx-auto flex h-[546px] w-full max-w-[1440px] flex-col items-center justify-center gap-1 px-4 md:px-0'>
        {/* "LIFE AT SHAREPAL" Label */}
        <motion.p
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className='mb-6 text-center font-inter text-o1 font-semibold uppercase tracking-wider text-neutral-500'
        >
          LIFE AT SHAREPAL
        </motion.p>

        {/* Main Heading */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          className='text-center'
        >
          <h1 className='mb-1 font-ubuntu text-d2 font-bold leading-tight text-primary-900'>
            Come for the Rentals,
          </h1>
          <div className='relative inline-block'>
            <h1 className='font-ubuntu text-d2 font-bold leading-tight text-primary-900'>
              Stay for the{" "}
              <span className='relative text-primary-600'>
                People.
                <SpImage
                  src='https://images.sharepal.in/carepal/waiver-line.svg'
                  width={250}
                  height={80}
                  alt='Waiver Line'
                  className='absolute -bottom-2 left-0 h-auto w-[180px] md:w-[250px]'
                />
              </span>
            </h1>
          </div>
        </motion.div>

        {/* Description Text */}
        <motion.p
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
          className='mt-6 max-w-[605px] text-center font-inter text-h2 font-medium text-primary-900'
        >
          Welcome to SharePal — where work feels like home
          <br />
          and every day brings something new.
        </motion.p>
      </div>
    </section>
  )
}
