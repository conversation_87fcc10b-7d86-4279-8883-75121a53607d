"use client"

import SpImage from "@/shared/SpImage/sp-image"
import { motion } from "framer-motion"
import { Typography } from "../ui/typography"

const teamMembers = [
  {
    name: "<PERSON><PERSON>",
    role: "Founder's Office - Operations and Finance",
    details:
      "The backbone of strategy and execution — where numbers meet operations to shape SharePal's future.",
    avatar: "/images/meet-your-pals/naman.png",
  },
  {
    name: "Yogesh Subramanian",
    role: "Process Owner (Operations)",
    details:
      "Master of the reverse flow — handling returns, defaults, and pickups.",
    avatar: "/images/meet-your-pals/Yogesh.png",
  },
  {
    name: "<PERSON>",
    role: "Marketing Lead",
    details:
      "Designer of calm — crafting a catalog that feels like comfort and looks like peace.",
    avatar: "/images/meet-your-pals/arnold.png",
  },
  {
    name: "Bidish<PERSON>",
    role: "Marketing Associate",
    details:
      "Where numbers meet creativity — optimizing and strategizing through data-powered campaigns.",
    avatar: "/images/meet-your-pals/bidisha.png",
  },
  {
    name: "<PERSON><PERSON><PERSON>",
    role: "Social Media Specialist",
    details:
      "The face of SharePal's socials — crafting scroll-stopping content.",
    avatar: "/images/meet-your-pals/shruti.png",
  },
  {
    name: "Daniel Naik",
    role: "Associate- Content Production, Marketing",
    details: "The visual storyteller — bringing SharePal's story to life.",
    avatar: "/images/meet-your-pals/daniel.png",
  },
  {
    name: "P Swathykrishna",
    role: "Associate - Sales & Marketing",
    details:
      "The energy behind our campaigns — bringing company culture to life.",
    avatar: "/images/meet-your-pals/swathykrishna.png",
  },
  {
    name: "Tejashwi Das",
    role: "Sales and BD Intern",
    details:
      "Turning customer needs into possibilities — with curiosity, hustle, and heart.",
    avatar: "/images/meet-your-pals/tejaswi.png",
  },
  {
    name: "Jashmita Tanwar",
    role: "Marketing Content Strategist",
    details:
      "The voice behind the screen — crafting content that builds trust and tells the SharePal story.",
    avatar: "/images/meet-your-pals/jashmita.png",
  },
  {
    name: "Janani Gopalakrishnan",
    role: "Senior risk operations",
    details:
      "Passionate problem-solver — bringing sharp focus and creative rhythm to risk ops.",
    avatar: "/images/meet-your-pals/janani.png",
  },
  {
    name: "Nikita Singh",
    role: "Senior Operations --",
    details:
      "Risk manager and cross-team ninja — manage smooth operations without hinderance.",
    avatar: "/images/meet-your-pals/nikita.png",
  },
  {
    name: "Bamitha R Shetty",
    role: "Operation Lead",
    details:
      "Leading the Closure Team like a pro — resolving post-rental puzzles and keeping customer-friendly.",
    avatar: "/images/meet-your-pals/bamitha.png",
  },
]

export default function MeetYourPals() {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6 }}
      className='flex w-full justify-center'
    >
      {/* Gray Background Container - Using Tailwind utilities */}
      <div className='flex min-h-[2400px] w-[1440px] flex-col items-center gap-10 bg-neutral-150 px-[120px] py-[60px]'>
        {/* Top Description Text - Using Tailwind utilities */}
        <div className='flex h-16 w-[680px] items-center justify-center'>
          <Typography
            as='p'
            className='text-center font-inter text-h5 font-medium text-primary-900'
          >
            Whether it's sales, content, SEO, operations, social media or design
            — we've explored everything.
          </Typography>
        </div>

        {/* Team Members Grid - All 4 rows */}
        {[0, 1, 2, 3].map((rowIndex) => (
          <div
            key={rowIndex}
            className='grid h-[508.67px] w-[1200px] grid-cols-3 gap-5'
          >
            {teamMembers
              .slice(rowIndex * 3, (rowIndex + 1) * 3)
              .map((member, index) => (
                <motion.div
                  key={member.name}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  viewport={{ once: true }}
                  className='flex h-[508.67px] w-[386.67px] flex-col gap-2 rounded-3xl bg-neutral-100 p-3'
                >
                  {/* Member Photo Container - Using Tailwind utilities */}
                  <div className='flex h-[362.67px] w-[362.67px] items-center justify-center overflow-hidden rounded-2xl bg-white p-1'>
                    <SpImage
                      src={member.avatar}
                      alt={member.name}
                      width={354.67}
                      height={354.67}
                      className='h-[354.67px] w-[354.67px] rounded-xl object-cover'
                    />
                  </div>

                  {/* Member Name - Using Tailwind utilities */}
                  <Typography
                    as='h3'
                    className='h-7 w-[362.67px] text-center font-inter text-h4 font-bold text-neutral-900'
                  >
                    {member.name}
                  </Typography>

                  {/* Member Role - Using Tailwind utilities */}
                  <Typography
                    as='h4'
                    className='h-6 w-[362.67px] text-center font-inter text-b3 font-semibold text-primary-500'
                  >
                    {member.role}
                  </Typography>

                  {/* Member Details - Using Tailwind utilities */}
                  <Typography
                    as='p'
                    className='h-9 w-[362.67px] text-center font-inter text-sh4 font-medium leading-[18px] text-neutral-400'
                  >
                    {member.details}
                  </Typography>
                </motion.div>
              ))}
          </div>
        ))}
      </div>
    </motion.div>
  )
}
