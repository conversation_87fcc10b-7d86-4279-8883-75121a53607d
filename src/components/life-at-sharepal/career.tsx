"use client"

import { motion } from "framer-motion"
import { useState } from "react"
import { Typography } from "../ui/typography"

const jobListings = [
  {
    id: 1,
    title: "Frontend Developer",
    department: "Engineering",
    location: "Bangalore",
  },
  {
    id: 2,
    title: "Backend Developer",
    department: "Engineering",
    location: "Remote",
  },
  {
    id: 3,
    title: "Product Manager",
    department: "Product",
    location: "Bangalore",
  },
  {
    id: 4,
    title: "UI/UX Designer",
    department: "Design",
    location: "Remote",
  },
  {
    id: 5,
    title: "DevOps Engineer",
    department: "Engineering",
    location: "Bangalore",
  },
  {
    id: 6,
    title: "Marketing Specialist",
    department: "Marketing",
    location: "Remote",
  },
]

export default function Career() {
  const [searchQuery, setSearchQuery] = useState("")
  const [selectedRemote, setSelectedRemote] = useState("Remote")
  const [selectedDepartment, setSelectedDepartment] = useState("Department")

  const filteredJobs = jobListings.filter((job) => {
    const matchesSearch = job.title
      .toLowerCase()
      .includes(searchQuery.toLowerCase())
    const matchesRemote =
      selectedRemote === "Remote" || job.location === selectedRemote
    const matchesDepartment =
      selectedDepartment === "Department" ||
      job.department === selectedDepartment
    return matchesSearch && matchesRemote && matchesDepartment
  })

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6 }}
      className='flex w-full justify-center'
    >
      {/* Career Section Container - Using Tailwind utilities */}
      <div className='flex h-[664px] w-[1440px] flex-col items-center gap-10 bg-neutral-150 pb-20 pl-[120px] pr-[120px] pt-[60px]'>
        {/* Top Description Text - Using Tailwind utilities */}
        <div className='flex h-16 w-[680px] items-center justify-center'>
          <Typography
            as='p'
            className='text-center font-inter text-h5 font-medium text-primary-900'
          >
            We've learned to thrive under pressure, take the lead, and think on
            our feet — all while having a blast.
          </Typography>
        </div>

        {/* Search and Filter Section - Using Tailwind utilities */}
        <div className='flex h-12 w-[1200px] items-center gap-5'>
          {/* Remote Filter Button - Using Tailwind utilities */}
          <button
            className='flex h-12 w-32 items-center justify-center gap-1 rounded-full border border-neutral-200 bg-neutral-150 p-2.5 font-inter focus:outline-none'
            onClick={() => {
              const options = ["Remote", "Bangalore", "Mumbai"]
              const nextIndex =
                (options.indexOf(selectedRemote) + 1) % options.length
              setSelectedRemote(options[nextIndex])
            }}
          >
            {/* Leading Icon - Location Pointer */}
            <svg
              width='20'
              height='20'
              viewBox='0 0 20 20'
              fill='none'
              className='h-5 w-5'
            >
              <g style={{ transform: "translate(4.17px, 1.67px)" }}>
                <path
                  d='M5.83 0C2.62 0 0 2.62 0 5.83c0 4.42 5.83 10.84 5.83 10.84s5.84-6.42 5.84-10.84C11.67 2.62 9.05 0 5.83 0z'
                  fill='#030D31'
                  stroke='#030D31'
                  strokeWidth='0.5'
                />
                <circle
                  cx='5.83'
                  cy='5.83'
                  r='2.5'
                  fill='white'
                  stroke='#030D31'
                  strokeWidth='0.5'
                />
              </g>
            </svg>

            {/* Label Container */}
            <div className='flex h-4 w-[68px] items-center justify-center gap-1 px-2'>
              <Typography
                as='span'
                className='flex h-4 w-[52px] items-center justify-center text-center font-inter text-sh5 font-semibold text-primary-900'
              >
                {selectedRemote}
              </Typography>
            </div>

            {/* Trailing Icon - Chevron Down */}
            <svg
              width='20'
              height='20'
              viewBox='0 0 24 24'
              fill='none'
              stroke='currentColor'
              strokeWidth='2'
              className='h-5 w-5'
            >
              <polyline points='6 9 12 15 18 9' />
            </svg>
          </button>

          {/* Department Filter Button - Using Tailwind utilities */}
          <button
            className='flex h-12 w-[158px] items-center justify-center gap-1 rounded-full border border-neutral-200 bg-neutral-150 p-2.5 font-inter focus:outline-none'
            onClick={() => {
              const options = [
                "Department",
                "Engineering",
                "Marketing",
                "Design",
                "Product",
              ]
              const nextIndex =
                (options.indexOf(selectedDepartment) + 1) % options.length
              setSelectedDepartment(options[nextIndex])
            }}
          >
            {/* Leading Icon - Work Brief Case */}
            <svg
              width='20'
              height='20'
              viewBox='0 0 16 16'
              fill='none'
              className='h-5 w-5'
            >
              <path
                d='M3 5h10a1 1 0 0 1 1 1v6a1 1 0 0 1-1 1H3a1 1 0 0 1-1-1V6a1 1 0 0 1 1-1z'
                stroke='#000000'
                strokeWidth='1.5'
                fill='none'
              />
              <path
                d='M6 5V3a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1v2'
                stroke='#000000'
                strokeWidth='1.5'
                fill='none'
              />
              <line
                x1='3'
                y1='9'
                x2='13'
                y2='9'
                stroke='#000000'
                strokeWidth='1.5'
              />
            </svg>

            {/* Label Container */}
            <div className='flex h-4 w-[98px] items-center justify-center gap-1 px-2'>
              <Typography
                as='span'
                className='flex h-4 w-[82px] items-center justify-center text-center font-inter text-sh5 font-semibold text-primary-900'
              >
                {selectedDepartment}
              </Typography>
            </div>

            {/* Trailing Icon - Chevron Down */}
            <svg
              width='20'
              height='20'
              viewBox='0 0 24 24'
              fill='none'
              stroke='currentColor'
              strokeWidth='2'
              className='h-5 w-5'
            >
              <polyline points='6 9 12 15 18 9' />
            </svg>
          </button>

          {/* City Search Input - Using Tailwind utilities */}
          <div className='relative flex h-12 w-[834px] items-center rounded-full border-2 border-neutral-200 bg-gray-100 px-5'>
            <input
              type='text'
              placeholder='Search roles'
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className='h-[18px] w-full border-none bg-transparent pr-10 font-inter text-sh4 font-medium text-neutral-300 focus:outline-none'
            />

            {/* Search Icon - absolutely positioned on the right */}
            <div className='absolute right-6 top-3 flex h-6 w-6 items-center justify-center'>
              <svg
                width='24'
                height='24'
                viewBox='0 0 24 24'
                fill='none'
                className='h-6 w-6'
              >
                <g style={{ transform: "translate(3px, 3px)" }}>
                  <circle
                    cx='8.75'
                    cy='8.75'
                    r='7.75'
                    stroke='#000000'
                    strokeWidth='2'
                    fill='none'
                  />
                  <line
                    x1='16.5'
                    y1='16.5'
                    x2='21'
                    y2='21'
                    stroke='#000000'
                    strokeWidth='2'
                    strokeLinecap='round'
                  />
                </g>
              </svg>
            </div>
          </div>
        </div>

        {/* Job Listings Grid - Using Tailwind utilities */}
        <div className='mt-5 grid w-[1200px] grid-cols-2 gap-5'>
          {filteredJobs.map((job) => (
            <motion.div
              key={job.id}
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.4 }}
              className='flex h-[84px] w-[590px] items-center justify-between rounded-3xl border border-neutral-200 bg-neutral-100 px-6 py-3'
            >
              {/* Job Info Container */}
              <div className='flex h-[60px] w-[215px] flex-col gap-1'>
                {/* Job Title */}
                <Typography
                  as='h3'
                  className='flex h-7 w-[215px] items-center font-inter text-h4 font-bold text-neutral-900'
                >
                  {job.title}
                </Typography>

                <div className='flex items-center gap-4'>
                  {/* Department */}
                  <span className='flex h-7 w-[117px] items-center gap-1 py-1 font-inter text-sh4 text-neutral-400'>
                    <svg
                      width='20'
                      height='20'
                      viewBox='0 0 16 16'
                      fill='none'
                      className='h-5 w-5'
                    >
                      <path
                        d='M3 5h10a1 1 0 0 1 1 1v6a1 1 0 0 1-1 1H3a1 1 0 0 1-1-1V6a1 1 0 0 1 1-1z'
                        stroke='#7D808D'
                        strokeWidth='1.5'
                        fill='none'
                      />
                      <path
                        d='M6 5V3a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1v2'
                        stroke='#7D808D'
                        strokeWidth='1.5'
                        fill='none'
                      />
                      <line
                        x1='3'
                        y1='9'
                        x2='13'
                        y2='9'
                        stroke='#7D808D'
                        strokeWidth='1.5'
                      />
                    </svg>
                    {job.department}
                  </span>

                  {/* Location */}
                  <span className='flex h-7 w-[94px] items-center gap-1 py-1 font-inter text-sh4 text-neutral-400'>
                    <svg
                      width='20'
                      height='20'
                      viewBox='0 0 20 20'
                      fill='none'
                      className='h-5 w-5'
                    >
                      <g style={{ transform: "translate(4.17px, 1.67px)" }}>
                        <path
                          d='M5.83 0C2.62 0 0 2.62 0 5.83c0 4.42 5.83 10.84 5.83 10.84s5.84-6.42 5.84-10.84C11.67 2.62 9.05 0 5.83 0z'
                          fill='#7D808D'
                          stroke='#7D808D'
                          strokeWidth='0.5'
                        />
                        <circle
                          cx='5.83'
                          cy='5.83'
                          r='2.5'
                          fill='white'
                          stroke='#7D808D'
                          strokeWidth='0.5'
                        />
                      </g>
                    </svg>
                    {job.location}
                  </span>
                </div>
              </div>

              {/* View Details Button - Using Tailwind utilities */}
              <button className='h-10 w-[120px] rounded-full border-2 border-primary-900 bg-gray-100 px-4 py-2 font-inter text-sh4 font-medium text-primary-900 transition-colors hover:bg-gray-50'>
                View Details
              </button>
            </motion.div>
          ))}
        </div>

        {/* No Results Message */}
        {filteredJobs.length === 0 && (
          <div className='text-center'>
            <Typography as='p' className='font-inter text-b3 text-neutral-400'>
              No job openings match your search criteria.
            </Typography>
          </div>
        )}
      </div>
    </motion.div>
  )
}
