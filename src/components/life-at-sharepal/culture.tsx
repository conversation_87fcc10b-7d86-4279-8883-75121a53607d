"use client"

import SpImage from "@/shared/SpImage/sp-image"
import { motion } from "framer-motion"
import { Typography } from "../ui/typography"

const testimonials = [
  {
    name: "<PERSON><PERSON>",
    message: "The collaborative culture — everyone's open to helping!",
    avatar: "/images/culture/nikita.png",
  },
  {
    name: "<PERSON><PERSON><PERSON><PERSON>",
    message: "I love the flexibility and friendly environment.",
    avatar: "/images/culture/tejaswi.png",
  },
  {
    name: "<PERSON><PERSON>",
    message:
      "The energy. The trust. The ownership. It feels great to be valued.",
    avatar: "/images/culture/naman.png",
  },
  {
    name: "<PERSON><PERSON><PERSON>",
    message: "My colleagues — chill, supportive, and always fun.",
    avatar: "/images/culture/shruti.png",
  },
  {
    name: "<PERSON>",
    message: "The founders are super approachable!",
    avatar: "/images/culture/swathykrishna.png",
  },
]

export default function Culture() {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6 }}
      className='space-y-0'
    >
      {/* MAIN GRAY SECTION - Using Tailwind utilities */}
      <div className='flex w-full justify-center'>
        <div className='flex h-[710px] w-[1440px] flex-col items-center justify-start gap-10 rounded-3xl bg-neutral-150 pb-20 pl-[120px] pr-[120px] pt-[60px]'>
          {/* 1. TOP QUOTE TEXT - Using Tailwind utilities */}
          <div className='flex h-16 w-[680px] items-center justify-center'>
            <Typography
              as='p'
              className='text-center font-inter text-h5 font-medium text-primary-900'
            >
              We've learned to thrive under pressure, take the lead, and think
              on our feet — all while having a blast.
            </Typography>
          </div>

          {/* 2. WHITE CONTAINER - Using Tailwind utilities */}
          <div className='flex h-[466px] w-[1200px] flex-col items-center justify-center gap-10 rounded-[32px] bg-white px-[120px] py-12 shadow-lg'>
            {/* Blue Arrow */}
            <div className='flex justify-center'>
              <SpImage
                src='/images/culture/arrow.png'
                width={322}
                height={214}
                alt='Blue Arrow'
                className='h-[214px] w-[322px]'
              />
            </div>

            {/* Inner Text Container - Using Tailwind utilities */}
            <div className='flex h-[116px] w-[960px] flex-col items-center justify-center gap-3'>
              {/* Label Text */}
              <div className='flex h-6 w-[960px] items-center justify-center'>
                <Typography
                  as='p'
                  className='text-center font-inter text-sh1 font-medium uppercase text-gray-600'
                >
                  HOW SHAREPAL HELPS US GROW
                </Typography>
              </div>

              {/* Main Quote */}
              <div className='flex h-20 w-[960px] items-center justify-center'>
                <Typography
                  as='p'
                  className='text-center font-ubuntu text-h1 font-bold tracking-tight text-primary-900'
                >
                  "SharePal pushed me out of my comfort zone and helped me
                  become more confident, calm, and proactive."
                </Typography>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* TESTIMONIALS SECTION - Using Tailwind utilities */}
      <div className='flex w-full justify-center'>
        <div className='flex h-[924px] w-[1440px] flex-col items-center justify-start gap-10 bg-white px-[120px] py-[60px]'>
          {/* 1. "WE ASKED OUR TEAM" */}
          <div className='flex w-full items-center justify-center'>
            <Typography
              as='p'
              className='whitespace-nowrap text-center font-inter text-sh1 font-medium uppercase text-gray-600'
            >
              WE ASKED OUR TEAM
            </Typography>
          </div>

          {/* 2. Chat Bubble Container - Right aligned */}
          <div className='flex w-full flex-col items-end gap-2'>
            {/* "Now" Text - Above bubble, right aligned */}
            <div className='flex justify-end'>
              <Typography
                as='span'
                className='mr-4 font-inter text-sh4 font-normal text-neutral-400'
              >
                Now
              </Typography>
            </div>

            {/* Message Bubble - Single Line, No Wrapping */}
            <div className='rounded-full bg-bike-300 px-6 py-3'>
              <Typography
                as='p'
                className='whitespace-nowrap text-center font-inter text-sh1 font-medium text-neutral-700'
              >
                What's your favorite part about working at SharePal?
              </Typography>
            </div>
          </div>

          {/* 3. "HERE'S WHAT THEY SAID" */}
          <div className='flex w-full items-center justify-center'>
            <Typography
              as='p'
              className='whitespace-nowrap text-center font-inter text-sh1 font-medium uppercase text-gray-600'
            >
              HERE'S WHAT THEY SAID
            </Typography>
          </div>

          {/* 4. Testimonials List - Using Tailwind utilities */}
          <div className='flex h-[600px] w-[1200px] flex-col items-start justify-start overflow-hidden'>
            {testimonials.map((testimonial, index) => {
              // Define custom widths for each testimonial message box
              const messageWidths = [525, 434, 604, 481, 377]
              const messageWidth = messageWidths[index] || 525

              return (
                <motion.div
                  key={testimonial.name}
                  initial={{ opacity: 0, x: -20 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  viewport={{ once: true }}
                  className='mb-6 flex w-full items-start gap-4'
                >
                  <div className='h-[60px] w-[60px] flex-shrink-0 overflow-hidden rounded-full'>
                    <SpImage
                      src={testimonial.avatar}
                      alt={testimonial.name}
                      width={60}
                      height={60}
                      className='h-full w-full rounded-full object-cover'
                    />
                  </div>
                  <div className='flex flex-col gap-2'>
                    <div className='flex items-center gap-3'>
                      <Typography
                        as='h4'
                        className='font-inter text-sh1 font-semibold text-neutral-900'
                      >
                        {testimonial.name}
                      </Typography>
                      <Typography
                        as='span'
                        className='font-inter text-sh4 text-neutral-400'
                      >
                        Now
                      </Typography>
                    </div>
                    {/* Message with exact Figma properties */}
                    <div
                      className='flex h-14 min-h-12 items-center justify-center rounded-full bg-neutral-150 px-4 py-3'
                      style={{ width: `${messageWidth}px` }}
                    >
                      <Typography
                        as='p'
                        className='text-center font-inter text-sh1 font-medium text-neutral-700'
                      >
                        {testimonial.message}
                      </Typography>
                    </div>
                  </div>
                </motion.div>
              )
            })}
          </div>
        </div>
      </div>

      {/* CULTURE EVOLUTION SECTION - Exact Figma specs */}
      <div className='flex w-full justify-center'>
        <div className='flex h-[472px] w-[1440px] flex-col items-center justify-center gap-12 bg-neutral-150 px-[120px] py-[60px]'>
          {/* Culture Evolution White Container - Exact specs: 1200×352px */}
          <div className='flex h-[352px] w-[1200px] flex-col items-center justify-center gap-10 rounded-[32px] bg-white px-6 py-12'>
            {/* Content container with proper spacing */}
            <div className='flex flex-col items-center justify-center gap-5'>
              {/* "A CULTURE THAT'S ALWAYS EVOLVING" label */}
              <Typography
                as='p'
                className='text-center font-inter text-sh1 font-medium text-gray-800'
              >
                A CULTURE THAT'S ALWAYS EVOLVING
              </Typography>

              {/* Main heading */}
              <Typography
                as='h3'
                className='text-center font-ubuntu text-d4 font-bold tracking-tight text-primary-900'
              >
                Sure, we're proud of what we've built —<br />
                but we're also constantly improving.
              </Typography>

              {/* Description text - EXACT Figma specs: 900×96px */}
              <div className='flex h-24 w-[900px] items-center justify-center'>
                <Typography
                  as='p'
                  className='text-center font-inter font-medium text-primary-900'
                  style={{
                    fontSize: "24px",
                    lineHeight: "32px",
                    letterSpacing: "0%",
                  }}
                >
                  One thing we're working on?
                  <br />
                  Creating an even stronger collaborative spirit across all
                  teams.
                  <br />
                  Because when we work together, there's literally no limit.
                </Typography>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* INSTAGRAM SECTION - Using Tailwind utilities */}
      <div className='mb-0 flex w-full justify-center'>
        <div className='flex h-[688px] w-[1440px]'>
          {/* Left Content Area - Using Tailwind utilities */}
          <div className='flex h-[688px] w-[870px] flex-col justify-center bg-white py-20 pl-[120px] pr-10'>
            <div className='flex w-[710px] flex-col gap-8'>
              <div className='h-6 w-[172px]'>
                <Typography
                  as='p'
                  className='whitespace-nowrap font-inter text-sh1 font-medium text-gray-800'
                >
                  OUR SOCIAL MEDIA
                </Typography>
              </div>

              <div className='h-[164px] w-[710px]'>
                <Typography
                  as='h3'
                  className='font-ubuntu text-d1 font-bold leading-[82px] tracking-tight text-primary-900'
                >
                  Catch Us
                  <br />
                  Behind the Scenes!
                </Typography>
              </div>

              <div className='h-20 w-[470px]'>
                <Typography
                  as='p'
                  className='font-inter text-h1 font-bold text-primary-500'
                >
                  Wanna see what life really
                  <br />
                  looks like at SharePal?
                </Typography>
              </div>

              <div className='mt-10 flex items-center justify-start'>
                <div className='flex h-16 w-[312px] items-center justify-center rounded-full border-2 border-primary-900 bg-white px-4 py-3'>
                  <Typography
                    as='p'
                    className='whitespace-nowrap text-center font-inter text-h5 font-medium text-primary-900'
                  >
                    Follow us on Instagram
                  </Typography>
                </div>
              </div>
            </div>
          </div>

          {/* Right Image Area - Using Tailwind utilities */}
          <div
            className='flex h-[688px] w-[570px] items-center justify-center gap-3 overflow-hidden p-[60px]'
            style={{
              background:
                "linear-gradient(226.5deg, rgba(158, 255, 0, 0) 37.77%, #9EFF00 155.46%)",
            }}
          >
            {/* Image Container */}
            <div className='relative h-[623px] w-[455px] overflow-hidden rounded-3xl opacity-100'>
              <SpImage
                src='/images/culture/background.png'
                alt='Woman with Instagram logo'
                width={455}
                height={623}
                className='h-full w-full object-cover'
              />

              {/* Instagram Badge - Using Tailwind utilities */}
              <div className='absolute right-5 top-5 flex h-[173px] w-[173px] flex-col items-center justify-center gap-2 rounded-2xl bg-white p-4 opacity-100 shadow-lg'>
                {/* Instagram Icon */}
                <div
                  className='flex h-20 w-20 items-center justify-center rounded-2xl'
                  style={{
                    background:
                      "linear-gradient(45deg, #f09433 0%,#e6683c 25%,#dc2743 50%,#cc2366 75%,#bc1888 100%)",
                  }}
                >
                  <svg width='40' height='40' viewBox='0 0 24 24' fill='white'>
                    <rect
                      x='2'
                      y='2'
                      width='20'
                      height='20'
                      rx='5'
                      ry='5'
                      stroke='white'
                      strokeWidth='2'
                      fill='none'
                    />
                    <path
                      d='M16 11.37A4 4 0 1 1 12.63 8 4 4 0 0 1 16 11.37z'
                      stroke='white'
                      strokeWidth='1.5'
                      fill='none'
                    />
                    <line
                      x1='17.5'
                      y1='6.5'
                      x2='17.51'
                      y2='6.5'
                      stroke='white'
                      strokeWidth='2'
                      strokeLinecap='round'
                    />
                  </svg>
                </div>

                {/* Instagram Handle Text */}
                <div className='flex h-7 w-[140px] items-center justify-center rounded-md bg-primary-900 opacity-100'>
                  <Typography
                    as='p'
                    className='text-center font-inter text-o3 font-bold text-white underline'
                  >
                    @lifeatsharepal
                  </Typography>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </motion.div>
  )
}
