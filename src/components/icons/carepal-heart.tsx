export const CarepalHeartIcon = (
  props?: React.JSX.IntrinsicAttributes & React.SVGProps<SVGSVGElement>,
) => (
  <svg
    xmlns='http://www.w3.org/2000/svg'
    width='32'
    height='32'
    fill='none'
    viewBox='0 0 16 16'
    {...props}
  >
    <path
      fill='inherit'
      d='M10.866 3.801c.62 0 1.201.236 1.64.665.436.426.676.996.676 1.603 0 .608-.24 1.177-.677 1.604l-4.506 4.41-4.504-4.41a2.22 2.22 0 0 1-.677-1.604c0-.607.24-1.176.677-1.604a2.33 2.33 0 0 1 1.638-.664c.619 0 1.2.236 1.638.664l.651.638.58.567.578-.568.648-.637a2.33 2.33 0 0 1 1.639-.664m0-.8c-.803 0-1.605.3-2.218.899L8 4.537 7.349 3.9C6.737 3.3 5.935 3 5.133 3s-1.604.3-2.215.9a3.022 3.022 0 0 0 0 4.34l5.068 4.962L8 13.19l.012.012 5.07-4.962A3.02 3.02 0 0 0 14 6.07a3.02 3.02 0 0 0-.918-2.17 3.16 3.16 0 0 0-2.217-.9z'
    ></path>
    <path
      fill='inherit'
      fillRule='evenodd'
      d='M8.862 5.922h-1.64L6.66 8.528c.94-.39 1.419-1.044 1.419-1.044l-.41-.341L9.36 6.55l-.322 1.73-.436-.362s-.769.982-2.078 1.24l-.216.998h1.268L7.828 9h.615c1.24 0 2.014-.68 2.014-1.76 0-.79-.573-1.317-1.595-1.317z'
      clipRule='evenodd'
    ></path>
  </svg>
)
