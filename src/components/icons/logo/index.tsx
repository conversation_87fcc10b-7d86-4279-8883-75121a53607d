import { cn } from "@/lib/utils"
import React from "react"

export const IconLogoLeft = (
  props?: React.JSX.IntrinsicAttributes & React.SVGProps<SVGSVGElement>,
) => (
  <svg
    xmlns='http://www.w3.org/2000/svg'
    width='86'
    height='27'
    fill='none'
    viewBox='0 0 86 27'
    {...props}
  >
    <path
      fill='#fff'
      fillRule='evenodd'
      d='M2.3 18.159h5.787c.173 1.14 1.544 1.962 3.262 1.962 1.775 0 2.886-.707 2.886-1.746 0-.765-.39-1.212-2.352-1.818l-2.18-.664c-3.478-1.054-5.325-2.93-5.325-5.773 0-4.243 3.666-7.014 8.717-7.014 5.368 0 8.644 2.454 8.673 6.581h-5.585c-.043-1.241-1.212-2.064-2.944-2.064-1.602 0-2.64.722-2.64 1.703 0 .837.576 1.415 2.25 1.905l2.28.664c3.738 1.082 5.355 2.641 5.355 5.643 0 4.387-3.753 7.115-9.251 7.115-5.614 0-8.919-2.381-8.933-6.494m17.816 6.133 4.43-20.825h5.687L28.66 10.77h.115c1.184-1.732 2.973-2.728 5.181-2.728 2.916 0 4.85 1.775 4.85 4.416a9.7 9.7 0 0 1-.217 1.948l-2.078 9.886h-5.672l1.89-9.005c.073-.376.102-.679.102-.982 0-1.097-.91-1.905-2.165-1.905-1.371 0-2.555 1.04-2.872 2.54l-1.977 9.352zm28.73 0h5.714l3.392-15.933h-5.585l-.549 2.57h-.274c-.505-1.704-2.294-2.8-4.59-2.8-4.574 0-7.965 4.416-7.965 10.347 0 3.738 2.034 6.047 5.31 6.047 2.006 0 3.594-.823 4.763-2.482h.26zm1.702-8.948c0 2.57-1.587 4.835-3.406 4.835-1.342 0-2.236-1.068-2.236-2.684 0-2.7 1.53-4.878 3.434-4.878 1.342 0 2.208 1.068 2.208 2.727m5.056 8.948 3.42-15.933h5.586l-.462 2.31h.115c.722-1.487 2.136-2.54 3.854-2.54.909 0 1.558.13 2.193.418l-1.082 4.95c-.736-.346-1.429-.592-2.396-.592-1.89 0-3.261 1.054-3.738 3.248l-1.76 8.139zm13.466-6.898c0 4.589 2.973 7.288 7.533 7.288 3.037 0 5.725-1.882 7.18-4.844-6.297.382-9.34-2.32-9.34-2.32s5.225.941 10.253-.404q.123-.617.182-1.267c.47-5.161-2.502-7.892-6.774-7.892-5.426 0-9.034 3.983-9.034 9.439m5.657-2.944h5.397c.03-.073.044-.303.044-.462 0-1.213-.953-2.078-2.324-2.078-1.486 0-2.742 1.024-3.117 2.54'
      clipRule='evenodd'
    ></path>
  </svg>
)

export const IconLogoLeftBlue = (
  props: React.JSX.IntrinsicAttributes & React.SVGProps<SVGSVGElement>,
) => (
  <svg
    xmlns='http://www.w3.org/2000/svg'
    width='115'
    height='36'
    fill='none'
    viewBox='0 0 115 36'
    {...props}
  >
    <path
      fill='#1B4BFF'
      fillRule='evenodd'
      d='M2.222 23.92h7.83c.234 1.543 2.089 2.656 4.413 2.656 2.401 0 3.905-.957 3.905-2.363 0-1.034-.528-1.64-3.183-2.46l-2.948-.898c-4.706-1.425-7.205-3.964-7.205-7.81 0-5.74 4.96-9.49 11.793-9.49 7.263 0 11.696 3.32 11.735 8.904h-7.556c-.06-1.679-1.64-2.792-3.984-2.792-2.167 0-3.573.976-3.573 2.304 0 1.133.781 1.913 3.046 2.577l3.085.899c5.057 1.464 7.244 3.573 7.244 7.634 0 5.936-5.077 9.626-12.516 9.626-7.595 0-12.066-3.222-12.086-8.786m24.105 8.299L32.32 4.044h7.693l-2.128 9.88h.156c1.601-2.343 4.022-3.69 7.01-3.69 3.944 0 6.56 2.4 6.56 5.974 0 .8-.098 1.699-.293 2.636l-2.811 13.375h-7.674l2.558-12.184a7 7 0 0 0 .137-1.328c0-1.484-1.23-2.577-2.93-2.577-1.854 0-3.455 1.406-3.885 3.436L34.04 32.22zm38.869 0h7.732l4.588-21.556H69.96l-.742 3.475h-.371c-.683-2.304-3.105-3.787-6.21-3.787-6.189 0-10.777 5.974-10.777 14 0 5.056 2.753 8.18 7.185 8.18 2.714 0 4.862-1.113 6.444-3.358h.351zM67.5 20.113c0 3.476-2.148 6.541-4.608 6.541-1.816 0-3.027-1.445-3.027-3.632 0-3.65 2.07-6.6 4.647-6.6 1.816 0 2.988 1.446 2.988 3.691m6.84 12.106 4.627-21.556h7.557l-.625 3.124h.156c.976-2.011 2.89-3.437 5.213-3.437 1.23 0 2.11.176 2.968.567l-1.464 6.697c-.996-.469-1.933-.8-3.241-.8-2.558 0-4.413 1.425-5.057 4.392L82.092 32.22zm18.218-9.333c0 6.209 4.022 9.86 10.192 9.86 4.108 0 7.746-2.546 9.714-6.553-8.519.517-12.637-3.14-12.637-3.14s7.07 1.274 13.872-.546a16 16 0 0 0 .247-1.714c.635-6.983-3.386-10.677-9.165-10.677-7.342 0-12.223 5.39-12.223 12.77m7.654-3.983h7.302c.039-.098.059-.41.059-.625 0-1.64-1.289-2.812-3.144-2.812-2.011 0-3.71 1.386-4.217 3.437'
      clipRule='evenodd'
    ></path>
  </svg>
)

export const IconLogoRight = ({
  color,
  className,
}: {
  color?: string
  className?: string
}) => (
  <svg
    xmlns='http://www.w3.org/2000/svg'
    width='51'
    height='27'
    fill='none'
    viewBox='0 0 51 27'
    className={cn(className)}
  >
    <path
      fill={color ?? "#9EFF00"}
      fillRule='evenodd'
      d='M4.786 3.106h8.14c5.075 0 7.917 2.679 7.917 6.682 0 5.477-3.84 8.93-10 8.93H7.791l-1.25 5.863H.247l1.07-5.06c6.5-1.309 10.317-6.29 10.317-6.29l2.168 1.84 1.601-8.78-8.404 3.004 2.04 1.731s-2.377 3.315-7.047 5.296zm31.4 21.475h-5.892l.49-2.322h-.267c-1.206 1.712-2.843 2.56-4.911 2.56-3.378 0-5.477-2.381-5.477-6.236 0-6.116 3.498-10.67 8.215-10.67 2.366 0 4.212 1.131 4.733 2.887h.282l.566-2.649h5.76zm-7.648-4.242c1.875 0 3.512-2.336 3.512-4.985 0-1.711-.893-2.813-2.277-2.813-1.965 0-3.542 2.248-3.542 5.03 0 1.667.923 2.768 2.307 2.768M42.825 3.106l-4.569 21.475h5.893l4.57-21.475z'
      clipRule='evenodd'
    ></path>
  </svg>
)
