import { cn } from "@/lib/utils"

export const IconCancelled = ({ className }: { className: string }) => (
  <svg
    xmlns='http://www.w3.org/2000/svg'
    width='96'
    height='96'
    fill='none'
    viewBox='0 0 96 96'
    className={cn(className)}
  >
    <g id='Order Status Icons' clipPath='url(#clip0_5032_95404)'>
      <path
        id='Vector'
        fill='url(#paint0_linear_5032_95404)'
        d='M48 96c26.51 0 48-21.49 48-48S74.51 0 48 0 0 21.49 0 48s21.49 48 48 48'
      ></path>
      <path
        id='Intersect'
        fill='url(#paint1_linear_5032_95404)'
        fillRule='evenodd'
        d='M95.316 56.12 71.64 32.444l-8.485-8.486-15.557 15.557-15.556-15.557-8.485 8.486L39.113 48 23.557 63.556l8.485 8.486 23.387 23.386c20.33-3.158 36.44-19.075 39.887-39.308'
        clipRule='evenodd'
        opacity='0.2'
        style={{ mixBlendMode: "multiply" }}
      ></path>
      <path
        id='Vector (Stroke)'
        fill='#fff'
        fillRule='evenodd'
        d='M56.083 48 71.64 63.556l-8.486 8.486-15.556-15.557-15.556 15.557-8.486-8.486L39.113 48 23.556 32.444l8.486-8.486 15.556 15.557 15.556-15.557 8.486 8.486z'
        clipRule='evenodd'
      ></path>
    </g>
    <defs>
      <linearGradient
        id='paint0_linear_5032_95404'
        x1='96'
        x2='0'
        y1='0'
        y2='96'
        gradientUnits='userSpaceOnUse'
      >
        <stop stopColor='#FAC7C3'></stop>
        <stop offset='0.475' stopColor='#F04438'></stop>
        <stop offset='1' stopColor='#601B16'></stop>
      </linearGradient>
      <linearGradient
        id='paint1_linear_5032_95404'
        x1='41.097'
        x2='102.127'
        y1='32.473'
        y2='87.484'
        gradientUnits='userSpaceOnUse'
      >
        <stop stopColor='#601B16'></stop>
        <stop offset='1' stopColor='#FAC7C3'></stop>
      </linearGradient>
      <clipPath id='clip0_5032_95404'>
        <path fill='#fff' d='M0 0h96v96H0z'></path>
      </clipPath>
    </defs>
  </svg>
)

export const IconSuccess = ({ className }: { className: string }) => (
  <svg
    xmlns='http://www.w3.org/2000/svg'
    width='96'
    height='96'
    fill='none'
    viewBox='0 0 96 96'
    className={cn(className)}
  >
    <g id='Order Status Icons' clipPath='url(#clip0_5032_32485)'>
      <path
        id='Vector'
        fill='url(#paint0_linear_5032_32485)'
        d='M48 96c26.51 0 48-21.49 48-48S74.51 0 48 0 0 21.49 0 48s21.49 48 48 48'
      ></path>
      <path
        id='Intersect'
        fill='url(#paint1_linear_5032_32485)'
        fillRule='evenodd'
        d='M95.388 55.689 75.912 36.214l-8.485-8.486L41.97 53.184 29.243 40.456l-8.485 8.485L41.97 70.155 64.796 92.98c15.91-5.944 27.822-20.089 30.591-37.291'
        clipRule='evenodd'
        opacity='0.2'
        style={{ mixBlendMode: "multiply" }}
      ></path>
      <path
        id='Rectangle 3065 (Stroke)'
        fill='#fff'
        fillRule='evenodd'
        d='M41.971 53.185 29.243 40.457l-8.485 8.485L41.97 70.155l33.941-33.941-8.485-8.485z'
        clipRule='evenodd'
      ></path>
    </g>
    <defs>
      <linearGradient
        id='paint0_linear_5032_32485'
        x1='96'
        x2='0'
        y1='0'
        y2='96'
        gradientUnits='userSpaceOnUse'
      >
        <stop stopColor='#B2EBC9'></stop>
        <stop offset='0.475' stopColor='#10BE56'></stop>
        <stop offset='1' stopColor='#064C22'></stop>
      </linearGradient>
      <linearGradient
        id='paint1_linear_5032_32485'
        x1='39'
        x2='95'
        y1='35.502'
        y2='93.002'
        gradientUnits='userSpaceOnUse'
      >
        <stop stopColor='#064C22'></stop>
        <stop offset='1' stopColor='#B2EBC9'></stop>
      </linearGradient>
      <clipPath id='clip0_5032_32485'>
        <path fill='#fff' d='M0 0h96v96H0z'></path>
      </clipPath>
    </defs>
  </svg>
)

export const IconWarning = ({ className }: { className: string }) => (
  <svg
    xmlns='http://www.w3.org/2000/svg'
    width='96'
    height='96'
    fill='none'
    viewBox='0 0 96 96'
    className={cn(className)}
  >
    <g clipPath='url(#clip0_400_22592)'>
      <path
        fill='url(#paint0_linear_400_22592)'
        d='M48 96c26.51 0 48-21.49 48-48S74.51 0 48 0 0 21.49 0 48s21.49 48 48 48'
      ></path>
      <path
        fill='url(#paint1_linear_400_22592)'
        d='M94.782 58.782c-4.097 17.852-18.148 31.903-36 36L42 78V66h8l-8-8V18h12z'
        opacity='0.2'
        style={{ mixBlendMode: "multiply" }}
      ></path>
      <path
        fill='#fff'
        fillRule='evenodd'
        d='M54 18v40H42V18zm0 48v12H42V66z'
        clipRule='evenodd'
      ></path>
    </g>
    <defs>
      <linearGradient
        id='paint0_linear_400_22592'
        x1='96'
        x2='0'
        y1='0'
        y2='96'
        gradientUnits='userSpaceOnUse'
      >
        <stop stopColor='#FDDEB5'></stop>
        <stop offset='0.475' stopColor='#F79009'></stop>
        <stop offset='1' stopColor='#633A04'></stop>
      </linearGradient>
      <linearGradient
        id='paint1_linear_400_22592'
        x1='54.902'
        x2='113.824'
        y1='27.147'
        y2='63.51'
        gradientUnits='userSpaceOnUse'
      >
        <stop stopColor='#633A04'></stop>
        <stop offset='1' stopColor='#FDDEB5'></stop>
      </linearGradient>
      <clipPath id='clip0_400_22592'>
        <path fill='#fff' d='M0 0h96v96H0z'></path>
      </clipPath>
    </defs>
  </svg>
)

export const IconAddressTruck = () => (
  <svg
    xmlns='http://www.w3.org/2000/svg'
    width='36'
    height='38'
    fill='none'
    viewBox='0 0 36 38'
  >
    <path
      fill='#E3E3E3'
      d='M36.666 5.5V3.834A1.667 1.667 0 0 1 38.333 5.5zm-18.333 0h-1.667a1.667 1.667 0 0 1 1.667-1.666zm0 10v-1.666A1.666 1.666 0 0 1 20 15.5zm18.333-8.333H18.333V3.834h18.333zM20 5.5v26.667h-3.334V5.5zm15 23.334V5.5h3.333v23.334zM18.333 17.167H10v-3.334h8.333zM5 22.167v6.666H1.666v-6.666zm11.666 10V15.5H20v16.667zm-7.845 1.178a1.667 1.667 0 0 0 2.357 0l2.357 2.357a5 5 0 0 1-7.07 0zm2.357-2.357a1.666 1.666 0 0 0-2.357 0l-2.356-2.356a5 5 0 0 1 7.07 0zm17.643 2.357a1.667 1.667 0 0 0 2.357 0l2.357 2.357a5 5 0 0 1-7.07 0zm2.357-2.357a1.666 1.666 0 0 0-2.357 0l-2.356-2.356a5 5 0 0 1 7.07 0zm-22.357 0c-.326.326-.488.75-.488 1.179H5c0-1.277.488-2.56 1.465-3.535zm-.488 1.179c0 .442.176.866.488 1.178l-2.356 2.357A5 5 0 0 1 5 32.167zm5-1.667h5v3.334h-5zm-2.155 2.845c.313-.312.488-.736.488-1.178H15c0 1.276-.489 2.56-1.465 3.535zm.488-1.178c0-.442-.175-.866-.488-1.179l2.357-2.356A5 5 0 0 1 15 32.167zm19.512 1.178c.313-.312.488-.736.488-1.178H35c0 1.276-.489 2.56-1.465 3.535zm.488-1.178c0-.442-.175-.866-.488-1.179l2.357-2.356A5 5 0 0 1 35 32.167zM18.333 30.5h8.333v3.334h-8.333zm10.488.488c-.326.326-.488.75-.488 1.179H25c0-1.277.488-2.56 1.465-3.535zm-.488 1.179c0 .442.176.866.488 1.178l-2.356 2.357A5 5 0 0 1 25 32.167zM5 28.833A1.667 1.667 0 0 0 6.666 30.5v3.334a5 5 0 0 1-5-5zm5-11.666a5 5 0 0 0-5 5H1.666A8.333 8.333 0 0 1 10 13.834zm28.333 11.667a5 5 0 0 1-5 5V30.5A1.666 1.666 0 0 0 35 28.834z'
    ></path>
  </svg>
)
