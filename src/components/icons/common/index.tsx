import { cn } from "@/lib/utils"

export const IconStar = (
  props?: React.JSX.IntrinsicAttributes & React.SVGProps<SVGSVGElement>,
) => (
  <svg
    xmlns='http://www.w3.org/2000/svg'
    width='20'
    height='20'
    fill='none'
    viewBox='0 0 20 20'
    {...props}
  >
    <path
      fill='#E8AE19'
      d='M6.354 5.333 10 .604l3.646 4.73 5.708 1.916-3.604 5.104.146 5.688L10 16.396l-5.896 1.646.146-5.709L.667 7.25z'
    ></path>
  </svg>
)

export const IconGoogle = (
  props?: React.JSX.IntrinsicAttributes & React.SVGProps<SVGSVGElement>,
) => (
  <svg
    xmlns='http://www.w3.org/2000/svg'
    width='24'
    height='24'
    fill='none'
    viewBox='0 0 24 24'
    {...props}
  >
    <path
      fill='#4285F4'
      d='M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09'
    ></path>
    <path
      fill='#34A853'
      d='M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23'
    ></path>
    <path
      fill='#FBBC05'
      d='M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22z'
    ></path>
    <path
      fill='#EA4335'
      d='M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53'
    ></path>
  </svg>
)

export const IconWhatsapp = ({ className }: { className?: string }) => (
  <svg
    xmlns='http://www.w3.org/2000/svg'
    width='30'
    height='30'
    className={cn(className)}
    fill='none'
    viewBox='0 0 30 30'
  >
    <path
      fill='#000'
      d='m7.881 24.741 1.086.634A11.9 11.9 0 0 0 15.002 27a12 12 0 1 0-12-12 11.9 11.9 0 0 0 1.626 6.036l.633 1.086-.98 3.602zM.008 30l2.028-7.452A14.9 14.9 0 0 1 .002 15c0-8.285 6.715-15 15-15s15 6.716 15 15c0 8.285-6.715 15-15 15a14.9 14.9 0 0 1-7.545-2.032zm9.58-22.038q.303-.021.605-.006.121.009.243.024c.239.027.501.172.59.373q.67 1.523 1.302 3.06c.093.229.037.521-.14.806-.09.146-.231.35-.395.558-.169.218-.534.617-.534.617s-.148.176-.091.397c.021.084.09.205.153.307l.088.143c.384.64.9 1.29 1.53 1.902.18.174.356.352.545.519a9 9 0 0 0 2.355 1.5l.008.003c.127.056.191.085.377.165q.14.058.287.099.054.015.11.017a.52.52 0 0 0 .442-.213c1.085-1.314 1.185-1.4 1.193-1.4v.003a.72.72 0 0 1 .567-.19.8.8 0 0 1 .265.06c.796.364 2.1.933 2.1.933l.873.391c.147.07.28.237.285.397.006.101.015.263-.02.56-.047.389-.165.855-.282 1.1q-.122.25-.314.453a3.6 3.6 0 0 1-.495.431 3 3 0 0 1-.188.136 8 8 0 0 1-.575.33 3 3 0 0 1-1.249.345c-.277.015-.555.035-.834.02-.012 0-.852-.13-.852-.13a14.2 14.2 0 0 1-5.76-3.069c-.339-.299-.654-.62-.975-.939-1.332-1.328-2.342-2.76-2.955-4.113a5.25 5.25 0 0 1-.495-2.12 4.1 4.1 0 0 1 .847-2.52c.11-.14.213-.287.392-.457.189-.18.31-.276.441-.342a1.5 1.5 0 0 1 .556-.15'
    ></path>
  </svg>
)

export const IconVerification = (
  props: React.JSX.IntrinsicAttributes & React.SVGProps<SVGSVGElement>,
) => (
  <svg
    xmlns='http://www.w3.org/2000/svg'
    width='80'
    height='90'
    fill='none'
    viewBox='0 0 80 90'
    {...props}
  >
    <path
      fill='#476AED'
      d='M16.086 59.24c-4.294 1.008-7.702 4.233-8.91 8.466L4.012 78.794c-1.47 5.16 2.399 10.28 7.742 10.28h47.17c5.343 0 9.212-5.12 7.742-10.28l-3.164-11.088a12.12 12.12 0 0 0-8.91-8.467l-19.251-4.435-19.252 4.435z'
    ></path>
    <path
      fill='#1437BA'
      d='M35.336 71.815c6.355 0 11.506-5.15 11.506-11.506v-2.853l-11.506-2.65-11.506 2.65v2.853c0 6.355 5.15 11.506 11.506 11.506'
    ></path>
    <path
      fill='#BAC7F8'
      d='M43.007 50.721v9.589a7.671 7.671 0 0 1-15.341 0V50.72z'
    ></path>
    <path
      fill='#0F298B'
      d='M16.523 31.664c-3.859-8.486 1.444-14.887 6.77-17.008v.013c.064-.788.164-1.501.273-2.105A2.56 2.56 0 0 1 26.1 10.45h15.723c9.82 0 16.266 11.453 12.618 21.482l-2.495 6.857H19.763z'
    ></path>
    <path
      fill='#BAC7F8'
      d='M21.913 46.886H48.76a7.67 7.67 0 0 0 7.67-7.67 3.834 3.834 0 0 0-3.835-3.836H18.078a3.834 3.834 0 0 0-3.836 3.835 7.67 7.67 0 0 0 7.67 7.67'
    ></path>
    <path
      fill='#E8ECFD'
      d='M43.006 25.792H27.665a7.67 7.67 0 0 0-7.67 7.67v6.76c0 5.085 2.02 9.964 5.616 13.56l1.615 1.614a10.23 10.23 0 0 0 7.231 2.996h1.755c2.711 0 5.314-1.078 7.231-2.996l1.615-1.614a19.18 19.18 0 0 0 5.617-13.56v-6.76a7.67 7.67 0 0 0-7.669-7.67'
    ></path>
    <path
      fill='#91EA00'
      d='M2.736 16.203c-1.06 0-1.918-.859-1.918-1.917V6.615A5.76 5.76 0 0 1 6.571.862h7.67a1.917 1.917 0 1 1 0 3.835h-7.67a1.92 1.92 0 0 0-1.917 1.918v7.67a1.917 1.917 0 0 1-1.918 1.918M14.242 69.898h-7.67a5.76 5.76 0 0 1-5.754-5.753v-7.67a1.917 1.917 0 1 1 3.836 0v7.67a1.92 1.92 0 0 0 1.917 1.917h7.67a1.917 1.917 0 1 1 0 3.836M67.935 16.203c-1.06 0-1.917-.859-1.917-1.917V6.615A1.92 1.92 0 0 0 64.1 4.697h-7.67a1.917 1.917 0 1 1 0-3.835h7.67a5.76 5.76 0 0 1 5.753 5.753v7.67a1.917 1.917 0 0 1-1.918 1.918'
    ></path>
    <path
      fill='#fff'
      d='M68.964 67.756c-2.922 3.037-5.89 6.14-8.81 9.22a2.54 2.54 0 0 1-1.846.797h-.012a2.6 2.6 0 0 1-1.845-.793q-2.34-2.418-4.697-4.818a2.527 2.527 0 0 1 .051-3.616 2.596 2.596 0 0 1 3.655.046q1.4 1.435 2.803 2.878c2.136-2.238 4.282-4.489 6.4-6.71l.54-.567c.973-1.024 2.597-1.041 3.64-.053s1.102 2.598.121 3.616'
    ></path>
    <path
      fill='#fff'
      fillRule='evenodd'
      d='m77.798 64.862-.005-.006q-.742-.716-1.486-1.44-.024-1.008-.056-2.016c-.06-1.94-.9-3.732-2.25-5.083-1.356-1.358-3.155-2.187-5.088-2.248a297 297 0 0 0-2.023-.056q-.725-.739-1.442-1.478c-1.341-1.379-3.143-2.198-5.1-2.197s-3.756.824-5.093 2.194l-.004.004q-.718.74-1.444 1.482-1.01.024-2.023.056h-.006c-3.905.133-7.2 3.42-7.334 7.324v.005q-.032 1.01-.056 2.017-.743.724-1.483 1.44l-.004.004c-1.373 1.335-2.197 3.136-2.196 5.088.001 1.946.816 3.748 2.197 5.09l.004.003q.74.716 1.482 1.439.024 1.008.056 2.017c.06 1.944.902 3.731 2.246 5.08 1.358 1.365 3.162 2.19 5.093 2.25q1.014.032 2.026.057.725.74 1.442 1.477c1.341 1.38 3.144 2.198 5.102 2.197 1.956-.001 3.754-.817 5.097-2.193l.005-.005q.717-.74 1.442-1.48 1.012-.024 2.023-.056h.005c3.905-.133 7.201-3.42 7.336-7.323v-.007q.032-1.008.056-2.016.743-.724 1.485-1.443C79.178 73.703 80 71.903 80 69.95c0-1.964-.834-3.755-2.202-5.087m-3.77-.456q1.085 1.06 2.165 2.104c.961.937 1.505 2.148 1.505 3.439s-.539 2.507-1.5 3.44q-1.079 1.044-2.161 2.104-.03 1.466-.076 2.932c-.093 2.692-2.415 5.011-5.113 5.103a277 277 0 0 1-2.939.076q-1.06 1.08-2.106 2.158c-.939.962-2.154 1.498-3.451 1.499-1.297 0-2.515-.538-3.451-1.5l-2.105-2.153q-1.47-.03-2.94-.077c-1.312-.04-2.568-.601-3.535-1.573s-1.537-2.218-1.577-3.53q-.045-1.468-.077-2.935a615 615 0 0 0-2.161-2.102c-.964-.936-1.5-2.154-1.5-3.44s.54-2.504 1.5-3.437q1.079-1.044 2.161-2.104.03-1.465.077-2.934c.092-2.691 2.414-5.01 5.112-5.102q1.47-.046 2.939-.076 1.061-1.08 2.108-2.16c.934-.958 2.152-1.498 3.446-1.5 1.295 0 2.512.538 3.448 1.501q1.045 1.076 2.106 2.154 1.47.03 2.94.076c1.31.041 2.561.604 3.53 1.574.97.97 1.537 2.22 1.578 3.53q.048 1.467.076 2.933'
      clipRule='evenodd'
    ></path>
    <path
      fill='#fff'
      fillRule='evenodd'
      d='M76.193 66.51q-1.08-1.044-2.166-2.104-.029-1.465-.076-2.934c-.04-1.31-.609-2.559-1.578-3.529s-2.22-1.533-3.53-1.574q-1.47-.046-2.94-.076a514 514 0 0 1-2.106-2.154c-.936-.963-2.153-1.5-3.448-1.5s-2.512.541-3.446 1.5q-1.047 1.077-2.108 2.159-1.47.03-2.939.076c-2.698.092-5.02 2.41-5.112 5.102q-.047 1.468-.077 2.934-1.082 1.06-2.162 2.104c-.96.933-1.5 2.151-1.499 3.437s.536 2.504 1.5 3.44q1.079 1.043 2.161 2.102.03 1.467.077 2.936c.04 1.311.609 2.557 1.577 3.529.967.972 2.223 1.533 3.535 1.573q1.47.047 2.94.077 1.06 1.077 2.105 2.153c.936.962 2.154 1.5 3.451 1.5 1.297-.001 2.512-.537 3.451-1.5q1.046-1.077 2.106-2.157a277 277 0 0 0 2.939-.076c2.698-.092 5.02-2.41 5.113-5.103q.046-1.466.076-2.932a583 583 0 0 1 2.162-2.105c.96-.932 1.5-2.149 1.5-3.44s-.545-2.501-1.506-3.438m-7.228 1.246c-2.922 3.037-5.891 6.14-8.81 9.22a2.54 2.54 0 0 1-1.847.797h-.011a2.6 2.6 0 0 1-1.846-.793q-2.34-2.418-4.697-4.818a2.53 2.53 0 0 1 .052-3.616 2.596 2.596 0 0 1 3.654.046q1.399 1.435 2.804 2.878l1.59-1.666 4.804-5.04.546-.572c.973-1.023 2.596-1.04 3.64-.052 1.043.988 1.101 2.598.12 3.616'
      clipRule='evenodd'
    ></path>
    <path
      fill='#91EA00'
      fillRule='evenodd'
      d='M76.193 66.51q-1.08-1.044-2.166-2.104-.029-1.465-.076-2.934c-.04-1.31-.609-2.559-1.578-3.529s-2.22-1.533-3.53-1.574q-1.47-.046-2.94-.076a514 514 0 0 1-2.106-2.154c-.936-.963-2.153-1.5-3.448-1.5s-2.512.541-3.446 1.5q-1.047 1.077-2.108 2.159-1.47.03-2.939.076c-2.698.092-5.02 2.41-5.112 5.102q-.047 1.468-.077 2.934-1.082 1.06-2.162 2.104c-.96.933-1.5 2.151-1.499 3.437s.536 2.504 1.5 3.44q1.079 1.043 2.161 2.102.03 1.467.077 2.936c.04 1.311.609 2.557 1.577 3.529.967.972 2.223 1.533 3.535 1.573q1.47.047 2.94.077 1.06 1.077 2.105 2.153c.936.962 2.154 1.5 3.451 1.5 1.297-.001 2.512-.537 3.451-1.5q1.046-1.077 2.106-2.157a277 277 0 0 0 2.939-.076c2.698-.092 5.02-2.41 5.113-5.103q.046-1.466.076-2.932a583 583 0 0 1 2.162-2.105c.96-.932 1.5-2.149 1.5-3.44s-.545-2.501-1.506-3.438m-7.228 1.246c-2.922 3.037-5.891 6.14-8.81 9.22a2.54 2.54 0 0 1-1.847.797h-.011a2.6 2.6 0 0 1-1.846-.793q-2.34-2.418-4.697-4.818a2.53 2.53 0 0 1 .052-3.616 2.596 2.596 0 0 1 3.654.046q1.399 1.435 2.804 2.878l1.59-1.666 4.804-5.04.546-.572c.973-1.023 2.596-1.04 3.64-.052 1.043.988 1.101 2.598.12 3.616'
      clipRule='evenodd'
    ></path>
  </svg>
)

export const IconPerson = ({ className }: { className?: string }) => (
  <svg
    xmlns='http://www.w3.org/2000/svg'
    width='18'
    height='17'
    fill='none'
    className={cn(className)}
    viewBox='0 0 18 17'
  >
    <path
      fill='#4A4E5A'
      fillRule='evenodd'
      d='M6.828 11.621c.477 0 .935.189 1.272.524l.9.894.9-.894a1.8 1.8 0 0 1 1.272-.524h3.62c.602 0 1.186.2 1.66.568.475.368.812.883.958 1.463l.56 2.222a.89.89 0 0 1-.331.948.902.902 0 0 1-1.415-.514l-.559-2.221a.89.89 0 0 0-.872-.678h-3.621l-.9.894a1.805 1.805 0 0 1-2.544 0l-.9-.894h-3.62a.9.9 0 0 0-.872.678l-.56 2.221a.89.89 0 0 1-.74.682.9.9 0 0 1-.912-.43.9.9 0 0 1-.093-.686l.558-2.222a2.68 2.68 0 0 1 .958-1.463 2.7 2.7 0 0 1 1.66-.568zM9 0c.705 0 1.494.17 2.141.35 1.496.413 2.357 1.804 2.357 3.223v3.581c0 1.42-.86 2.81-2.357 3.224-.647.178-1.436.35-2.141.35s-1.494-.17-2.141-.35c-1.496-.414-2.357-1.804-2.357-3.224V3.573c0-1.419.86-2.81 2.357-3.223C7.506.17 8.295 0 9 0m0 1.788c-.458 0-1.059.118-1.658.284-.601.166-1.04.757-1.04 1.501v3.581c0 .744.439 1.335 1.04 1.502.6.165 1.2.283 1.658.283s1.059-.118 1.658-.284c.601-.166 1.04-.757 1.04-1.5V3.572c0-.744-.439-1.335-1.04-1.502-.6-.164-1.2-.283-1.658-.283'
      clipRule='evenodd'
    ></path>
  </svg>
)
