"use client"

import SpImage from "@/shared/SpImage/sp-image"
import { motion } from "framer-motion"

export function HeroSection() {
  return (
    <section className='relative h-auto min-h-[650px] bg-gray-150 py-16 md:py-24'>
      <div className='mx-auto px-4'>
        <div className='relative flex flex-col items-center text-center'>
          {/* Decorative Images - Responsive positioning */}
          <div className='absolute left-4 top-0 hidden lg:block xl:left-8 xl:top-8'>
            <SpImage
              src='https://images.sharepal.in/bike-rental/about-hero-left.webp'
              width={500}
              height={500}
              alt='SharePal Logo'
              className='h-auto w-[250px] md:h-auto md:w-[350px]'
            />
          </div>

          <div className='absolute right-4 top-0 hidden lg:block xl:right-8 xl:top-8'>
            <SpImage
              src='https://images.sharepal.in/bike-rental/about-hero-right.webp'
              width={500}
              height={500}
              alt='SharePal Logo'
              className='h-auto w-[250px] md:h-auto md:w-[350px]'
            />
          </div>

          {/* Main Content - Improved typography */}
          <div className='z-10 mx-auto flex w-full max-w-4xl flex-col items-center justify-center gap-3 px-4 py-0 md:py-16'>
            <motion.p
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className='mb-6 text-b6 font-semibold tracking-widest md:text-sh1'
            >
              ABOUT US
            </motion.p>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className='space-y-8 font-ubuntu text-d7 md:text-d3 lg:space-y-2'
            >
              <div className=''>
                Great Experiences <br /> should be accessible to{" "}
                <span className='relative inline-block'>
                  Everyone.
                  <SpImage
                    src='https://images.sharepal.in/carepal/waiver-line.svg'
                    width={250}
                    height={80}
                    alt='Waiver Line'
                    className='inline-block h-auto w-[150px] md:w-[250px]'
                    containerClassName='absolute bottom-[-10px] md:bottom-[-24px]'
                  />
                </span>
              </div>

              <SpImage
                src='https://images.sharepal.in/bike-rental/about-hero-left.webp'
                width={500}
                height={500}
                alt='SharePal Logo'
                className='h-auto w-[250px] md:h-auto md:w-[350px]'
                containerClassName=' block lg:hidden'
              />
            </motion.div>
          </div>
        </div>
      </div>
    </section>
  )
}
