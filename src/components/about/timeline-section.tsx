"use client"

import { cn } from "@/lib/utils"
import { motion } from "framer-motion"

const timelineData = [
  {
    year: "2017",
    title: "The Spark",
    description:
      "It all began with a simple moment: a pal needed a bag to shoot cities. That's when it hit — sharing everyday things could make life lighter.",
    image: "/images/about/timeline-2017.jpg",
  },
  {
    year: "2018",
    title: "The First Rentals",
    description:
      "Starting out of Mumbai, we stood our dreams along with jackets and shoes. That home became our first warehouse. We began renting out travel gear, making adventures affordable for everyone.",
    image: "/images/about/timeline-2018.jpg",
  },
  {
    year: "2019",
    title: "Trust Takes Shape",
    description:
      "Knocked on doors with home trials to earn your trust — and learned from every single customer. Expanded to Delhi & Bangalore with small warehouses.",
    image: "/images/about/timeline-2019.jpg",
  },
  {
    year: "2020",
    title: "A Pause & a Pivot",
    description:
      "COVID froze travel, and we had to shut down most locations. But you found new ways to connect — and understood our gearing gear. We shifted gears and gearing became our next big category. Started shipping across India through courier partners.",
    image: "/images/about/timeline-2020.jpg",
  },
  {
    year: "2021",
    title: "More Cities, More Stories",
    description:
      "We got major demands from Hyderabad and Pune Plus we operated warehouses in these cities as well. Added gaming gear, projectors, and cameras - helping you create, connect, and celebrate.",
    image: "/images/about/timeline-2021.jpg",
  },
  {
    year: "2022",
    title: "Building Even",
    description:
      "From a warehouse office, we finally moved into our own dedicated office space. No longer just a warehouse startup — we became a growing ecosystem of people, ideas, and trust.",
    image: "/images/about/timeline-2022.jpg",
  },
  {
    year: "2023",
    title: "Building a Team, Growing a Voice",
    description:
      "Opened warehouses across five Kolkata, Chennai, and many other cities. Launched our presence on Instagram — to connect, not just sell. Entertainment became our team expanded — marketers, operators, dreamers.",
    image: "/images/about/timeline-2023.jpg",
  },
  {
    year: "2024",
    title: "Sharper, Smarter, Stronger",
    description:
      "We rebranded — with visuals, tone, and experiences centered on trust and transparency. Upgraded logistics, tech, and storytelling — for a smoother SharePal at every touchpoint.",
    image: "/images/about/timeline-2024.jpg",
  },
  {
    year: "2025",
    title: "Beyond Rentals",
    description:
      "Launched bike rentals — another step toward making experiences accessible. With a bolder identity and bigger dreams, we're here to help you live more, own less.",
  },
]

export function TimelineSection() {
  return (
    <section className='bg-gray-100 py-16 md:py-20'>
      <div className='container mx-auto px-4'>
        <div className='mb-16 text-center'>
          <motion.h2
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className='mb-6 text-b6 font-semibold tracking-widest md:text-sh1'
          >
            OUR JOURNEY
          </motion.h2>
        </div>

        <div className='relative mx-auto max-w-4xl lg:max-w-6xl'>
          {/* Timeline Line - Mobile: left side, Desktop: center */}
          <div className='absolute left-6 top-0 h-full w-0.5 bg-primary-300 md:left-1/2 md:-translate-x-1/2'></div>

          {/* Timeline End Dot */}
          <div className='absolute bottom-0 left-6 z-10 h-3 w-3 -translate-x-1/2 rounded-full bg-primary-500 md:left-1/2 md:h-4 md:w-4'></div>

          {timelineData.map((item, index) => (
            <motion.div
              key={item.year}
              initial={{ opacity: 0, y: 50 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              viewport={{ once: true }}
              className={`relative mb-8 flex items-start md:mb-12 ${
                index % 2 === 0 ? "md:flex-row" : "md:flex-row-reverse"
              }`}
            >
              {/* Timeline Dot */}
              <div className='absolute left-6 z-10 h-4 w-4 -translate-x-1/2 rounded-full border-4 border-neutral-100 bg-primary-500 md:left-1/2 md:h-6 md:w-6'></div>

              {/* Content */}
              <div
                className={`ml-12 w-full text-start md:ml-0 md:w-5/12 ${
                  index % 2 === 0
                    ? "md:pr-12 md:text-end"
                    : "text-start md:pl-12"
                }`}
              >
                <div className='hover: rounded-2xl bg-gray-100 p-6 transition-all duration-300 md:p-8'>
                  <div
                    className={cn(
                      "mb-4 flex flex-col gap-2 md:flex-col",
                      // index % 2 === 0 ? "md:items-end" : "items-start",
                    )}
                  >
                    <span className='text-h6 font-bold md:text-h4'>
                      {item.year}
                    </span>
                    <h3 className='text-sh2 font-bold text-primary-500 md:text-h6'>
                      {item.title}
                    </h3>
                  </div>
                  <p className='text-b5 leading-relaxed text-neutral-600 md:text-b3'>
                    {item.description}
                  </p>
                </div>
              </div>

              {/* Image - Hidden on mobile, shown on desktop */}
              {item.image && (
                <div
                  className={`hidden md:block md:w-5/12 ${
                    index % 2 === 0 ? "md:pl-12" : "md:pr-12"
                  }`}
                >
                  <div className='hover: flex h-48 items-center justify-center overflow-hidden rounded-2xl bg-gradient-to-br from-primary-100 to-primary-150 transition-all duration-300 lg:h-64 lg:rounded-3xl'>
                    <div className='text-center'>
                      <span className='text-4xl opacity-40 lg:text-5xl'>
                        📅
                      </span>
                      <p className='mt-2 text-b6 font-medium text-primary-600 lg:text-b5'>
                        {item.year}
                      </p>
                    </div>
                  </div>
                </div>
              )}
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  )
}
