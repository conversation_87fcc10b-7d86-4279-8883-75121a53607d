"use client"

import { motion } from "framer-motion"
import { SharePalLogo } from "../common/logo"

export function OurStorySection() {
  return (
    <section className='bg-secondary-600 px-4 py-16 md:py-24'>
      <div className='container rounded-2xl bg-gray-100 p-8 text-center md:rounded-3xl md:p-12'>
        <div className='mx-auto max-w-2xl'>
          <motion.h2
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className='mb-6 text-b6 font-semibold tracking-widest md:text-sh1'
          >
            OUR STORY
          </motion.h2>

          <motion.p
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            viewport={{ once: true }}
            className='mb-8 text-sh1 leading-relaxed md:text-h3'
          >
            Our founders saw how people longed to travel, create, and explore,
            but the cost and waste of buying gear made it impractical. Thus,
            SharePal became a bridge - offering high-quality lifestyle gear on
            rent, so you can live large without owning big.
          </motion.p>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
            viewport={{ once: true }}
            className='mx-auto mb-6 flex max-w-[200px] items-center justify-center rounded-xl bg-primary-500 p-4'
          >
            <SharePalLogo />
          </motion.div>

          <motion.p
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.6 }}
            viewport={{ once: true }}
            className='mx-auto max-w-lg rounded-xl border border-neutral-200 bg-neutral-100 p-3 text-b6 text-neutral-850 md:text-sh1'
          >
            <b>Pal</b> symbolizes friendship - always by your side, ready to
            equip your next adventure.
          </motion.p>
        </div>
      </div>
    </section>
  )
}
