"use client"

import SpImage from "@/shared/SpImage/sp-image"
import { motion } from "framer-motion"

export function VisionMissionSection() {
  return (
    <section className='bg-gray-100 py-16'>
      <div className='container'>
        <div className='mx-auto grid max-w-6xl gap-6 md:grid-cols-2'>
          <ValuesCardNew
            title='Our Vision'
            description='To enable a world where shared ownership and people can live fuller, freer, and more conscious lives through shared experiences.'
            image='https://images.sharepal.in/common-icons/our-vision.svg'
          />

          <ValuesCardNew
            title='Our Mission'
            description='To empower everyone to access premium experiences by making premium gear accessible, affordable, and sustainable - helping them turn everyday moments into lasting memories.'
            image='https://images.sharepal.in/common-icons/our-mission.svg'
          />
        </div>
      </div>
    </section>
  )
}

interface ValuesCardNewProps {
  title: string
  description: string
  image: string
}

export const ValuesCardNew = ({
  title,
  description,
  image,
}: ValuesCardNewProps) => {
  return (
    <motion.div
      initial={{ opacity: 0, x: -50 }}
      whileInView={{ opacity: 1, x: 0 }}
      transition={{ duration: 0.6 }}
      viewport={{ once: true }}
      className='rounded-2xl border border-neutral-200 bg-gray-100 p-6 text-center md:rounded-3xl'
    >
      <div className='w-full space-y-3'>
        <div className='md:w-18 mx-auto flex w-20 items-center justify-center rounded-2xl md:h-24'>
          <SpImage
            src={image}
            width={100}
            height={100}
            alt={title}
            className='h-auto md:w-full'
          />
        </div>
        <h4 className='mb-4 text-h4 text-neutral-900 md:text-h1'>{title}</h4>
        <p className='text-b4 leading-relaxed text-neutral-400 md:text-sh1'>
          {description}
        </p>
      </div>
    </motion.div>
  )
}
