"use client"

import SpImage from "@/shared/SpImage/sp-image"
import { motion } from "framer-motion"

const values = [
  {
    image: "https://images.sharepal.in/common-icons/new-start-box.svg",
    title: "Access Over Ownership",
    description: "Experience everything you need, when you need it.",
  },
  {
    image: "https://images.sharepal.in/common-icons/handshake.svg",
    title: "Trust as a Service",
    description: "Gear that's always clean, safe, and dependable.",
  },
  {
    image: "https://images.sharepal.in/common-icons/sustainable.svg",
    title: "Sustainability in Action",
    description: "Share more, waste less, and lower your footprint.",
  },
  {
    image: "https://images.sharepal.in/common-icons/group.svg",
    title: "Community of Explorers",
    description:
      "We support not just rentals but dreams, creativity & stories.",
  },
  {
    image: "https://images.sharepal.in/common-icons/happy-emoji.svg",
    title: "Joy in the Journey",
    description: "We enable you to enjoy the moment, no worry about gear.",
  },
]

export function ValuesSection() {
  return (
    <section className='bg-neutral-150 py-16'>
      <div className='container'>
        <div className='mb-12 text-center'>
          <motion.h2
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className='mb-6 text-b6 font-semibold tracking-widest md:text-sh1'
          >
            OUR VALUES
          </motion.h2>
        </div>

        <div className='mx-auto grid grid-cols-1 gap-4 md:grid-cols-3 lg:grid-cols-5'>
          {values.map((value, index) => (
            <motion.div
              key={value.title}
              initial={{ opacity: 0 }}
              whileInView={{ opacity: 1 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              viewport={{ once: true }}
              className='rounded-2xl bg-gray-100 p-4 text-center transition-all duration-300 hover:-translate-y-1 md:rounded-3xl'
            >
              <div
                className={`mx-auto mb-4 flex w-20 items-center justify-center rounded-2xl md:mb-6 md:w-24 lg:rounded-3xl`}
              >
                <SpImage
                  src={value.image}
                  width={100}
                  height={100}
                  alt={value.title}
                  className='h-auto md:w-full'
                />
              </div>

              <h3 className='mb-3 px-6 text-h5 font-bold text-neutral-800 md:text-h6'>
                {value.title}
              </h3>

              <p className='text-b5 leading-relaxed text-neutral-600 md:text-b3'>
                {value.description}
              </p>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  )
}
