"use client"

import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  fetchAvailableHub,
  transformAvailableHubsForSelector,
} from "@/services/bikes"
import { useBikeRentalStore } from "@/store/bike-rental-store"
import { useCityStore } from "@/store/city-store"
import { useQuery } from "@tanstack/react-query"
import { MapPin } from "lucide-react"
import { useState } from "react"
import { HubSelectorSkeleton } from "../skeletons"

interface HubSelectorProps {
  bike_code: string
  selectedHubCode?: string
  onHubSelect: (hubCode: string) => void
  className?: string
}

export const HubSelector: React.FC<HubSelectorProps> = ({
  bike_code,
  selectedHubCode,
  onHubSelect,
  className,
}) => {
  const [isOpen, setIsOpen] = useState(false)

  // Get data from stores
  const { selectedCity } = useCityStore()
  const { pickup_time, dropoff_time } = useBikeRentalStore()

  // Use TanStack Query for hub fetching
  const {
    data: hubs = [],
    isLoading,
    refetch,
  } = useQuery({
    queryKey: [
      "availableHubs",
      pickup_time,
      dropoff_time,
      selectedCity.city_url,
      bike_code,
    ],
    queryFn: () =>
      fetchAvailableHub({
        pickup_time: pickup_time || "",
        dropoff_time: dropoff_time || "",
        city_url: selectedCity.city_url,
        bike_code,
      }),
    enabled: false, // Only fetch when manually triggered
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus: false,
  })

  // Transform hubs for selector component
  const { fullyAvailableHubs, partiallyAvailableHubs } =
    transformAvailableHubsForSelector(hubs)

  const hasHubs = hubs.length > 0
  const selectedHub = [...fullyAvailableHubs, ...partiallyAvailableHubs].find(
    (hub) => hub.hub_code === selectedHubCode,
  )

  // Fetch hubs when dropdown is opened for the first time
  const handleOpenChange = (open: boolean) => {
    setIsOpen(open)
    if (
      open &&
      hubs.length === 0 &&
      !isLoading &&
      pickup_time &&
      dropoff_time
    ) {
      refetch()
    }
  }

  // Show message if pickup/dropoff times are not set
  if (!pickup_time || !dropoff_time) {
    return (
      <div className='flex items-center gap-2 text-xs text-gray-500'>
        <MapPin className='h-4 w-4' />
        <span>Please select pickup and dropoff times first</span>
      </div>
    )
  }

  // if (isLoading) {
  //   return <HubSelectorSkeleton />
  // }

  return (
    <div className={className}>
      <Select
        value={selectedHubCode}
        onValueChange={(value) => onHubSelect(value)}
        open={isOpen}
        onOpenChange={handleOpenChange}
      >
        <SelectTrigger className='w-full'>
          <SelectValue placeholder='Select Pickup Location'>
            {selectedHub && (
              <div className='flex items-center gap-2'>
                <MapPin className='h-4 w-4 text-bike-600' />
                <span>{selectedHub.name}</span>
                {/* <span
                  className={`rounded-full px-2 py-0.5 text-xs ${
                    selectedHub.availability === "full"
                      ? "bg-green-100 text-green-700"
                      : "bg-yellow-100 text-yellow-700"
                  }`}
                >
                  {selectedHub.availability === "full"
                    ? "Fully Available"
                    : "Limited"}
                </span> */}
              </div>
            )}
          </SelectValue>
        </SelectTrigger>
        <SelectContent>
          {isLoading ? (
            <HubSelectorSkeleton />
          ) : (
            fullyAvailableHubs.length > 0 && (
              <>
                <div className='px-2 py-1.5 text-xs font-semibold uppercase tracking-wide text-gray-500'>
                  Fully Available
                </div>
                {fullyAvailableHubs.map((hub) => (
                  <SelectItem key={hub.hub_code} value={hub.hub_code}>
                    <div className='flex items-center gap-2'>
                      <MapPin className='h-4 w-4 text-green-600' />
                      <span>{hub.name}</span>
                      <span className='rounded-full bg-green-100 px-2 py-0.5 text-xs text-green-700'>
                        Available
                      </span>
                    </div>
                  </SelectItem>
                ))}
              </>
            )
          )}

          {partiallyAvailableHubs.length > 0 && (
            <>
              {fullyAvailableHubs.length > 0 && (
                <div className='my-1 border-t' />
              )}
              {/* <div className='px-2 py-1.5 text-xs font-semibold uppercase tracking-wide text-gray-500'>
                Limited Availability
              </div> */}
              {partiallyAvailableHubs.map((hub) => (
                <SelectItem key={hub.hub_code} value={hub.hub_code}>
                  <div className='flex items-center gap-2'>
                    <MapPin className='h-4 w-4 text-yellow-600' />
                    <span>{hub.name}</span>
                    {/* <span className='rounded-full bg-yellow-100 px-2 py-0.5 text-xs text-yellow-700'>
                      Limited
                    </span> */}
                  </div>
                </SelectItem>
              ))}
            </>
          )}
        </SelectContent>
      </Select>
    </div>
  )
}
