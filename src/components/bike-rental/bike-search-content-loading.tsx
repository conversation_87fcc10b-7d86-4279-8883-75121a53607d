import { BikeCardSkeleton, SidebarFilterSkeleton } from "@/components/skeletons"

export const BikeSearchContentLoading: React.FC = () => {
  return (
    <div className='container mx-auto px-4 py-6 lg:py-8'>
      {/* Mobile Filter Toggle Skeleton */}
      <div className='mb-6 lg:hidden'>
        <div className='h-10 w-full animate-pulse rounded-lg bg-white shadow-sm' />
      </div>

      <div className='flex flex-col gap-6 lg:flex-row lg:gap-8'>
        {/* Sidebar Filters Skeleton */}
        <div className='lg:w-1/3'>
          <div className='sticky top-4'>
            <SidebarFilterSkeleton />
          </div>
        </div>

        {/* Results Skeleton */}
        <div className='lg:w-3/4'>
          {/* <div className='mb-6 rounded-lg bg-white p-4 shadow-sm lg:p-6'>
            <FilterHeaderSkeleton />
          </div> */}

          {/* Bike Cards Grid Skeleton */}
          <div className='grid grid-cols-1 gap-4 sm:grid-cols-2 lg:gap-6 xl:grid-cols-2 2xl:grid-cols-3'>
            {Array.from({ length: 6 }).map((_, index) => (
              <BikeCardSkeleton key={index} />
            ))}
          </div>
        </div>
      </div>
    </div>
  )
}
