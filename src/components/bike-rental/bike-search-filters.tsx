// "use client"
// import { But<PERSON> } from "@/components/ui/button"
// import { Checkbox } from "@/components/ui/checkbox"
// import { Label } from "@/components/ui/label"
// import {
//   Select,
//   SelectContent,
//   SelectItem,
//   SelectTrigger,
//   SelectValue,
// } from "@/components/ui/select"
// import { Separator } from "@/components/ui/separator"
// import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs"
// import { Typography } from "@/components/ui/typography"
// import { FilterState } from "@/types/bike-search"
// import { Bike } from "@/types/common"
// import { DollarSign, Filter, MapPin, Star, X, Zap } from "lucide-react"
// import { useEffect, useMemo, useState } from "react"

// interface BikeSearchFiltersProps {
//   bikes: Bike[]
//   onFiltersChange: (filteredBikes: Bike[]) => void
// }

// const availabilityOptions = [
//   { value: "all", label: "All Bikes" },
//   { value: "fully_available", label: "Fully Available" },
//   { value: "partially_available", label: "Limited Availability" },
// ]

// export const BikeSearchFilters: React.FC<BikeSearchFiltersProps> = ({
//   bikes,
//   onFiltersChange,
// }) => {
//   const [filters, setFilters] = useState<FilterState>({
//     bikeCategory: "all",
//     priceSort: "",
//     availabilityFilter: "all",
//     hubLocations: [],
//   })

//   const uniqueHubLocations = useMemo(() => {
//     return Array.from(
//       new Set(
//         bikes.flatMap((bike) => [
//           ...bike.fully_available_hubs.map((hub) => hub.hub_name),
//           ...bike.partially_available_hubs.map((hub) => hub.hub_name),
//         ]),
//       ),
//     )
//   }, [bikes])

//   useEffect(() => {
//     let filtered = [...bikes]

//     if (filters.bikeCategory === "electric") {
//       filtered = filtered.filter(
//         (bike) => bike.fuel_type?.toLowerCase() === "electric",
//       )
//     }

//     if (filters.availabilityFilter !== "all") {
//       filtered = filtered.filter((bike) => {
//         if (filters.availabilityFilter === "fully_available") {
//           return bike.fully_available_hubs.length > 0
//         } else if (filters.availabilityFilter === "partially_available") {
//           return bike.partially_available_hubs.length > 0
//         }
//         return true
//       })
//     }

//     if (filters.hubLocations.length > 0) {
//       filtered = filtered.filter((bike) => {
//         const bikeHubNames = [
//           ...bike.fully_available_hubs.map((hub) => hub.hub_name),
//           ...bike.partially_available_hubs.map((hub) => hub.hub_name),
//         ]
//         return filters.hubLocations.some((location) =>
//           bikeHubNames.includes(location),
//         )
//       })
//     }

//     if (filters.priceSort !== "") {
//       filtered.sort((a, b) => {
//         const priceA = a.pricing?.price_per_hour || 0
//         const priceB = b.pricing?.price_per_hour || 0
//         return filters.priceSort === "low_to_high"
//           ? priceA - priceB
//           : priceB - priceA
//       })
//     }

//     onFiltersChange(filtered)
//   }, [filters, bikes, onFiltersChange])

//   const handleBikeCategoryChange = (category: "all" | "electric") => {
//     setFilters((prev) => ({ ...prev, bikeCategory: category }))
//   }

//   const handlePriceSortChange = (sort: "" | "low_to_high" | "high_to_low") => {
//     setFilters((prev) => ({ ...prev, priceSort: sort }))
//   }

//   const handleAvailabilityFilterChange = (value: string) => {
//     setFilters((prev) => ({
//       ...prev,
//       availabilityFilter: value as FilterState["availabilityFilter"],
//     }))
//   }

//   const handleHubLocationChange = (hubName: string, checked: boolean) => {
//     setFilters((prev) => ({
//       ...prev,
//       hubLocations: checked
//         ? [...prev.hubLocations, hubName]
//         : prev.hubLocations.filter((location) => location !== hubName),
//     }))
//   }

//   const clearAllFilters = () => {
//     setFilters({
//       bikeCategory: "all",
//       priceSort: "",
//       availabilityFilter: "all",
//       hubLocations: [],
//     })
//   }

//   const hasActiveFilters = useMemo(() => {
//     return (
//       filters.bikeCategory !== "all" ||
//       filters.priceSort !== "" ||
//       filters.availabilityFilter !== "all" ||
//       filters.hubLocations.length > 0
//     )
//   }, [filters])

//   return (
//     <div className='rounded-2xl bg-gray-100 p-4 shadow-sm sm:rounded-2xl sm:p-6'>
//       <div className='mb-4 flex items-center justify-between sm:mb-6'>
//         <div className='flex min-h-8 items-center gap-2'>
//           <Filter className='h-4 w-4 text-bike-600 sm:h-5 sm:w-5' />
//           <Typography
//             as='h3'
//             className='font-ubuntu text-sh3 text-gray-900 sm:text-h5'
//           >
//             Filters
//           </Typography>
//         </div>
//         {hasActiveFilters && (
//           <Button
//             onClick={clearAllFilters}
//             variant='ghost'
//             size='sm'
//             className='text-bike-600 hover:text-bike-700'
//           >
//             <X className='size-4' />
//             Clear All
//           </Button>
//         )}
//       </div>
//       <div className='space-y-4 sm:space-y-6'>
//         <div>
//           <Typography
//             as='h4'
//             className='mb-2 text-sh4 font-medium text-gray-900 sm:mb-3'
//           >
//             Bike Category
//           </Typography>
//           <Tabs
//             value={filters.bikeCategory}
//             onValueChange={(value) =>
//               handleBikeCategoryChange(value as "all" | "electric")
//             }
//             className='w-full rounded-3xl'
//           >
//             <TabsList className='grid h-12 w-full grid-cols-2 rounded-3xl px-2'>
//               <TabsTrigger
//                 value='all'
//                 className='flex items-center gap-1 rounded-3xl py-2 text-b5 sm:gap-2 sm:text-b4'
//               >
//                 <Star className='h-3 w-3 sm:h-4 sm:w-4' />
//                 All Models
//               </TabsTrigger>
//               <TabsTrigger
//                 value='electric'
//                 className='flex items-center gap-1 rounded-3xl py-2 text-b5 sm:gap-2 sm:text-b4'
//               >
//                 <Zap className='h-3 w-3 sm:h-4 sm:w-4' />
//                 Electric
//               </TabsTrigger>
//             </TabsList>
//           </Tabs>
//         </div>

//         <div>
//           <Typography
//             as='h4'
//             className='mb-2 text-sh4 font-medium text-gray-900 sm:mb-3'
//           >
//             <div className='flex items-center gap-1 sm:gap-2'>
//               <DollarSign className='h-3 w-3 text-bike-600 sm:h-4 sm:w-4' />
//               Price Sorting
//             </div>
//           </Typography>
//           <Select
//             value={filters.priceSort}
//             onValueChange={(value) =>
//               handlePriceSortChange(value as "" | "low_to_high" | "high_to_low")
//             }
//           >
//             <SelectTrigger className='h-9 w-full text-b5 sm:h-10 sm:text-b4'>
//               <SelectValue placeholder='Sort by price' />
//             </SelectTrigger>
//             <SelectContent>
//               <SelectItem value='low_to_high' className='text-b5 sm:text-b4'>
//                 Price: Low to High
//               </SelectItem>
//               <SelectItem value='high_to_low' className='text-b5 sm:text-b4'>
//                 Price: High to Low
//               </SelectItem>
//             </SelectContent>
//           </Select>
//         </div>

//         <Separator />

//         <div>
//           <Typography
//             as='h4'
//             className='mb-2 text-sh4 font-medium text-gray-900 sm:mb-3'
//           >
//             Availability
//           </Typography>
//           <div className='space-y-2 sm:space-y-3'>
//             {availabilityOptions.map((option) => (
//               <div
//                 key={option.value}
//                 className='flex items-center space-x-2 sm:space-x-3'
//               >
//                 <Checkbox
//                   id={`availability-${option.value}`}
//                   checked={filters.availabilityFilter === option.value}
//                   onCheckedChange={() =>
//                     handleAvailabilityFilterChange(option.value)
//                   }
//                   className='h-4 w-4 sm:h-5 sm:w-5'
//                 />
//                 <Label
//                   htmlFor={`availability-${option.value}`}
//                   className='cursor-pointer text-b5 text-gray-700 sm:text-b4'
//                 >
//                   {option.label}
//                 </Label>
//               </div>
//             ))}
//           </div>
//         </div>

//         {uniqueHubLocations.length > 0 && (
//           <div>
//             <Typography as='h4' className='mb-2 text-sh4 text-gray-900 sm:mb-3'>
//               <div className='flex items-center gap-1 sm:gap-2'>
//                 <MapPin className='h-3 w-3 text-bike-600 sm:h-4 sm:w-4' />
//                 Pickup Locations
//               </div>
//             </Typography>
//             <div className='max-h-40 space-y-2 overflow-y-auto border-gray-100 sm:max-h-40 sm:space-y-3'>
//               {uniqueHubLocations.map((hubName) => (
//                 <div
//                   key={hubName}
//                   className='flex items-center space-x-2 sm:space-x-3'
//                 >
//                   <Checkbox
//                     id={`hub-${hubName}`}
//                     checked={filters.hubLocations.includes(hubName)}
//                     onCheckedChange={(checked) =>
//                       handleHubLocationChange(hubName, checked as boolean)
//                     }
//                     className='h-4 w-4 sm:h-5 sm:w-5'
//                   />
//                   <Label
//                     htmlFor={`hub-${hubName}`}
//                     className='cursor-pointer text-b5 text-gray-700 sm:text-b4'
//                   >
//                     {hubName}
//                   </Label>
//                 </div>
//               ))}
//             </div>
//           </div>
//         )}
//       </div>
//     </div>
//   )
// }

export const BikeSearchFilters = () => {
  return <div>BikeSearchFilters</div>
}
