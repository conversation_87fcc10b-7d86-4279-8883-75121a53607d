import { Button } from "@/components/ui/button"
import { Typography } from "@/components/ui/typography"
import { memo } from "react"

interface NoResultsFoundProps {
  onSearchAgain: () => void
}

export const NoResultsFound = memo<NoResultsFoundProps>(({ onSearchAgain }) => (
  <div className='rounded-xl bg-gray-100 p-6 shadow-sm sm:p-8 lg:p-12'>
    <div className='text-center'>
      <div className='mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-gray-100 sm:mb-6 sm:h-20 sm:w-20'>
        <svg
          className='h-8 w-8 text-gray-400 sm:h-10 sm:w-10'
          fill='none'
          stroke='currentColor'
          viewBox='0 0 24 24'
        >
          <path
            strokeLinecap='round'
            strokeLinejoin='round'
            strokeWidth={1.5}
            d='M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z'
          />
        </svg>
      </div>
      <Typography
        as='h3'
        className='mb-2 font-ubuntu text-h5 text-gray-900 sm:mb-3 sm:text-h4'
      >
        No bikes found
      </Typography>
      <Typography
        as='p'
        className='mx-auto mb-6 max-w-md text-b4 text-gray-600 sm:mb-8 sm:text-b3'
      >
        We couldn&apos;t find any bikes matching your criteria. Try adjusting
        your filters or search for a different time.
      </Typography>
      <Button
        onClick={onSearchAgain}
        className='w-full bg-bike-600 px-6 py-3 text-b3 font-medium text-white hover:bg-bike-700 sm:w-auto sm:py-2'
      >
        Search Again
      </Button>
    </div>
  </div>
))

NoResultsFound.displayName = "NoResultsFound"
