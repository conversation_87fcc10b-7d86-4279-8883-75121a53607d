"use client"

import DialogToDrawer from "@/components/custom/dialog-to-drawer"
import { DateSelector } from "@/components/ui/date-selector"
import { TimeSelector } from "@/components/ui/time-selector"
import { useBikeRentalHandlers } from "@/hooks/use-bike-rental-handlers"
import { createBikeRentalSearchUrl } from "@/lib/bike-rental-url-utils"
import { useBikeRentalStore } from "@/store/bike-rental-store"
import { useCityStore } from "@/store/city-store"
import { isValid } from "date-fns"
import Link from "next/link"
import { useRouter } from "next/navigation"
import React from "react"
import { toast } from "sonner"
import { Button } from "../ui/button"

export const BikeRentalSelector: React.FC = ({}) => {
  const router = useRouter()
  const { selectedCity } = useCityStore()
  const { isSelectorOpen, setSelectorOpen, closeSelector } =
    useBikeRentalStore()

  const {
    pickup_date,
    dropoff_date,
    pickup_time,
    dropoff_time,
    handleDateSelect,
    handleTimeSelect,
    getTimeSlots,
    isDateValid,
    isDropoffDateValid,
    validateRental,
    getTimestamps,
  } = useBikeRentalHandlers()

  const handleSearch = () => {
    const { pickup_timestamp, dropoff_timestamp } = getTimestamps()
    if (!pickup_timestamp || !dropoff_timestamp) {
      toast.error("Failed to generate valid timestamps. Please try again.")
      return
    }

    // Get city URL from selectedCity, fallback to 'bangalore' if not available
    const cityUrl = selectedCity?.city_url || "bangalore"

    // Create search URL and navigate (let the search page handle validation errors)
    const searchUrl = createBikeRentalSearchUrl(
      cityUrl,
      pickup_timestamp,
      dropoff_timestamp,
    )

    // Close dialog and navigate
    closeSelector()
    router.push(searchUrl)
  }

  // Check if all fields are selected (allow search even if validation fails)
  const canContinue = React.useMemo(() => {
    if (!pickup_date || !dropoff_date || !pickup_time || !dropoff_time) {
      return false
    }

    if (!isValid(pickup_date) || !isValid(dropoff_date)) {
      return false
    }

    // Allow search even if validation fails - let the search page handle the error
    return true
  }, [pickup_date, dropoff_date, pickup_time, dropoff_time])

  // Generate search URL for the Link
  const searchUrl = React.useMemo(() => {
    if (!canContinue) return "#"

    const { pickup_timestamp, dropoff_timestamp } = getTimestamps()
    if (!pickup_timestamp || !dropoff_timestamp) return "#"

    const cityUrl = selectedCity?.city_url || "bangalore"
    return createBikeRentalSearchUrl(
      cityUrl,
      pickup_timestamp,
      dropoff_timestamp,
    )
  }, [canContinue, getTimestamps, selectedCity?.city_url])

  return (
    <DialogToDrawer
      open={isSelectorOpen}
      onOpenChange={setSelectorOpen}
      onContinue={handleSearch}
      continueDisabled={!canContinue}
      continueText='Search'
      footer={
        <div className='flex justify-end'>
          <Button
            onClick={handleSearch}
            asChild
            disabled={!canContinue}
            className='w-full bg-bike-600 text-white hover:bg-bike-700 disabled:opacity-50'
          >
            <Link href={searchUrl}>Search</Link>
          </Button>
        </div>
      }
    >
      <div className='grid grid-cols-1 gap-3 md:gap-6'>
        {/* First Row - Pickup Date and Time */}
        <div className='grid grid-cols-1 gap-3 md:grid-cols-2 md:gap-6'>
          <DateSelector
            label='Pickup Date'
            placeholder='Select pickup date'
            value={pickup_date}
            onValueChange={(date) => handleDateSelect(date, "pickup")}
            disabled={(date) => !isDateValid(date)}
            dateFormat='MMM dd, yyyy'
          />

          <TimeSelector
            label='Pickup Time'
            placeholder='Select pickup time'
            value={pickup_time}
            onValueChange={(time) => handleTimeSelect(time, "pickup")}
            timeSlots={getTimeSlots("pickup")}
            disabled={!pickup_date}
          />
        </div>

        {/* Second Row - Dropoff Date and Time */}
        <div className='grid grid-cols-1 gap-3 md:grid-cols-2 md:gap-6'>
          <DateSelector
            label='Drop-off Date'
            placeholder='Select drop-off date'
            value={dropoff_date}
            onValueChange={(date) => handleDateSelect(date, "dropoff")}
            disabled={(date) => !isDropoffDateValid(date)}
            dateFormat='MMM dd, yyyy'
          />

          <TimeSelector
            label='Drop-off Time'
            placeholder='Select drop-off time'
            value={dropoff_time}
            onValueChange={(time) => handleTimeSelect(time, "dropoff")}
            timeSlots={getTimeSlots("dropoff")}
            disabled={!dropoff_date}
          />
        </div>
      </div>
    </DialogToDrawer>
  )
}
