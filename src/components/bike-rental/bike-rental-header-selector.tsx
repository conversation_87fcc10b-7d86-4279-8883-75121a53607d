// "use client"

// import { Typography } from "@/components/ui/typography"
// import { cn } from "@/lib/utils"
// import { XIcon } from "lucide-react"
// import React, { useState } from "react"
// import { BikeRentalInputs } from "./bike-rental-inputs"

// interface BikeRentalHeaderSelectorProps {
//   selectedCity: any
// }

// export const BikeRentalHeaderSelector: React.FC<
//   BikeRentalHeaderSelectorProps
// > = ({ selectedCity }) => {
//   const [isMobileDrawerOpen, setIsMobileDrawerOpen] = useState(false)

//   const handleMobileSearchClick = () => {
//     setIsMobileDrawerOpen(false)
//   }

//   return (
//     <>
//       {/* Desktop Version */}
//       <div className='hidden w-full rounded-b-3xl border-t border-neutral-200 bg-gradient-to-b from-gray-50 to-bike-200 py-2 pb-4 lg:block'>
//         <div className='container'>
//           <BikeRentalInputs isDesktop={true} selectedCity={selectedCity} />
//         </div>
//       </div>

//       {/* Mobile Drawer */}
//       {isMobileDrawerOpen && (
//         <>
//           {/* Backdrop with blur */}
//           <div
//             className='fixed inset-0 z-50 bg-black/50 backdrop-blur-sm lg:hidden'
//             onClick={() => setIsMobileDrawerOpen(false)}
//           />

//           {/* Drawer Content - Coming from top */}
//           <div
//             className={cn(
//               "fixed left-4 right-4 top-20 z-50 rounded-2xl bg-white p-6 shadow-xl transition-all duration-300 lg:hidden",
//               isMobileDrawerOpen
//                 ? "translate-y-0 opacity-100"
//                 : "-translate-y-full opacity-0",
//             )}
//           >
//             {/* Header */}
//             <div className='mb-6 flex items-center justify-between'>
//               <Typography as='h3' className='text-h5 font-semibold'>
//                 Select Rental Times
//               </Typography>
//             </div>

//             {/* Date/Time Inputs with Search Button */}
//             <div className='space-y-4'>
//               <BikeRentalInputs
//                 isDesktop={false}
//                 selectedCity={selectedCity}
//                 onSearchClick={handleMobileSearchClick}
//               />
//             </div>

//             {/* Floating Close Button */}
//             <button
//               onClick={() => setIsMobileDrawerOpen(false)}
//               className='absolute -bottom-16 left-1/2 flex h-12 w-12 -translate-x-1/2 items-center justify-center rounded-full bg-white shadow-lg'
//             >
//               <XIcon className='h-6 w-6 text-gray-600' />
//             </button>
//           </div>
//         </>
//       )}
//     </>
//   )
// }

const Dummy = () => {
  return <div>Dummy</div>
}

export default Dummy
