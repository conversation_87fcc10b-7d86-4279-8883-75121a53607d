"use client"

import { Typography } from "@/components/ui/typography"
import {
  formatDurationFromHours,
  formatTimestampToDateTime,
  getRelativeTimeDescription,
} from "@/functions/date"
import { useBikeRentalStore } from "@/store/bike-rental-store"
import { Calendar, Clock, EditIcon } from "lucide-react"
import { memo, useMemo } from "react"
import { Button } from "../ui/button"
import { BikeSearchError } from "./bike-search-error"

interface BikeSearchHeaderProps {
  city: string
  pickup_timestamp: number
  dropoff_timestamp: number
  total_hours: number
  bikeCount?: number
  isLoading?: boolean
  error?: string
  onRetry?: () => void
}

export const BikeSearchHeader = memo<BikeSearchHeaderProps>(
  ({
    city,
    pickup_timestamp,
    dropoff_timestamp,
    total_hours,
    bikeCount = 0,
    isLoading = false,
    error,
    onRetry,
  }) => {
    const { isSelectorOpen, openSelector } = useBikeRentalStore()
    // Memoized formatted values for performance
    const formattedPickupTime = useMemo(
      () => formatTimestampToDateTime(pickup_timestamp, { includeYear: true }),
      [pickup_timestamp],
    )

    const formattedDropoffTime = useMemo(
      () => formatTimestampToDateTime(dropoff_timestamp, { includeYear: true }),
      [dropoff_timestamp],
    )

    const formattedDuration = useMemo(
      () => formatDurationFromHours(total_hours),
      [total_hours],
    )

    const relativePickupTime = useMemo(
      () => getRelativeTimeDescription(pickup_timestamp),
      [pickup_timestamp],
    )

    const capitalizedCity = useMemo(
      () => city.charAt(0).toUpperCase() + city.slice(1),
      [city],
    )

    // Loading state component
    if (isLoading) {
      return (
        <div className='border-b border-gray-200 bg-white shadow-sm'>
          <div className='container py-6'>
            <div className='animate-pulse'>
              <div className='mb-4 h-8 w-3/4 rounded bg-gray-200'></div>
              <div className='grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3'>
                {[1, 2, 3].map((i) => (
                  <div key={i} className='rounded-lg bg-gray-50 p-3'>
                    <div className='mb-2 h-4 w-16 rounded bg-gray-200'></div>
                    <div className='h-5 w-32 rounded bg-gray-200'></div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      )
    }

    // Error state component
    if (error) {
      return (
        <div className='border-b border-red-100 bg-red-50 shadow-sm'>
          <div className='container py-6'>
            <BikeSearchError
              error={error}
              onRetry={onRetry}
              retryable={!!onRetry}
            />
          </div>
        </div>
      )
    }

    // Main content
    return (
      <>
        <div className='mb-4 rounded-2xl border border-gray-200 bg-white p-4 shadow-sm md:mb-6 md:p-6'>
          {/* Search Summary */}
          <div className='mb-4 flex flex-col items-start justify-between gap-4 md:flex-row md:items-center'>
            <div className='w-full'>
              <Typography
                as='h1'
                className='font-ubuntu text-h5 text-gray-900 sm:text-h4 lg:text-h3'
              >
                Available Bikes in{" "}
                <span className='text-bike-600'>{capitalizedCity}</span>
              </Typography>
              {/* Bike count and duration information */}
              <div className='mt-2 flex flex-col gap-1 sm:flex-row sm:gap-4'>
                <Typography as='p' className='text-b5 text-gray-600 sm:text-b4'>
                  {bikeCount} bike{bikeCount !== 1 ? "s" : ""} available
                </Typography>
                <Typography as='p' className='text-b5 text-gray-500 sm:text-b4'>
                  • {formattedDuration} rental
                </Typography>
              </div>
            </div>

            {/* Edit Button */}
            <div className='flex-shrink-0'>
              <Button
                onClick={openSelector}
                variant='outline'
                className='hover:bg-bike-50 flex items-center gap-2 border-bike-600 text-bike-600'
              >
                <EditIcon className='h-4 w-4' />
                Modify Search
              </Button>
            </div>
          </div>

          <div className='grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3'>
            {/* Pickup */}
            <div className='flex items-center gap-3 rounded-lg bg-gray-50 p-3'>
              <div className='flex h-7 w-7 items-center justify-center rounded-full bg-bike-100'>
                <Calendar className='h-3.5 w-3.5 text-bike-600' />
              </div>
              <div className='min-w-0 flex-1'>
                <Typography as='div' className='mb-0.5 text-sh5 text-gray-900'>
                  Pickup
                </Typography>
                <Typography
                  as='div'
                  className='text-b6 font-medium text-gray-700'
                >
                  {formattedPickupTime}
                </Typography>
              </div>
            </div>

            {/* Dropoff */}
            <div className='flex items-center gap-3 rounded-lg bg-gray-50 p-3'>
              <div className='flex h-7 w-7 items-center justify-center rounded-full bg-bike-100'>
                <Calendar className='h-3.5 w-3.5 text-bike-600' />
              </div>
              <div className='min-w-0 flex-1'>
                <Typography as='div' className='mb-0.5 text-sh5 text-gray-900'>
                  Drop-off
                </Typography>
                <Typography
                  as='div'
                  className='text-b6 font-medium text-gray-700'
                >
                  {formattedDropoffTime}
                </Typography>
              </div>
            </div>

            {/* Duration */}
            <div className='flex items-center gap-3 rounded-lg bg-gray-50 p-3 sm:col-span-2 lg:col-span-1'>
              <div className='flex h-7 w-7 items-center justify-center rounded-full bg-bike-100'>
                <Clock className='h-3.5 w-3.5 text-bike-600' />
              </div>
              <div className='min-w-0 flex-1'>
                <Typography as='div' className='mb-0.5 text-sh5 text-gray-900'>
                  Duration
                </Typography>
                <Typography
                  as='div'
                  className='text-b6 font-medium text-gray-700'
                >
                  {formattedDuration}
                </Typography>
              </div>
            </div>
          </div>
        </div>
      </>
    )
  },
)

BikeSearchHeader.displayName = "BikeSearchHeader"
