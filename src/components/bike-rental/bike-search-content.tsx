import { BikeGrid } from "@/components/bike-rental/bike-grid"
import { NoResultsFound } from "@/components/bike-rental/no-results-found"
import SpImage from "@/shared/SpImage/sp-image"
import { Bike } from "@/types/common"
// import { Bike } from "@/types/common"
import { memo } from "react"

interface BikeSearchContentProps {
  bikes: Bike[]
  filteredBikes: Bike[]
  showMobileFilters: boolean
  onToggleMobileFilters: () => void
  onFiltersChange: (filtered: Bike[]) => void
  // total_hours?: number
  city: string
}

export const BikeSearchContent = memo<BikeSearchContentProps>(
  ({
    // bikes,
    // showMobileFilters,
    // onToggleMobileFilters,
    // onFiltersChange,
    filteredBikes,
    // total_hours,
  }) => {
    return (
      <>
        {/* Mobile Filter Button */}
        {/* <div className='lg:hidden'>
          <Button
            onClick={onToggleMobileFilters}
            variant='outline'
            className='fixed bottom-6 right-3 z-10 flex h-12 w-12 items-center justify-center gap-2 rounded-full border-bike-500 bg-bike-150 py-3 shadow-sm hover:bg-gray-50'
          >
            <SlidersHorizontal className='h-4 w-4 text-bike-600' />
            <Typography
              as='span'
              className='sr-only text-b3 font-medium text-gray-700'
            >
              Filters & Sort
            </Typography>
          </Button>
        </div> */}

        {/* Mobile Filter Drawer */}
        {/* <Drawer open={showMobileFilters} onOpenChange={onToggleMobileFilters}>
          <DrawerContent className='max-h-[85vh]'>
            <DrawerHeader className='border-b border-gray-200 pb-4'>
              <DrawerTitle className='flex items-center gap-2'>
                <SlidersHorizontal className='h-5 w-5 text-bike-600' />
                <Typography
                  as='span'
                  className='text-h6 font-semibold text-gray-900'
                >
                  Filters & Sort
                </Typography>
              </DrawerTitle>
            </DrawerHeader>
            <div className='max-h-screen overflow-y-auto pb-6'>
              <BikeSearchFilters
                bikes={bikes}
                onFiltersChange={onFiltersChange}
              />
            </div>
          </DrawerContent>
        </Drawer> */}

        <div className='mt-4 flex flex-col-reverse gap-4 lg:mt-0 lg:flex-row lg:gap-6'>
          {/* Desktop Filters Sidebar */}
          <div className='block w-full lg:w-1/3'>
            <div className='sticky top-20'>
              {/* <BikeSearchFilters
                bikes={bikes}
                onFiltersChange={onFiltersChange}
              /> */}

              <SpImage
                src='https://images.sharepal.in/bike-rental/bike-desktop.webp'
                width={350}
                height={800}
                className='h-full w-full object-contain'
                containerClassName='w-full h-full'
              />
            </div>
          </div>

          {/* Results */}
          <section className='lg:w-3/4'>
            {filteredBikes.length === 0 ? (
              <NoResultsFound onSearchAgain={() => window.location.reload()} />
            ) : (
              <BikeGrid bikes={filteredBikes} />
            )}
          </section>
        </div>
      </>
    )
  },
)

BikeSearchContent.displayName = "BikeSearchContent"
