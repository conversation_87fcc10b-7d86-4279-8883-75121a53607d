import { But<PERSON> } from "@/components/ui/button"
import { Typography } from "@/components/ui/typography"
import { BUSINESS_HOURS } from "@/lib/bike-rental-utils"
import { useCityStore } from "@/store/city-store"
import { AlertCircle } from "lucide-react"
import Link from "next/link"

interface BikeSearchErrorProps {
  error: string
  retryable?: boolean
  onGoBack?: () => void
  onRetry?: () => void
  type?: "validation" | "fetch" | "expired" | "network" | "general"
  suggestions?: string[]
}

export const BikeSearchError = ({
  error,
  retryable = false,
  onGoBack,
  onRetry,
  type = "general",
  suggestions = [],
}: BikeSearchErrorProps) => {
  const { selectedCity } = useCityStore()
  const getTitle = () => {
    switch (type) {
      case "expired":
        return "Time has passed"
      case "validation":
        // Check if it's a minimum rental hours error
        if (error.includes(`${BUSINESS_HOURS.MIN_RENTAL_HOURS} hours`)) {
          return `${BUSINESS_HOURS.MIN_RENTAL_HOURS}-Hour Minimum Required`
        }
        return "Invalid search parameters"
      case "fetch":
        return "Unable to load bikes"
      case "network":
        return "Connection error"
      default:
        return "Invalid Search"
    }
  }

  return (
    <div className='flex min-h-[calc(100vh_-_196px)] flex-col items-center justify-center rounded-xl bg-gray-100 p-6 shadow-sm sm:p-8 lg:p-12'>
      <div className='text-center'>
        <div className='mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-neutral-100 sm:mb-6 sm:h-20 sm:w-20'>
          <AlertCircle className='h-8 w-8 text-neutral-300 sm:h-10 sm:w-10' />
        </div>
        <Typography
          as='h3'
          className='mb-2 font-ubuntu text-d7 text-neutral-700 sm:mb-3 sm:text-d4'
        >
          {getTitle()}
        </Typography>
        <Typography
          as='p'
          className='mx-auto mb-6 max-w-md text-b4 text-neutral-600 sm:mb-8 sm:text-b3'
        >
          {error || "Something went wrong. Please try again."}
        </Typography>

        {suggestions.length > 0 && (
          <div className='mb-6 space-y-2'>
            <Typography
              as={"p"}
              className='text-sm font-medium text-neutral-700'
            >
              Suggestions:
            </Typography>
            <ul className='space-y-1 text-sm text-neutral-600'>
              {suggestions.map((suggestion, index) => (
                <li key={index}>• {suggestion}</li>
              ))}
            </ul>
          </div>
        )}
        <div className='flex flex-col gap-3 sm:flex-row sm:justify-center'>
          {onGoBack && (
            <Button
              onClick={onGoBack}
              variant='outline'
              className='flex min-w-[120px] items-center gap-2 border-gray-200 text-neutral-700 hover:bg-gray-100 focus:ring-2 focus:ring-primary-100'
            >
              Go Back
            </Button>
          )}

          {retryable && onRetry && (
            <Button
              onClick={onRetry}
              variant='outline'
              className='flex min-w-[120px] items-center gap-2 border-gray-200 text-neutral-700 hover:bg-gray-100 focus:ring-2 focus:ring-primary-100'
            >
              Retry
            </Button>
          )}

          <Button
            asChild
            className='w-full bg-bike-600 px-6 py-3 text-b3 font-medium text-white hover:bg-bike-700 sm:w-auto sm:py-2'
          >
            <Link
              href={
                selectedCity ? `/${selectedCity?.city_url ?? "bangalore"}` : "/"
              }
              prefetch={true}
            >
              Start New Search
            </Link>
          </Button>
        </div>
      </div>
    </div>
  )
}
