import { EnhancedBikeCard } from "@/components/cards"
import { Bike } from "@/types/common"
import { memo } from "react"

interface BikeGridProps {
  bikes: Bike[]
  // total_hours?: number
}

export const BikeGrid = memo<BikeGridProps>(({ bikes }) => (
  <div className='grid grid-cols-1 gap-3 sm:grid-cols-2 sm:gap-4 lg:gap-6 xl:grid-cols-2'>
    {bikes.map((bike) => (
      <EnhancedBikeCard
        key={bike.id}
        {...bike}
        context='search'
        // total_hours={total_hours}
      />
    ))}
  </div>
))

BikeGrid.displayName = "BikeGrid"
