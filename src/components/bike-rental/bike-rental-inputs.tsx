"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { DateSelector } from "@/components/ui/date-selector"
import { TimeSelector } from "@/components/ui/time-selector"
import { toast } from "@/components/ui/use-toast"
import { useBikeRentalHandlers } from "@/hooks/use-bike-rental-handlers"
import { createBikeRentalSearchUrl } from "@/lib/bike-rental-url-utils"
import { cn } from "@/lib/utils"
import { useCityStore } from "@/store/city-store"
import { isValid } from "date-fns"
import Link from "next/link"
import React from "react"

interface BikeRentalInputsProps {
  className?: string
  isDesktop?: boolean

  onSearchClick?: (canContinue?: boolean) => void
}

export const BikeRentalInputs: React.FC<BikeRentalInputsProps> = ({
  className,
  isDesktop = false,

  onSearchClick,
}) => {
  const { selectedCity } = useCityStore()

  const {
    pickup_date,
    dropoff_date,
    pickup_time,
    dropoff_time,
    handleDateSelect,
    handleTimeSelect,
    getTimeSlots,
    isDateValid,
    isDropoffDateValid,
    getTimestamps,
    getRentalDuration,
  } = useBikeRentalHandlers()

  // Controlled open state for selectors
  const [openPickupDate, setOpenPickupDate] = React.useState<boolean>(false)
  const [openPickupTime, setOpenPickupTime] = React.useState<boolean>(false)
  const [openDropoffDate, setOpenDropoffDate] = React.useState<boolean>(false)
  const [openDropoffTime, setOpenDropoffTime] = React.useState<boolean>(false)

  // No longer need missingField for border highlight

  // Check if all fields are selected
  const canContinue = React.useMemo(() => {
    if (!pickup_date) return false
    if (!pickup_time) return false
    if (!dropoff_date) return false
    if (!dropoff_time) return false
    if (!isValid(pickup_date) || !isValid(dropoff_date)) return false
    // Check minimum rental duration
    const duration = getRentalDuration()
    if (duration !== null && duration < 24) return false
    return true
  }, [pickup_date, pickup_time, dropoff_date, dropoff_time, getRentalDuration])

  // Generate search URL for the Link
  const searchUrl = React.useMemo(() => {
    if (!canContinue) return "#"
    const { pickup_timestamp, dropoff_timestamp } = getTimestamps()
    if (!pickup_timestamp || !dropoff_timestamp) return "#"
    const cityUrl = selectedCity?.city_url || "bangalore"
    return createBikeRentalSearchUrl(
      cityUrl,
      pickup_timestamp,
      dropoff_timestamp,
    )
  }, [canContinue, getTimestamps, selectedCity?.city_url])

  // Single handler for all field changes with auto-progression logic
  const handleChange = (
    value: Date | string | null | undefined,
    field: "pickup_date" | "pickup_time" | "dropoff_date" | "dropoff_time",
  ) => {
    switch (field) {
      case "pickup_date":
        handleDateSelect(value as Date, "pickup")
        setOpenPickupDate(false)

        if (value && !pickup_time) {
          setOpenPickupTime(true)
        }
        break

      case "pickup_time":
        handleTimeSelect(value as string, "pickup")
        setOpenPickupTime(false)

        if (value && pickup_date && !dropoff_date) {
          setOpenDropoffDate(true)
        }
        break

      case "dropoff_date":
        handleDateSelect(value as Date, "dropoff")
        setOpenDropoffDate(false)

        if (value && pickup_date && pickup_time && !dropoff_time) {
          setOpenDropoffTime(true)
        }
        break

      case "dropoff_time":
        handleTimeSelect(value as string, "dropoff")
        setOpenDropoffTime(false)
        break
    }
  }

  // On search click, highlight and open first missing field
  const handleSearchClick = () => {
    if (canContinue) {
      if (onSearchClick) onSearchClick(true)
      return
    }
    // Show error if minimum rental duration not met
    const duration = getRentalDuration()
    if (duration !== null && duration < 24) {
      toast.info("Minimum rental duration is 24 hours.")
      return
    }
    if (!pickup_date) {
      setOpenPickupDate(true)
      if (onSearchClick) onSearchClick(false)
      return
    }
    if (!pickup_time) {
      setOpenPickupTime(true)
      if (onSearchClick) onSearchClick(false)
      return
    }
    if (!dropoff_date) {
      setOpenDropoffDate(true)
      if (onSearchClick) onSearchClick(false)
      return
    }
    if (!dropoff_time) {
      setOpenDropoffTime(true)
      if (onSearchClick) onSearchClick(false)
      return
    }
  }

  return (
    <div
      className={cn(
        "grid w-full items-end gap-4",
        isDesktop
          ? "grid-cols-5 gap-4" // Desktop: 5 columns (4 inputs + 1 button)
          : "grid-cols-1 gap-4", // Mobile: 1 column
        className,
      )}
    >
      {/* Minimum rental duration error is now shown as toast on search click */}
      <DateSelector
        label='Pickup Date'
        placeholder='Select Date'
        value={pickup_date}
        onValueChange={(date) => handleChange(date, "pickup_date")}
        disabled={(date) => !isDateValid(date)}
        open={openPickupDate}
        setOpen={setOpenPickupDate}
        dateFormat='MMM dd, yyyy'
        className={isDesktop ? "min-w-[180px]" : "w-full"}
        buttonClassName={cn(isDesktop ? "h-12" : "h-11")}
      />

      <TimeSelector
        label='Pickup Time'
        placeholder='Select Time'
        value={pickup_time}
        onValueChange={(time) => handleChange(time, "pickup_time")}
        timeSlots={getTimeSlots("pickup")}
        disabled={!pickup_date}
        open={openPickupTime}
        setOpen={setOpenPickupTime}
        className={isDesktop ? "min-w-[140px]" : "w-full"}
        buttonClassName={cn(isDesktop ? "h-12" : "h-11")}
      />

      <DateSelector
        label='Drop-off Date'
        placeholder='Select Date'
        value={dropoff_date}
        onValueChange={(date) => handleChange(date, "dropoff_date")}
        disabled={(date) => !isDropoffDateValid(date)}
        open={openDropoffDate}
        setOpen={setOpenDropoffDate}
        dateFormat='MMM dd, yyyy'
        className={isDesktop ? "min-w-[180px]" : "w-full"}
        buttonClassName={cn(isDesktop ? "h-12" : "h-11")}
      />

      <TimeSelector
        label='Drop-off Time'
        placeholder='Select Time'
        value={dropoff_time}
        onValueChange={(time) => handleChange(time, "dropoff_time")}
        timeSlots={getTimeSlots("dropoff")}
        disabled={!dropoff_date}
        open={openDropoffTime}
        setOpen={setOpenDropoffTime}
        className={isDesktop ? "min-w-[140px]" : "w-full"}
        buttonClassName={cn(isDesktop ? "h-12" : "h-11")}
      />

      {/* Search Button - Desktop as 5th column, Mobile full width */}

      <Button
        asChild
        disabled={!canContinue}
        size={isDesktop ? "lg" : "default"}
        className='h-12 w-full bg-bike-600 font-medium text-white hover:bg-bike-700 disabled:opacity-50'
        onClick={handleSearchClick}
      >
        <Link href={searchUrl}>Search</Link>
      </Button>
    </div>
  )
}
