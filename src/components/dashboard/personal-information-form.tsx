"use client"

import { zod<PERSON>esolver } from "@hookform/resolvers/zod"
import { motion } from "framer-motion"
import { useForm } from "react-hook-form"

import { CustomFormField } from "@/components/custom/form-field"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { Label } from "@/components/ui/label"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import {
  personalInformationSchema,
  type PersonalInformationFormData,
} from "@/lib/validations/personal-information"

import { PhoneInput } from "@/components/custom/phone-input"
import { cn } from "@/lib/utils"
import { updateUser } from "@/services/user"
import { useUserStore } from "@/store/user-store"
import { InfoIcon } from "lucide-react"
import { toast } from "sonner"

const formVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.3,
      ease: "easeOut",
    },
  },
}

const inputVariants = {
  hidden: { opacity: 0, x: -20 },
  visible: {
    opacity: 1,
    x: 0,
    transition: {
      duration: 0.2,
    },
  },
}

interface PersonalInformationFormProps {
  onCancel: () => void
}

export function PersonalInformationForm({
  onCancel,
}: PersonalInformationFormProps) {
  const { user, setUser } = useUserStore()

  const form = useForm<PersonalInformationFormData>({
    resolver: zodResolver(personalInformationSchema),
    defaultValues: {
      first_name: user?.first_name || "",
      last_name: user?.last_name || "",
      email: user?.email || "",
      country_code_calling: user?.country_code_calling || 91,
      calling_number: user?.calling_number || "",
      country_code_whatsapp: user?.country_code_whatsapp || 91,
      whatsapp_number: user?.whatsapp_number || "",
      isWhatsappSame: user?.calling_number === user?.whatsapp_number,
      // gender: user?.gender || 'male',
      date_of_birth: user?.date_of_birth
        ? new Date(user.date_of_birth).toISOString().split("T")[0]
        : "",
    },
  })

  const onSubmit = async (data: PersonalInformationFormData) => {
    try {
      //check if calling number is same as user calling number
      if (data.calling_number !== user?.calling_number) {
        toast.error("You can not update your calling number")
        return
      }

      if (user) {
        // call the api too (testing needed)
        const userUpdateResponse = await updateUser({
          ...user,
          ...data,
        })
        if (userUpdateResponse)
          setUser({
            ...user,
            ...data,
            date_of_birth: new Date(
              data?.date_of_birth ?? new Date(),
            ).getTime(),
          })
      }
      onCancel()
    } catch (error) {
      console.error(error)
    }
  }

  const isWhatsappSame = form.watch("isWhatsappSame")

  return (
    <Card className='border-0 p-0'>
      <CardContent className='p-0'>
        <motion.div initial='hidden' animate='visible' className='w-full'>
          <Form {...form}>
            <form
              onSubmit={form.handleSubmit(onSubmit)}
              className='flex flex-col gap-4'
            >
              <div className='grid gap-4 md:grid-cols-2'>
                <div className={cn(`grid items-end gap-2 md:grid-cols-2`)}>
                  <CustomFormField
                    form={form}
                    name='first_name'
                    label='Your Name'
                    placeholder='First name'
                    required
                  />
                  <CustomFormField
                    form={form}
                    name='last_name'
                    label=''
                    placeholder='Last name'
                  />
                </div>
                <CustomFormField
                  form={form}
                  name='email'
                  label='Email Address'
                  placeholder='<EMAIL>'
                  type='email'
                />
                <PhoneInput
                  form={form}
                  phoneFieldName='calling_number'
                  countryCodeFieldName='country_code_calling'
                  label='Phone Number'
                  placeholder='Enter Phone Number'
                  required
                  readOnly
                />

                <FormField
                  control={form.control}
                  name='isWhatsappSame'
                  render={({ field }) => (
                    <FormItem className='space-y-3'>
                      <FormLabel className='!text-b4'>
                        Is this your WhatsApp Number?
                      </FormLabel>
                      <FormControl>
                        <RadioGroup
                          onValueChange={(value) => {
                            field.onChange(value === "yes")
                            if (value === "yes") {
                              form.setValue(
                                "whatsapp_number",
                                form.getValues("calling_number"),
                              )
                            } else {
                              form.setValue("whatsapp_number", "")
                            }
                          }}
                          defaultValue={field.value ? "yes" : "no"}
                          className='grid-cols-2'
                        >
                          {[
                            { id: "yes-radio", value: "yes", label: "Yes" },
                            { id: "no-radio", value: "no", label: "No" },
                          ].map((item) => (
                            <div
                              key={item.id}
                              className='relative flex flex-col gap-4 rounded-2xl border-2 border-input border-neutral-150 bg-neutral-150 p-4 has-[[data-state=checked]]:border-secondary-500 has-[[data-state=checked]]:bg-gray-100'
                            >
                              <div className='flex justify-between gap-2'>
                                <RadioGroupItem
                                  id={item.id}
                                  value={item.value}
                                  className='order-1 after:absolute after:inset-0'
                                />
                                <Label htmlFor={item.id}>{item.label}</Label>
                              </div>
                            </div>
                          ))}
                        </RadioGroup>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {!isWhatsappSame && (
                  <motion.div
                    variants={inputVariants}
                    initial='hidden'
                    animate='visible'
                    className='col-span-full grid w-full items-end gap-4 md:grid-cols-2'
                  >
                    <PhoneInput
                      form={form}
                      phoneFieldName='whatsapp_number'
                      countryCodeFieldName='country_code_whatsapp'
                      placeholder='Enter Whatsapp Number'
                      label='WhatsApp Number'
                      required
                    />
                    <div className='flex items-center justify-start gap-1 rounded-2xl bg-secondary-100 px-2 py-1.5'>
                      <InfoIcon
                        size={18}
                        className='inline-block align-middle font-bold text-primary-500'
                      />
                      <div className=''>
                        <FormLabel className='text-xs font-bold text-primary-500'>
                          WhatsApp number required for order details
                        </FormLabel>
                        <p className='text-xs text-neutral-500'>
                          All order notifications are <br /> sent to you over
                          WhatsApp.
                        </p>
                      </div>
                    </div>
                  </motion.div>
                )}

                {/* <CustomFormField
                  form={form}
                  name="gender"
                  label="Gender"
                  type="select"
                  options={[
                    { value: 'male', label: 'Male' },
                    { value: 'female', label: 'Female' },
                    { value: 'other', label: 'Other' },
                  ]}
                /> */}

                <CustomFormField
                  form={form}
                  name='date_of_birth'
                  label='Date of Birth'
                  type='date'
                  placeholder='YYYY-MM-DD'
                />
              </div>

              <div className='flex justify-start gap-4'>
                <Button
                  type='submit'
                  variant='default'
                  size='lg'
                  className='md:h-11 md:w-auto'
                >
                  Save Details
                </Button>
                <Button
                  type='button'
                  variant='outline'
                  size='lg'
                  className='md:h-11 md:w-auto'
                  onClick={onCancel}
                >
                  Cancel
                </Button>
              </div>
            </form>
          </Form>
        </motion.div>
      </CardContent>
    </Card>
  )
}
