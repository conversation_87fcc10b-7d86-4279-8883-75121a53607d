"use client"

import Link from "next/link"

import { Typography } from "@/components/ui/typography"
import { fetchFaqs } from "@/services/common"

import { formatTimestampToDisplayFormat } from "@/functions/date"
import { BikeDetails, BikeOrder } from "@/types/order"
import { useQuery } from "@tanstack/react-query"
import { ChevronLeftIcon } from "sharepal-icons"
import GetCarepal from "./get-carepal"

// Order Details Header
interface OrderDetailsHeaderProps {
  bikeOrder: BikeOrder
  bikeDetails: BikeDetails
  isModification: boolean
}

const OrderDetailsHeader = ({
  bikeOrder,
  bikeDetails,
  isModification,
}: OrderDetailsHeaderProps) => {
  const { data: faqs } = useQuery({
    queryKey: ["carepa-faqs"],
    refetchOnWindowFocus: false,
    queryFn: async () => await fetchFaqs("carepal", "", "carepal"),
  })

  return (
    <>
      {/* Header */}
      <div className='w-full space-y-4'>
        <Link
          href='/dashboard/orders'
          className='inline-flex items-center text-neutral-900'
        >
          <ChevronLeftIcon className='mr-2 h-4 w-4 md:h-6 md:w-6' />
          <Typography as={"h6"} className='!text-h6 md:!text-h2'>
            Order Details
          </Typography>
        </Link>

        <div className='flex items-center justify-between'>
          <div className='flex items-center justify-center gap-2'>
            {/* icon */}
            <div className='flex h-10 w-10 items-center justify-center rounded-full bg-secondary-200 md:h-14 md:w-14 md:overflow-hidden md:rounded-full'>
              <svg
                xmlns='http://www.w3.org/2000/svg'
                width='100%'
                height='100%'
                fill='none'
                className='p-2.5'
                viewBox='0 0 32 32'
              >
                <path
                  fill='#1945E8'
                  d='M6.667 10.667v14.666h18.666V10.667h-4v10.666L16 18.667l-5.333 2.666V10.667zM4 28V7.733L7.067 4h17.866L28 7.733V28zM7.2 8h17.6l-1.133-1.333H8.333zm6.133 2.667V17L16 15.667 18.667 17v-6.333z'
                ></path>
              </svg>
            </div>

            <div>
              <Typography as={"h6"} className='text-sh4 font-bold md:text-h6'>
                #{bikeOrder.order_id}
              </Typography>
              <Typography
                as={"p"}
                className='!text-o4 text-neutral-600 md:!text-b6'
              >
                Order Placed On:{"  "}
                <span className='text-neutral-800'>
                  {formatTimestampToDisplayFormat(bikeDetails?.created_at)}
                </span>
              </Typography>
            </div>
          </div>

          {/* pdf button */}
          {/* <div className='flex gap-2'>
            <Button
              variant='outline-primary'
              className='rounded-full p-2 hover:bg-primary-100 md:px-4 md:py-2'
              asChild
            >
              <a
                href={bikeOrder.deal_cf_order_pdf}
                target='_blank'
                rel='noopener noreferrer'
              >
                <span className='hidden !text-bt3 md:flex'> Order Summary</span>
                <DownloadOutlinedIcon className='h-4 w-4' />
              </a>
            </Button>
          </div> */}
        </div>

        {/* Carepal Banner */}
        {isModification && bikeOrder?.carepal_fee <= 0 && (
          <GetCarepal faqs={faqs ?? []} orderId={bikeOrder.order_id} />
        )}
      </div>
    </>
  )
}

export default OrderDetailsHeader
