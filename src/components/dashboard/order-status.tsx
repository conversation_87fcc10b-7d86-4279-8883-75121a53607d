"use client"

import { AnimatePresence, motion } from "framer-motion"
import { useEffect, useState } from "react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import usePlaceOrder from "@/hooks/use-place-order"
import { useThrottle } from "@/hooks/use-throttle"
import { cn } from "@/lib/utils"
import { BikeOrderSummary } from "@/types/order"
import { fetchWithAuthPost } from "@/utils/fetchWithAuth"
import { useMutation, useQueryClient } from "@tanstack/react-query"
import { useRouter } from "next/navigation"
import { toast } from "sonner"

import useWindowSize from "@/hooks/use-window-resize"

import { trackContactSupportSelected } from "@/lib/gtag-event"
import { useUserStore } from "@/store/user-store"
import {
  CautionTriangleOutlinedIcon,
  ChevronDownIcon,
  CrossCircleOutlinedIcon,
  InfoCircleFilledIcon,
  TickCircleOutlinedIcon,
} from "sharepal-icons"
import { Typography } from "../ui/typography"

function formatTimestamp(isoString: string) {
  if (!isoString) return ""
  const date = new Date(isoString)

  // Helper to get ordinal suffix for the day
  function getOrdinalSuffix(n: number) {
    if (n > 10 && n < 14) return "th" // special case for 11th-13th
    switch (n % 10) {
      case 1:
        return "st"
      case 2:
        return "nd"
      case 3:
        return "rd"
      default:
        return "th"
    }
  }

  // Format day with ordinal suffix
  const day = date.getDate()
  const ordinal = getOrdinalSuffix(day)

  // Month abbreviation
  const months = [
    "Jan",
    "Feb",
    "Mar",
    "Apr",
    "May",
    "Jun",
    "Jul",
    "Aug",
    "Sep",
    "Oct",
    "Nov",
    "Dec",
  ]
  const monthAbbrev = months[date.getMonth()]

  // Format time in 12-hour format
  let hours = date.getHours()
  const minutes = date.getMinutes()
  const period = hours >= 12 ? "pm" : "am"
  hours = hours % 12 || 12 // convert 0 to 12 for midnight/noon
  const minutesPadded = minutes < 10 ? "0" + minutes : minutes

  // Build the formatted date and time strings
  const formattedDate = `${day}${ordinal} ${monthAbbrev}`
  const formattedTime = `${hours}:${minutesPadded} ${period}`

  return `${formattedDate} | ${formattedTime}`
}

// Example usage:
// console.log(formatTimestamp("2025-03-03T09:12:06.932Z"))
// Output might be "3rd Mar | 9:12 am" depending on the timestamp provided.

interface OrderStatusProps {
  orderSummary: BikeOrderSummary
  setOpenContactSupport: (value: boolean) => void
  setOrderCancelModal: (value: boolean) => void
  order_id: string
  paramData: string
}

const ACTION_TYPES = {
  VERIFY_PROFILE: "VERIFY_PROFILE",
  PAY_RENTAL_CHARGES: "PAY_RENTAL_CHARGES",
  GET_SUPPORT: "GET_SUPPORT",
  CONTACT_SUPPORT: "CONTACT_SUPPORT",
  CANCEL_ORDER: "CANCEL_ORDER",
  PROVIDE_FEEDBACK: "PROVIDE_FEEDBACK",
  REDIRECT: "REDIRECT",
  UNDO_CANCELLATION: "UNDO_CANCELLATION",
}

const getStatusColor = (stage: string, isWarning = false, isError = false) => {
  if (stage.includes("Damage") || isWarning) {
    return "text-warning-500"
  }
  if (stage.includes("Cancelled") || isError) {
    return "text-destructive-500"
  }
  return "text-success-500"
}

const getStatusIcon = (
  stage: string,
  isActive = false,
  isCurrent = false,
  isWarning = false,
  isError = false,
) => {
  if (stage.includes("Damage") || isWarning) {
    return (
      <CautionTriangleOutlinedIcon className='min-h-5 min-w-5 text-warning-500' />
    )
  }
  if (stage.includes("Cancelled") || isError) {
    return (
      <CrossCircleOutlinedIcon className='min-h-5 min-w-5 text-destructive' />
    )
  }

  if (isCurrent) {
    return (
      <TickCircleOutlinedIcon
        strokeWidth={4}
        className={cn(
          "min-h-5 min-w-5",
          isCurrent ? "text-success-400" : "text-muted-foreground",
        )}
      />
    )
  }
  if (isActive) {
    return
  }
}

interface ActionButton {
  text: string
  variant:
    | "default"
    | "default"
    | "outline"
    | "outline-primary"
    | "primary"
    | "outline-destructive"
    | "outline-bike"

  action_type: string
  disabled: boolean
  message: string
}

const getActionsForStage = (stage: string): ActionButton[] => {
  const baseActions: ActionButton[] = []

  // Add actions based on the stage
  if (stage === "Order Received" || stage === "Order Confirmed") {
    // baseActions.push({
    //   text: "Contact Support",
    //   variant: "outline",
    //   action_type: ACTION_TYPES.CONTACT_SUPPORT,
    //   disabled: false,
    //   message: "",
    // })
    baseActions.push({
      text: "Cancel Order",
      variant: "outline-destructive",
      action_type: ACTION_TYPES.CANCEL_ORDER,
      disabled: false,
      message: "",
    })
  }

  if (stage === "KYC Received") {
    baseActions.push({
      text: "Verify Profile",
      variant: "primary",
      action_type: ACTION_TYPES.VERIFY_PROFILE,
      disabled: false,
      message: "",
    })
  }

  if (stage.includes("Due") || stage.includes("Pending Payment")) {
    baseActions.push({
      text: "Pay Rental Charges",
      variant: "primary",
      action_type: ACTION_TYPES.PAY_RENTAL_CHARGES,
      disabled: false,
      message: "",
    })
  }

  if (stage.includes("Cancelled")) {
    baseActions.push({
      text: "Undo Cancellation",
      variant: "outline-primary",
      action_type: ACTION_TYPES.UNDO_CANCELLATION,
      disabled: false,
      message: "",
    })
  }

  // Always add support action
  baseActions.push({
    text: "Contact Support",
    variant: "outline-bike",
    action_type: ACTION_TYPES.GET_SUPPORT,
    disabled: false,
    message: "",
  })

  return baseActions
}

export function OrderStatus({
  orderSummary,
  paramData,
  order_id,
  setOpenContactSupport,
  setOrderCancelModal,
}: OrderStatusProps) {
  const [isMobile, setIsMobile] = useState(false)
  const [showAllUpdates, setShowAllUpdates] = useState(false)
  const router = useRouter()
  const { user } = useUserStore()

  const { hanldePaymentForOrder } = usePlaceOrder()

  const queryClient = useQueryClient()

  const { mutate: undoCancellation } = useMutation({
    mutationFn: () =>
      fetchWithAuthPost(
        "https://api.sharepal.in/api:AIoqxnqr/return/undo-cancellation",
        {
          order_id,
        },
      ),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["orderStatus"] })
    },
    onError: () => {
      toast.error("Failed to undo cancellation")
    },
  })

  const handleActionButton = async (
    actionType: string,
    actionMessage: string,
    e?: React.MouseEvent<HTMLButtonElement>,
  ) => {
    if (e) e.preventDefault()

    if (actionType === ACTION_TYPES.VERIFY_PROFILE) {
      router.push("/complete-verification")
    }
    if (actionType === ACTION_TYPES.PAY_RENTAL_CHARGES) {
      hanldePaymentForOrder({
        order_id,
        amount: orderSummary.bike_details.due_amount,
      })
    }
    if (actionType === ACTION_TYPES.GET_SUPPORT) {
      setOpenContactSupport(true)
    }
    if (actionType === ACTION_TYPES.CONTACT_SUPPORT) {
      setOpenContactSupport(true)
    }
    if (actionType === ACTION_TYPES.PROVIDE_FEEDBACK) {
      setOpenContactSupport(true)
    }
    if (actionType === ACTION_TYPES.CANCEL_ORDER) {
      setOrderCancelModal(true)
    }
    if (actionType === ACTION_TYPES.UNDO_CANCELLATION) {
      undoCancellation()
    }
    if (actionType === ACTION_TYPES.REDIRECT) {
      if (actionMessage.includes("Need support")) {
        trackContactSupportSelected({
          order_id,
          user_id: user?.id ?? 0,
        })
      }
      if (actionMessage) window.open(actionMessage, "_blank")
      else {
        toast.error("Unable to Open Page, Please try Later")
      }
    }
  }

  const size = useWindowSize()
  const throttleActionButton = useThrottle(handleActionButton, 1000)

  useEffect(() => {
    const checkMobile = () => {
      if (size.width) {
        setIsMobile(size.width < 768)
        if (size.width >= 768) {
          setShowAllUpdates(true)
        }
      }
    }
    checkMobile()
  }, [size.width])

  useEffect(() => {
    handleActionButton(paramData, "")
  }, [])

  // to remove query params after 5 seconds
  useEffect(() => {
    const removeQueryParam = () => {
      const url = new URL(window.location.href)
      url.searchParams.delete("open")
      router.replace(url.href, { scroll: true })
    }

    const timer = setTimeout(removeQueryParam, 5000)
    return () => clearTimeout(timer)
  }, [router])

  // Mock order status data based on bike_details.stage
  const mockOrderStatus = {
    activeStage: orderSummary.bike_details.stage,
    stages: [
      "Order Received",
      "Order Confirmed",
      "KYC Received",
      "Order Packed",
      "Order Shipped",
      "Order Delivered",
    ],
    stageInfo: {
      title: orderSummary.bike_details.stage,
      message: `Your order is currently at ${orderSummary.bike_details.stage} stage.`,
      actions: getActionsForStage(orderSummary.bike_details.stage),
      warning: orderSummary.bike_details.stage.includes("Cancelled"),
      error: orderSummary.bike_details.stage.includes("Damage"),
    },
    order_timeline: {} as { [key: string]: string },
  }

  return (
    <div className='sticky top-0 w-full space-y-4'>
      <Card className='border-none bg-gray-100 shadow-none'>
        <CardHeader className='pb-3'>
          <CardTitle>
            <Typography className='text-h6 md:!text-h2' as={"span"}>
              Order Status
            </Typography>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className='rounded-lg bg-gray-100'>
            <div className='space-y-4 md:space-y-6'>
              {/* Status Header */}
              <div className='flex items-start gap-3'>
                <div
                  className={cn(
                    "flex h-6 w-6 items-center justify-center rounded-full p-1",
                    mockOrderStatus.activeStage.includes("Cancelled") ||
                      mockOrderStatus.activeStage.includes("Damage") ||
                      mockOrderStatus.stageInfo.warning ||
                      mockOrderStatus.stageInfo.error
                      ? "bg-destructive/10"
                      : "",
                  )}
                >
                  {getStatusIcon(
                    mockOrderStatus.activeStage,
                    false,
                    true,
                    mockOrderStatus.stageInfo.warning,
                    mockOrderStatus.stageInfo.error,
                  )}
                </div>
                <div className='flex-1 space-y-1'>
                  <Typography
                    as={"h6"}
                    className={cn(
                      "!text-sh4 md:!text-h6",
                      getStatusColor(
                        mockOrderStatus.activeStage,
                        mockOrderStatus.stageInfo.warning,
                        mockOrderStatus.stageInfo.error,
                      ),
                    )}
                  >
                    {mockOrderStatus.stageInfo.title}!
                  </Typography>
                  <Typography
                    as={"p"}
                    className='text-b4 text-muted-foreground md:text-b2'
                  >
                    {mockOrderStatus.stageInfo.message}
                  </Typography>
                </div>
              </div>

              {/* Action Buttons */}
              <div className='flex flex-col gap-2'>
                {mockOrderStatus.stageInfo.actions.map((button, index) => (
                  <Button
                    key={button.text + index}
                    size={"lg"}
                    onClick={(e) => {
                      throttleActionButton(
                        button.action_type,
                        button.message,
                        e,
                      )
                    }}
                    disabled={button.disabled}
                    variant={button.variant}
                    className={cn("w-full !text-bt2", button.variant)}
                  >
                    {button.text}
                  </Button>
                ))}
              </div>

              {/* Timeline */}
              <div className='w-full'>
                <TimelineStatus
                  title='Order Timeline'
                  stages={mockOrderStatus.stages}
                  activeStage={mockOrderStatus.activeStage}
                  stageInfo={mockOrderStatus.stageInfo}
                  order_timeline={mockOrderStatus.order_timeline}
                  isMobile={isMobile}
                  showAllUpdates={showAllUpdates}
                  setShowAllUpdates={setShowAllUpdates}
                />
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

interface DividerText {
  text?: string
  icon?: React.ReactNode
}
export function DividerText({ text = "Order Timeline", icon }: DividerText) {
  return (
    <div className='relative flex w-full items-center justify-center'>
      <div className='absolute inset-0 flex items-center'>
        <div className='w-full border-t border-gray-200' />
      </div>
      <div className='relative'>
        <Typography
          as='span'
          className='flex items-center gap-2 rounded-full bg-neutral-150 px-4 py-1.5 text-b6 text-neutral-500'
        >
          {icon}
          {text}
          {icon}
        </Typography>
      </div>
    </div>
  )
}

interface SpecialInfoText {
  title: string
  description: string
  type: "warning" | "error" | "success" | "info" | "neutral-info"
}

export function SpecialInfoText({ title, description, type }: SpecialInfoText) {
  const icon = {
    warning: (
      <CautionTriangleOutlinedIcon className='min-h-5 min-w-5 text-warning-600' />
    ),
    error: (
      <CrossCircleOutlinedIcon className='min-h-5 min-w-5 text-destructive-600' />
    ),
    success: (
      <TickCircleOutlinedIcon className='min-h-5 min-w-5 text-success-600' />
    ),
    info: <InfoCircleFilledIcon className='min-h-5 min-w-5 text-primary-500' />,
    "neutral-info": (
      <InfoCircleFilledIcon className='min-h-5 min-w-5 text-neutral-500' />
    ),
  }[type]

  const bg = {
    warning: "bg-warning-100",
    error: "bg-destructive-100",
    success: "bg-success-100",
    info: "bg-primary-100",
    "neutral-info": "bg-neutral-100",
  }[type]

  const text = {
    warning: "text-warning-700",
    error: "text-destructive-700",
    success: "text-success-500",
    info: "text-primary-700",
    "neutral-info": "text-neutral-700",
  }[type]

  return (
    <div
      className={cn(
        "flex max-h-max w-full items-center gap-2 rounded-full px-4 py-2",
        bg,
      )}
    >
      {icon}
      <div className=''>
        {title && (
          <Typography as='span' className={cn("min-w-max !text-sh4", text)}>
            {title}
          </Typography>
        )}
        {description && (
          <Typography
            as='span'
            className={cn("min-w-max !text-b6 !leading-[10px]", text)}
          >
            {description}
          </Typography>
        )}
      </div>
    </div>
  )
}

const TimelineStatus = ({
  showAllUpdates,
  stages,
  activeStage,
  stageInfo,
  isMobile,
  setShowAllUpdates,
  order_timeline,
  title,
}: {
  showAllUpdates: boolean
  activeStage: string
  stages: string[]
  title: string
  stageInfo: {
    warning?: boolean
    error?: boolean
  }
  isMobile: boolean
  setShowAllUpdates: (val: boolean) => void
  order_timeline: {
    [key: string]: string
  }
}) => (
  <>
    <div className='flex items-center justify-between'>
      <DividerText text={title} />
    </div>
    <AnimatePresence mode='wait'>
      <motion.div
        key={showAllUpdates ? "full" : "compact"}
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: -20 }}
        className={cn(
          "mt-4 h-full overflow-hidden transition-all",
          showAllUpdates ? "h-auto" : "h-[100px]",
        )}
      >
        {/* Render the refund stages */}
        {
          // Full Timeline
          stages.map((stage, index) => {
            const activeIndex = stages.indexOf(activeStage)
            const isCompleted = index <= activeIndex
            const isCurrent = stage === activeStage

            return (
              <div key={stage} className='relative flex gap-3'>
                <div className='flex flex-col items-center'>
                  <div
                    className={cn(
                      "flex min-h-5 min-w-5 items-center justify-center rounded-full",
                      isCompleted ? "bg-success-500" : "bg-neutral-150",
                      isCurrent && "bg-gray-50",
                    )}
                  >
                    {getStatusIcon(
                      stage,
                      isCompleted,
                      isCurrent,
                      stage == activeStage && stageInfo.warning,
                      stage == activeStage && stageInfo.error,
                    )}
                  </div>
                  {index <
                    (isMobile && !showAllUpdates ? 1 : stages.length - 1) && (
                    <div
                      className={cn(
                        "h-full w-0.5 rounded-full",
                        isCompleted && index < activeIndex
                          ? "bg-success-500"
                          : "bg-neutral-300",
                      )}
                    />
                  )}
                </div>
                <div className='flex flex-1 items-center justify-between gap-2 pb-8'>
                  <Typography
                    as={"p"}
                    className={cn(
                      "!text-sh5 md:!text-sh3",
                      isCurrent ? "text-primary" : "text-foreground",
                    )}
                  >
                    {stage}
                  </Typography>
                  {index <= activeIndex && (
                    <Typography
                      as={"p"}
                      className='!text-o4 text-muted-foreground md:!text-b6'
                    >
                      {formatTimestamp(order_timeline[stage])}
                    </Typography>
                  )}
                </div>
              </div>
            )
          })
        }
      </motion.div>
    </AnimatePresence>
    {isMobile && stages?.length > 2 && (
      // onClick = {() => setShowAllUpdates(!showAllUpdates)}
      <button
        className='w-full'
        onClick={() => setShowAllUpdates(!showAllUpdates)}
      >
        <DividerText
          icon={
            <ChevronDownIcon
              className={cn(
                "h-4 w-4 !text-neutral-900 transition-all",
                showAllUpdates ? "rotate-180" : "rotate-0",
              )}
            />
          }
          text={showAllUpdates ? "Hide Status" : "View Status"}
        />
      </button>
    )}
  </>
)
