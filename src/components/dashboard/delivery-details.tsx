"use client"

import {
  ArrowRightIcon,
  ExternalLinkIcon,
  MapPin,
  TimerIcon,
  Truck,
} from "lucide-react"

import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"

import {
  formatDurationFromHours,
  formatTimestampForRental,
} from "@/functions/date"
import { BikeDetails, BikeOrder } from "@/types/order"
import { Typography } from "../ui/typography"
// import RentalChargeCalculator from '../modals/rental-charge-calculator'

interface DeliveryDetailsProps {
  bikeOrder: BikeOrder
  bikeDetails: BikeDetails
  isModification?: boolean
}

export default function BikeDeliveryDetails({
  bikeOrder,
  bikeDetails,
}: DeliveryDetailsProps) {
  const pickupDateTime = formatTimestampForRental(bikeDetails?.start_time)
  const dropoffDateTime = formatTimestampForRental(bikeDetails?.end_time)
  const formattedDuration = formatDurationFromHours(bikeDetails?.total_hours)

  return (
    <>
      <div className='w-full space-y-4'>
        {/* Header */}
        <div className='flex items-center gap-3'>
          <Truck className='h-6 w-6' />
          <Typography
            as={"h6"}
            className='text-sh4 text-primary-900 md:text-h6'
          >
            Pickup & Drop-off Details:
          </Typography>
        </div>

        {/* Content Grid */}
        <div className='grid gap-4 md:grid-cols-2'>
          <div className='flex h-full w-full flex-col gap-3'>
            {/* Dates Card */}
            <Card className='h-full w-full overflow-hidden !rounded-2xl'>
              <CardContent className='h-full w-full p-0'>
                {/* card top */}
                <div className='flex items-center gap-1 bg-neutral-150 px-4 py-3'>
                  <MapPin className='h-4 w-4 text-gray-600' />
                  <Typography as={"h5"} className='text-sh7 md:text-sh6'>
                    Rental Period:
                  </Typography>
                </div>

                <div className='flex min-h-[75px] items-center justify-between p-4'>
                  <div className='text-start'>
                    <Typography
                      as='p'
                      className='font-ubuntu text-sh6 text-neutral-500 md:text-sh5'
                    >
                      {pickupDateTime.date}
                    </Typography>
                    <Typography
                      as='p'
                      className='font-inter text-sh2 text-neutral-800'
                    >
                      {pickupDateTime.time}
                    </Typography>
                  </div>
                  <div className='flex items-center gap-2'>
                    <ArrowRightIcon className='size-8 text-neutral-900' />
                  </div>
                  <div className='text-end'>
                    <Typography
                      as='p'
                      className='font-ubuntu text-sh6 text-neutral-500 md:text-sh5'
                    >
                      {dropoffDateTime.date}
                    </Typography>
                    <Typography
                      as='p'
                      className='font-inter text-sh2 text-neutral-800'
                    >
                      {dropoffDateTime.time}
                    </Typography>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Typography
              as={"p"}
              className='b-2 flex items-center justify-start px-2 text-b6 text-gray-900'
            >
              <TimerIcon className='size-4 min-w-4' />
              {formattedDuration}

              <span className='ml-2 text-gray-700'>
                ({bikeDetails?.total_hours} Hours Total)
              </span>
            </Typography>
          </div>

          {/* Pickup & Drop-off Location */}
          <div className='flex h-full w-full flex-col gap-3'>
            {/* Delivery Address Card */}
            <Card className='w-full overflow-hidden !rounded-2xl'>
              <CardContent className='p-0'>
                <div className='w-full'>
                  <div className='flex items-center gap-1 bg-neutral-150 px-4 py-3'>
                    <MapPin className='h-4 w-4 text-gray-600' />
                    <Typography as={"h5"} className='text-sh7 md:text-sh6'>
                      Pickup & Drop-off Location:
                    </Typography>
                  </div>
                  <div className='min-h-[75px] px-4 py-3'>
                    <Typography
                      as={"p"}
                      className='mt-1 max-w-max text-wrap text-b6 text-gray-700'
                    >
                      Basement 2 Parking, Soul Space Paradigm, Marathahalli
                      Village, Marathahalli, Bengaluru, Karnataka 560037
                    </Typography>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Button
              variant='link'
              size={"sm"}
              className='flex h-auto items-center justify-start gap-2 p-0 !text-bt4 text-blue-600 hover:text-blue-700 md:!text-bt3'
            >
              <ExternalLinkIcon className='h-5 w-5 text-blue-600' />
              View Address
            </Button>
          </div>
        </div>
      </div>
    </>
  )
}
