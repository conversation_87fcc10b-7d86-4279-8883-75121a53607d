"use client"

import PalWalletLoading from "@/components/skeletons/pal-wallet-skeleton"
import { Card, CardContent } from "@/components/ui/card"
import {
  <PERSON><PERSON><PERSON>,
  Too<PERSON>ipContent,
  <PERSON>ltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip"
import SpImage from "@/shared/SpImage/sp-image"
import { useUserStore } from "@/store/user-store"
import { formatTransactionDate } from "@/utils/date-logics"
import { fetchWithAuth } from "@/utils/fetchWithAuth"
import { useQuery } from "@tanstack/react-query"
import { motion } from "framer-motion"
import { ArrowDownLeft, ArrowUpRight, HelpCircle } from "lucide-react"

interface Transaction {
  created_at: number
  amount: number
  detail: string
  source: string
  user_id: number
  starting_balance: number
  ending_balance: number
}

const fetchWalletTransactions = async (
  userId: number,
): Promise<Transaction[]> => {
  const url = new URL(
    "https://api.sharepal.in/api:AIoqxnqr/user/wallet-transactions",
  )
  url.searchParams.append("user_id", userId.toString())

  const data = await fetchWithAuth(url.toString())
  return data
}

export default function PalWallet() {
  const { user, wallet, userLoading } = useUserStore()

  const {
    data: transactions,
    isLoading: transactionsLoading,
    error,
  } = useQuery({
    queryKey: ["walletTransactions", user?.id],
    queryFn: () => fetchWalletTransactions(user?.id as number),
    enabled: !!user?.id,
  })

  if (userLoading || !wallet || transactionsLoading) {
    return <PalWalletLoading />
  }

  if (error) {
    return (
      <Card className='bg-gray-100'>
        <CardContent className='p-6'>
          <p className='text-center text-red-500'>
            Error loading wallet transactions. Please try again later.
          </p>
        </CardContent>
      </Card>
    )
  }

  const endingBalance = transactions ? transactions[0]?.ending_balance : 0
  const formattedBalance =
    typeof endingBalance === "number" && !isNaN(endingBalance)
      ? Math.abs(endingBalance).toLocaleString()
      : "0"

  return (
    <>
      <div className='space-y-6 rounded-3xl bg-gray-100 px-3 py-4 md:p-6'>
        <h3 className='px-2 text-2xl font-bold text-neutral-900'>Pal Wallet</h3>

        <Card className='overflow-hidden border-2 border-neutral-200 bg-neutral-150 p-3 md:p-6'>
          <div className='flex flex-col justify-start gap-5 md:flex-row md:items-center md:justify-between'>
            <div className='flex items-center gap-4'>
              <div className='flex h-20 w-20 items-center justify-center rounded-xl md:h-24 md:w-24'>
                <SpImage
                  src='/images/pal-wallet.webp'
                  alt='Wallet'
                  className='h-20 w-20 md:h-24 md:w-24'
                  width={200}
                  height={200}
                />
              </div>
              <div className='space-y-1'>
                <div className='flex items-center gap-2'>
                  <p className='text-base font-medium text-neutral-600'>
                    Available Balance
                  </p>
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger>
                        <HelpCircle className='h-4 w-4 text-neutral-400' />
                      </TooltipTrigger>
                      <TooltipContent>
                        Get rewards cashback and refunds, in your Pal Wallet for
                        quick processing Your balance never expires and equals
                        the Indian Rupee in value.
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </div>
                <p className='text-4xl font-bold text-neutral-900'>
                  ₹ {formattedBalance}
                </p>
              </div>
            </div>
            {/* <Button size='lg' variant={"primary"}>
      <Plus className='mr-2 h-5 w-5' /> Add Money
    </Button> */}
          </div>
        </Card>

        <Card className='overflow-hidden bg-gray-100 p-0'>
          <div className='hide-scrollbar max-h-[600px] overflow-auto'>
            <table className='w-full'>
              <thead className='sticky top-0 z-10 border-b border-neutral-200 bg-neutral-150'>
                <tr>
                  <th className='flex px-6 py-4 text-left text-xs font-semibold text-neutral-400'>
                    <span className='h-4 w-11 rounded-full'></span>
                    WALLET USED FOR
                  </th>
                  <th className='text-nowrap px-6 py-4 text-left text-xs font-semibold text-neutral-400'>
                    TRANSACTION AT
                  </th>
                  <th className='px-6 py-4 text-end text-xs font-semibold text-neutral-400'>
                    AMOUNT
                  </th>
                </tr>
              </thead>
              <tbody>
                {/* <tbody className="divide-y divide-neutral-200"> */}
                {transactions &&
                  transactions.map((transaction, index) => (
                    <motion.tr
                      key={`${transaction.source}-${transaction.created_at}`}
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: index * 0.05 }}
                      className='bg-gray-100'
                    >
                      <td className='min-w-max p-6'>
                        <div className='flex items-center gap-3 text-nowrap'>
                          <div
                            className={`rounded-full p-2 ${
                              transaction.amount > 0
                                ? "bg-emerald-50 text-emerald-600"
                                : "bg-red-50 text-red-600"
                            }`}
                          >
                            {transaction.amount > 0 ? (
                              <ArrowDownLeft className='h-4 w-4' />
                            ) : (
                              <ArrowUpRight className='h-4 w-4' />
                            )}
                          </div>
                          <div className='space-y-1'>
                            <p className='text-sm font-bold capitalize text-neutral-900'>
                              {transaction.detail}
                            </p>
                            <p className='text-xs font-medium text-gray-800'>
                              {transaction.source}
                            </p>
                          </div>
                        </div>
                      </td>
                      <td className='min-w-max text-nowrap px-6 py-4'>
                        <div className='space-y-1'>
                          <p className='text-xs font-medium text-neutral-900 md:text-sm'>
                            {formatTransactionDate(
                              new Date(transaction.created_at),
                            )}
                          </p>
                          {/* <p className='text-xs font-medium text-neutral-500'>
                  TRN No. #{Math.floor(Math.random() * 10000000000)}
                </p> */}
                        </div>
                      </td>
                      <td
                        className={`min-w-max whitespace-nowrap px-6 py-4 text-right text-sm font-semibold ${
                          transaction.amount > 0
                            ? "text-success-600"
                            : "text-destructive-600"
                        }`}
                      >
                        {transaction.amount > 0 ? "+" : "-"}₹
                        {Math.abs(transaction.amount).toLocaleString()}
                      </td>
                    </motion.tr>
                  ))}
              </tbody>
            </table>
          </div>
        </Card>
      </div>
      {/* */}
    </>
  )
}
