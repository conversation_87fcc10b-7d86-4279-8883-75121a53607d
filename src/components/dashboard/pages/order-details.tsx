"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Separator } from "@/components/ui/separator"
import { useQuery } from "@tanstack/react-query"
import { BadgeIndianRupee, FileBadge2Icon, InfoIcon } from "lucide-react"
import Link from "next/link"
import { useState } from "react"

import { OrderItemCard } from "@/components/cards/order-item-card"
import { Charges } from "@/components/checkout/order-summary-new/charge-item"
import ContactSupportHelp from "@/components/modals/contact-support-help"
import { OrderCancellationDialog } from "@/components/modals/order-cancellation"
import { OrderDetailsSkeleton } from "@/components/skeletons/order-details-skeleton"
import { Typography } from "@/components/ui/typography"
import { moneyFormatter } from "@/functions/small-functions"
import usePlaceOrder from "@/hooks/use-place-order"
import { fetchOrderStatus, fetchOrderSummary } from "@/services/orders"
import { useSearchParams } from "next/navigation"
import BikeDeliveryDetails from "../delivery-details"
import OrderDetailsHeader from "../order-details-header"
import { OrderStatus } from "../order-status"

const showModification = (stage: string) =>
  stage === "Order Received" ||
  stage === "Order Confirmed" ||
  stage === "KYC Received"

const showPayment = (stage: string) =>
  stage === "Order Packed" ||
  stage == "Order Shipped" ||
  stage === "Order Delivered" ||
  stage === "Delivery Scheduled" ||
  stage === "Delivery Partner Booked"

export default function OrderDetails({ orderId }: { orderId: string }) {
  const [orderCancelModal, setOrderCancelModal] = useState(false)
  const [openContactSupport, setOpenContactSupport] = useState(false)

  const params = useSearchParams()
  const [paramData] = useState(params.get("open"))

  const { hanldePaymentForOrder } = usePlaceOrder()

  const {
    data: orderSummary,
    isLoading: orderLoading,
    isError: orderError,
    error: orderErrorData,
  } = useQuery({
    queryKey: ["order-details-fetch", orderId],
    queryFn: () => fetchOrderSummary(orderId),
    refetchOnWindowFocus: true,
  })

  const {
    data: orderStatus,
    isLoading: statusLoading,
    isError: statusError,
  } = useQuery({
    queryKey: ["orderStatus", orderId],
    queryFn: () => fetchOrderStatus(orderId),
    enabled: !!orderId,
    refetchOnWindowFocus: false,
  })

  if (orderLoading || statusLoading) return <OrderDetailsSkeleton />
  if (orderError || statusError) {
    return (
      <div className='text-center text-red-500'>
        Error:{" "}
        {orderErrorData instanceof Error
          ? orderErrorData.message
          : "Failed to fetch order details"}
      </div>
    )
  }

  if (!orderSummary || !orderStatus)
    return (
      <div className='flex h-full w-full items-center justify-center px-4 py-12'>
        <div className='text-center'>
          <div className='mb-4 text-6xl text-red-500'>⚠️</div>
          <h2 className='text-2xl font-semibold text-gray-800'>
            Something Went Wrong
          </h2>
          <p className='mt-2 text-gray-600'>
            An unexpected error occurred. Please try again or contact support if
            the issue persists.
          </p>
          <div className='mt-6 flex justify-center gap-4'>
            <button
              onClick={() => location.reload()}
              className='inline-flex items-center rounded-xl bg-primary px-6 py-2 text-white shadow transition hover:bg-primary/90'
            >
              Retry
            </button>
            <Link
              href='https://api.whatsapp.com/send?phone=+917619220543&text=Hi'
              className='inline-flex items-center rounded-xl border border-gray-300 px-6 py-2 text-gray-700 transition hover:bg-gray-100'
            >
              Contact Support
            </Link>
          </div>
        </div>
      </div>
    )

  return (
    <div className='flex flex-col overflow-hidden'>
      <div className='relative grid gap-6 lg:grid-cols-[1fr_380px]'>
        {/* Main Content */}
        <div className='space-y-4 rounded-3xl bg-gray-100 p-4 pt-0 md:space-y-6 md:p-6'>
          <div className='hidden lg:block'>
            <OrderDetailsHeader
              bikeOrder={orderSummary?.bike_order}
              bikeDetails={orderSummary?.bike_details}
              isModification={showModification(
                orderSummary?.bike_details?.stage,
              )}
            />
          </div>

          <ContactSupportHelp
            openSideView={openContactSupport}
            setOpenSideView={setOpenContactSupport}
          />

          <OrderCancellationDialog
            orderId={String(orderSummary.bike_order.order_id) || ""}
            orderCancelModal={orderCancelModal}
            setOrderCancelModal={setOrderCancelModal}
          />

          <Separator className='hidden lg:block' />

          <BikeDeliveryDetails
            bikeOrder={orderSummary?.bike_order}
            bikeDetails={orderSummary?.bike_details}
            isModification={showModification(orderSummary?.bike_details?.stage)}
          />

          {/* Item Details */}
          <div className='w-full space-y-4'>
            {/* Header */}
            <div className='flex items-center gap-3'>
              <FileBadge2Icon className='h-6 w-6' />
              <Typography
                as={"h2"}
                className='text-sh4 font-bold text-neutral-900 md:text-h6'
              >
                Item Details:
              </Typography>
            </div>

            <div className='space-y-2 md:max-h-96 md:space-y-4 md:overflow-y-auto'>
              <OrderItemCard
                item={orderSummary?.bike}
                bikeOrder={orderSummary?.bike_order}
              />
            </div>
          </div>

          <Separator />

          {/* Order Summary */}
          <div className='w-full space-y-4'>
            {/* Header */}
            <div className='flex items-center justify-between gap-3'>
              <div className='flex items-center gap-3'>
                <BadgeIndianRupee className='h-6 w-6' />
                <Typography
                  as={"h2"}
                  className='text-sh4 text-primary-900 md:text-h6'
                >
                  Order Summary:
                </Typography>
              </div>
            </div>

            {/* Content  */}
            {orderSummary && (
              <Charges
                chargesData={{
                  total_rent: Number(orderSummary?.bike_order?.total_rent),
                  gst_amount: Number(orderSummary?.bike_order?.gst_amount),
                  // items_count: orderData?.cart_items?.length ?? 0,
                  coupon_discount: orderSummary?.bike_order?.coupon_discount,
                  wallet_balance_used:
                    orderSummary?.bike_order?.wallet_balance_used,
                  applied_coupon_code: orderSummary?.bike_order?.coupon_code,
                  wallet_used:
                    orderSummary?.bike_order?.wallet_balance_used > 0,
                  carepal_selected: orderSummary?.bike_order?.carepal_fee > 0,
                  carepal_fee: orderSummary?.bike_order?.carepal_fee || 0,
                }}
              />
            )}
          </div>

          <Separator />

          {/* More Details  */}
          <div className='flex items-center justify-between gap-4'>
            <div>
              <Typography
                as={"h2"}
                className='text-sh6 text-primary-900 md:text-sh2'
              >
                Order Total
              </Typography>
              <Typography as={"p"} className='text-o4 text-gray-800 md:text-b6'>
                price incl. of all taxes
              </Typography>
            </div>

            <Typography
              as={"h2"}
              className='text-sh2 text-primary-900 md:text-h4'
            >
              {moneyFormatter(orderSummary?.bike_order?.total_amount)}
            </Typography>
          </div>

          {/* Payment */}
          {orderSummary?.bike_details?.due_amount > 2 && (
            <>
              <Separator />
              <div className='flex flex-col items-start justify-between gap-4 md:flex-row md:items-center'>
                <div className='space-y-3'>
                  <Typography
                    as={"p"}
                    className='underline-offset-7 text-b4 text-gray-900 underline decoration-dotted md:text-b6'
                  >
                    Payment Mode
                  </Typography>
                  <div className='flex items-center justify-start gap-2'>
                    <Typography
                      as={"p"}
                      className='text-sh6 text-primary-900 md:text-sh3'
                    >
                      Pay via Cash on Delivery{" "}
                    </Typography>
                    <Typography
                      as={"span"}
                      className='flex min-w-max items-center gap-1 text-b6 text-destructive-600 md:text-b2'
                    >
                      (<InfoIcon className='h-3 w-3' /> Payment Pending )
                    </Typography>
                  </div>
                </div>

                {showPayment(orderSummary?.bike_details?.stage) && (
                  <Button
                    onClick={() =>
                      hanldePaymentForOrder({
                        order_id: orderId,
                        amount: orderSummary?.bike_details?.due_amount,
                      })
                    }
                    variant={"outline-primary"}
                    className='px-4 py-3 !text-bt2 max-md:!w-full md:w-auto md:!text-bt3'
                  >
                    Pay Rental Charges Online
                  </Button>
                )}
              </div>
            </>
          )}
        </div>

        <div className='relative row-start-1 w-full space-y-4 lg:row-start-auto lg:space-y-0'>
          <div className='flex rounded-2xl bg-gray-100 p-4 lg:hidden'>
            <OrderDetailsHeader
              bikeOrder={orderSummary?.bike_order}
              bikeDetails={orderSummary?.bike_details}
              isModification={showModification(
                orderSummary?.bike_details?.stage,
              )}
            />
          </div>

          {/* Order Status Sidebar */}
          <OrderStatus
            orderSummary={orderSummary}
            paramData={paramData ?? ""}
            order_id={orderId}
            setOpenContactSupport={setOpenContactSupport}
            setOrderCancelModal={setOrderCancelModal}
          />
        </div>
      </div>
    </div>
  )
}
