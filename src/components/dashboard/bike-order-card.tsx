"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader } from "@/components/ui/card"
import { Action, BikeDetails } from "@/types/order"
import { useQuery } from "@tanstack/react-query"
import {
  ArrowRightIcon,
  CheckCircle,
  ChevronRight,
  XCircleIcon,
} from "lucide-react"

import {
  formatDurationFromHours,
  formatTimestampForRental,
  formatTimestampToDisplayFormat,
} from "@/functions/date"
import { getImage, moneyFormatter } from "@/functions/small-functions"
import { cn } from "@/lib/utils"
import { fetchBikeOrderStatus } from "@/services/orders"
import SpImage from "@/shared/SpImage/sp-image"
import { Bike } from "@/types/common"
import Link from "next/link"
import { Typography } from "../ui/typography"

interface BikeOrderCardProps {
  order: BikeDetails
  bike: Bike
  onModify?: () => void
}

export function BikeOrderCard({ order, bike, onModify }: BikeOrderCardProps) {
  // const order = order?.bike_order_detail
  // const bike = order?.bike_detail

  const { data: stageActions } = useQuery({
    queryKey: ["bikeStageActions", order?.order_id],
    queryFn: () => fetchBikeOrderStatus(order?.order_id),
    select: (data) => data,
    enabled: order && order?.order_id !== "",
  })

  const getStatusDisplay = (status: string) => {
    switch (status) {
      case "Order Cancelled":
        return {
          label: stageActions?.activeStage ?? "",
          description: stageActions?.stageInfo.message ?? "",
          className: "text-red-600 !border-red-600",
          icon: <XCircleIcon className='h-6 w-6' />,
        }
      default:
        return {
          label: stageActions?.activeStage ?? "",
          description: stageActions?.stageInfo.message ?? "",
          action: "Modify Order",
          onAction: onModify,
          className: "text-green-600",
          icon: <CheckCircle className='h-6 w-6' />,
        }
    }
  }

  const statusDisplay = getStatusDisplay(order?.stage)

  const pickupDateTime = formatTimestampForRental(order?.start_time)
  const dropoffDateTime = formatTimestampForRental(order?.end_time)
  const formattedDuration = formatDurationFromHours(order?.total_hours)

  return (
    <Card className='overflow-hidden rounded-3xl border-2 border-neutral-200'>
      <CardHeader className='flex flex-row items-center justify-between gap-2 space-y-0 bg-neutral-150 p-4 px-3 py-2 md:gap-6'>
        {/* left  */}
        <div className='hidden items-center justify-between gap-3 md:flex md:gap-6'>
          <div className='space-y-0.5'>
            <p className='text-[10px] uppercase tracking-wide text-neutral-500'>
              ORDER PLACED ON:
            </p>
            <p className='text-sm font-medium'>
              {formatTimestampToDisplayFormat(order?.created_at)}
            </p>
          </div>
          <div className='space-y-0.5'>
            <p className='text-[10px] uppercase tracking-wide text-neutral-500'>
              CONTACT:
            </p>
            <p className='text-sm font-medium'>{order?.calling_number}</p>
          </div>
          <div className='space-y-0.5'>
            <p className='text-[10px] uppercase tracking-wide text-neutral-500'>
              RENTAL DURATION:
            </p>
            <p className='text-sm font-medium'>{order?.total_hours} Hours</p>
          </div>
        </div>

        <div className='flex w-full items-center justify-between gap-2 md:w-auto'>
          <div className='space-y-0.5'>
            <p className='text-[10px] uppercase tracking-wide text-neutral-500'>
              ORDER NO:
            </p>
            <p className='text-sm font-medium'>{order?.order_id}</p>
          </div>
          <Button
            size={"icon"}
            variant={"outline"}
            asChild
            className='border-neutral-200 bg-gray-100'
          >
            <Link href={`/dashboard/orders/${order?.order_id}`}>
              <ChevronRight className='h-5 w-5 text-neutral-400' />
            </Link>
          </Button>
        </div>
      </CardHeader>

      <CardContent className='bg-gray-100 p-2 md:space-y-6 md:px-4 md:py-3'>
        {/* desktop */}
        <div className='hidden w-full md:block'>
          <BikeStatusDisplay
            stageActions={stageActions?.stageInfo.actions || []}
            statusDisplay={statusDisplay}
            order={order}
          />
        </div>

        <div className='flex flex-col items-start gap-y-2 md:gap-3 lg:flex-row'>
          <div className='col-span-2 flex w-full flex-1 items-start justify-start gap-4 rounded-xl bg-neutral-150 p-2 md:flex-[55%] md:rounded-2xl md:p-3'>
            {/* Bike Image */}
            <div className='flex'>
              <div className='relative h-14 w-14 overflow-hidden rounded-lg border bg-gray-100 md:h-20 md:w-20 md:rounded-xl'>
                <SpImage
                  src={getImage(bike?.bike_images)}
                  alt={bike?.name}
                  fill
                  className='h-full w-full object-contain p-0.5'
                />
              </div>
            </div>

            {/* Bike details */}
            <div className='flex-1'>
              <p className='mb-0 text-[10px] font-medium uppercase tracking-wide text-neutral-500 md:mb-1'>
                BIKE DETAILS:
              </p>
              <div className='space-y-0.5'>
                <p className='line-clamp-1 text-wrap break-words text-o4 text-neutral-600 md:text-b6'>
                  {bike?.name}
                </p>
                <p className='text-xs text-neutral-600'>
                  Fuel Type: {bike?.fuel_type}
                </p>
                <p className='text-b4'>
                  {moneyFormatter(
                    order?.total_rent_per_hour * order?.total_hours,
                  )}
                </p>
              </div>
            </div>
          </div>

          <div className='w-full flex-1 rounded-xl bg-neutral-150 p-3 shadow-sm md:flex-[45%]'>
            <p className='mb-1 text-[10px] font-medium uppercase tracking-wide text-neutral-500'>
              Rental Period:
            </p>
            <div className='flex items-center justify-between'>
              <div className='text-center'>
                <Typography
                  as='p'
                  className='font-ubuntu text-b4 text-neutral-700 md:text-b3'
                >
                  {pickupDateTime.displayFormat}
                </Typography>
              </div>
              <div className='flex items-center gap-2'>
                <ArrowRightIcon className='h-4 w-4 text-neutral-400' />
              </div>
              <div className='text-center'>
                <Typography
                  as='p'
                  className='font-ubuntu text-b4 font-semibold text-neutral-700 md:text-b3'
                >
                  {dropoffDateTime.displayFormat}
                </Typography>
              </div>
            </div>
            <div className='mt-2 text-center'>
              <div className='flex items-center justify-center gap-2'>
                <div className='flex-1 border-t border-dashed border-neutral-300'></div>
                <Typography
                  as='span'
                  className='rounded bg-neutral-100 px-2 py-1 font-ubuntu text-b5 font-semibold text-gray-700 md:text-b4'
                >
                  {formattedDuration}
                </Typography>
                <div className='flex-1 border-t border-dashed border-neutral-300'></div>
              </div>
            </div>
          </div>
        </div>

        {/* mobile */}
        <div className='mt-3 block w-full md:hidden'>
          <BikeStatusDisplay
            stageActions={stageActions?.stageInfo.actions || []}
            statusDisplay={statusDisplay}
            order={order}
          />
        </div>
      </CardContent>
    </Card>
  )
}

interface StatusDisplayProps {
  statusDisplay: {
    label: string
    description: string
    action?: string
    onAction?: () => void
    className: string
    icon: React.ReactNode
  }
  stageActions: Action[]
  order: BikeDetails
}

const BikeStatusDisplay = ({ statusDisplay }: StatusDisplayProps) => {
  return (
    <div className='flex flex-col items-start justify-between gap-3 pb-1 md:flex-row md:pb-0'>
      <div className={cn("space-y-1")}>
        <h6
          className={cn(
            "flex items-center gap-2 text-sm font-semibold md:text-xl",
            statusDisplay.className,
          )}
        >
          {statusDisplay.icon}
          {statusDisplay.label}
        </h6>
        <p className='text-xs font-medium text-primary-900 md:text-sm'>
          {statusDisplay.description}
        </p>
      </div>

      {statusDisplay.action && statusDisplay.action != "Modify Order" && (
        <Button
          variant='outline-primary'
          className='ml-auto h-9 w-full md:h-10 md:w-auto'
          onClick={statusDisplay.onAction}
        >
          {statusDisplay.action}
        </Button>
      )}
    </div>
  )
}
