"use client"

import { cn } from "@/lib/utils"
import { type FilterValue } from "@/types/order"
import { AnimatePresence, easeInOut, motion } from "framer-motion"
import { FilterIcon } from "lucide-react"
import * as React from "react"

interface CustomDropdownProps {
  selectedFilters: FilterValue[]
  onToggleFilter: (filter: FilterValue) => void
  onRemoveFilter: (filter: FilterValue) => void
}

const filterOptions: { value: FilterValue; label: string }[] = [
  { value: "cancelled", label: "Cancelled Orders" },
  { value: "active", label: "Active Orders" },
  { value: "completed", label: "Completed Order" },
]

const dropdownVariants = {
  hidden: {
    opacity: 0,
    scale: 0.95,
    transition: {
      duration: 0.15,
      ease: easeInOut,
    },
  },
  visible: {
    opacity: 1,
    scale: 1,
    transition: {
      duration: 0.15,
      ease: easeInOut,
    },
  },
}

const itemVariants = {
  hover: {
    backgroundColor: "rgba(0, 0, 0, 0.05)",
    transition: { duration: 0.2 },
  },
  initial: {
    backgroundColor: "rgba(0, 0, 0, 0)",
    transition: { duration: 0.2 },
  },
}

export function CustomDropdown({
  selectedFilters,
  onToggleFilter,
  onRemoveFilter,
}: CustomDropdownProps) {
  const [isOpen, setIsOpen] = React.useState(false)
  const dropdownRef = React.useRef<HTMLDivElement>(null)

  // Close dropdown when clicking outside
  React.useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false)
      }
    }

    document.addEventListener("mousedown", handleClickOutside)
    return () => document.removeEventListener("mousedown", handleClickOutside)
  }, [])

  return (
    <div className='flex items-center gap-3'>
      <div className='flex flex-wrap items-center gap-2'>
        {selectedFilters.map((filter) => {
          const option = filterOptions.find((f) => f.value === filter)
          if (!option) return null

          return (
            <motion.div
              key={filter}
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.8 }}
              className='flex items-center gap-2 rounded-full bg-neutral-100 px-4 py-2'
            >
              <span className='text-sm font-medium'>{option.label}</span>
              <button
                className='text-neutral-400 hover:text-neutral-600'
                onClick={() => onRemoveFilter(filter)}
              >
                <span className='sr-only'>Remove {option.label} filter</span>×
              </button>
            </motion.div>
          )
        })}
      </div>

      <div className='relative' ref={dropdownRef}>
        <button
          onClick={() => setIsOpen(!isOpen)}
          className={cn(
            "flex h-8 w-8 items-center justify-center rounded-full border-2 transition-colors md:h-10 md:w-10",
            isOpen ? "border-neutral-400" : "border-neutral-200",
          )}
        >
          <FilterIcon className='h-4 w-4' />
        </button>

        <AnimatePresence>
          {isOpen && (
            <motion.div
              initial='hidden'
              animate='visible'
              exit='hidden'
              variants={dropdownVariants}
              className='absolute right-0 top-[calc(100%+8px)] min-w-[200px] rounded-xl border bg-gray-100 p-1 shadow-lg'
            >
              {filterOptions.map((option) => {
                const isSelected = selectedFilters.includes(option.value)

                return (
                  <motion.button
                    key={option.value}
                    variants={itemVariants}
                    whileHover='hover'
                    initial='initial'
                    className={cn(
                      "relative flex w-full items-center justify-between rounded-lg px-4 py-2 text-left text-sm",
                      isSelected && "font-medium",
                    )}
                    onClick={() => onToggleFilter(option.value)}
                  >
                    {option.label}
                    {isSelected && (
                      <motion.div
                        layoutId='selected-dot'
                        className='h-2 w-2 rounded-full bg-blue-600'
                        initial={{ scale: 0 }}
                        animate={{ scale: 1 }}
                        exit={{ scale: 0 }}
                      />
                    )}
                  </motion.button>
                )
              })}
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </div>
  )
}
