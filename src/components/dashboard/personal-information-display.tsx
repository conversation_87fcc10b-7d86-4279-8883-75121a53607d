"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Skeleton } from "@/components/ui/skeleton"
import { useUserStore } from "@/store/user-store"
import { Edit } from "lucide-react"
import { useState } from "react"
import { PersonalInformationForm } from "./personal-information-form"

export function PersonalInformationDisplay() {
  const [isEditing, setIsEditing] = useState(false)
  const { user, userLoading } = useUserStore()

  const formatDate = (timestamp: number) =>
    new Date(timestamp).toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
    })

  if (userLoading || !user) {
    return (
      <Card className='min-h-72 border-0 border-none bg-gray-100'>
        <CardHeader className='flex flex-row items-center justify-between'>
          <Skeleton className='h-8 w-[200px]' />
          <Skeleton className='h-10 w-[60px]' />
        </CardHeader>
        <CardContent>
          <div className='grid gap-4 md:grid-cols-2'>
            {[...Array(6)].map((_, index) => (
              <div key={index} className='space-y-2'>
                <Skeleton className='h-4 w-[100px]' />
                <Skeleton className='h-6 w-[150px]' />
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className='min-h-72 border-0 bg-gray-100'>
      <CardHeader className='flex flex-row items-center justify-between p-0 md:p-6'>
        <CardTitle className='text-xl font-semibold text-neutral-900 md:text-2xl'>
          Personal Information
        </CardTitle>
        {!isEditing && (
          <Button variant='ghost' onClick={() => setIsEditing(true)}>
            <Edit className='h-4 w-4' /> Edit
          </Button>
        )}
      </CardHeader>
      <CardContent className='p-2 pt-3 md:p-6 md:pt-0'>
        {isEditing ? (
          <PersonalInformationForm onCancel={() => setIsEditing(false)} />
        ) : (
          <div className='grid gap-4 md:grid-cols-2'>
            <div className='space-y-1'>
              <p className='text-sm text-neutral-500'>Full Name</p>
              <p className='text-sm font-medium md:text-base'>{`${user?.first_name} ${user?.last_name}`}</p>
            </div>
            <div className='space-y-1'>
              <p className='text-sm text-neutral-500'>Email</p>
              <p className='text-sm font-medium md:text-base'>{user?.email}</p>
            </div>
            <div className='space-y-1'>
              <p className='text-sm text-neutral-500'>Phone</p>
              <p className='text-sm font-medium md:text-base'>{`+${user?.country_code_calling} ${user?.calling_number}`}</p>
            </div>
            <div className='space-y-1'>
              <p className='text-sm text-neutral-500'>WhatsApp</p>
              <p className='text-sm font-medium md:text-base'>{`+${user?.country_code_whatsapp} ${user?.whatsapp_number}`}</p>
            </div>
            {/* <div className='space-y-1'>
              <p className='text-sm text-neutral-500'>Gender</p>
              <p className="font-medium">{user.gender}</p>
            </div> */}
            <div className='space-y-1'>
              <p className='text-sm text-neutral-500'>Date of Birth</p>
              <p className='text-sm font-medium md:text-base'>
                {user?.date_of_birth ? formatDate(user.date_of_birth) : ""}
              </p>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
