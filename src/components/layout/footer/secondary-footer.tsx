"use client"
import { cn } from "@/lib/utils"
import { usePathname } from "next/navigation"
import {
  FacebookFilledIcon,
  InstagramFilledIcon,
  LinkedInFilledIcon,
  MailOutlinedIcon,
  PhoneCallFilledIcon,
} from "sharepal-icons"
import FooterInfo from "./footer-info"

export const icons = {
  call: <PhoneCallFilledIcon />,
  mail: <MailOutlinedIcon />,
  facebook: <FacebookFilledIcon />,
  instagram: <InstagramFilledIcon />,
  linkedin: <LinkedInFilledIcon />,
}

const SecondaryFooter = () => {
  const pathname = usePathname()
  const isCheckoutPage = pathname.includes("/checkout")
  const isReturnOrderPage = pathname.includes("/return-order")
  const isOrderDetailsPage = pathname.match(/\/orders\/[^/]+$/)

  const shouldHideOnMobile =
    isCheckoutPage || isOrderDetailsPage || isReturnOrderPage

  return (
    <footer
      className={cn(
        "bg-primary-900 px-1 py-5 md:py-[72px]",
        // Hide on mobile for checkout and order details pages
        shouldHideOnMobile ? "hidden md:block" : "",
      )}
    >
      <div className='container mx-auto flex w-full flex-col gap-12'>
        {/* Information */}
        <FooterInfo />
      </div>
    </footer>
  )
}

export default SecondaryFooter
