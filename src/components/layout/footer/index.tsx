import {
  FacebookFilledIcon,
  InstagramFilledIcon,
  LinkedInFilledIcon,
  MailOutlinedIcon,
  PhoneCallFilledIcon,
} from "sharepal-icons"
import FooterInfo from "./footer-info"

export const icons = {
  call: <PhoneCallFilledIcon />,
  mail: <MailOutlinedIcon />,
  facebook: <FacebookFilledIcon />,
  instagram: <InstagramFilledIcon />,
  linkedin: <LinkedInFilledIcon />,
}

const Footer = async () => {
  return (
    <>
      <footer className='bg-primary-900 py-5 pb-16 md:py-[72px] md:pb-10'>
        <div className='container mx-auto flex w-full flex-col gap-5 md:gap-12'>
          {/* Information */}
          <FooterInfo />
        </div>
      </footer>
    </>
  )
}

export default Footer
