"use client"

import { Bad<PERSON> } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { moneyFormatter } from "@/functions/small-functions"
import useMediaQuery from "@/hooks/use-media-query"
import { cn } from "@/lib/utils"
import { useUserStore } from "@/store/user-store"
import { AnimatePresence, motion } from "framer-motion"
import { ChevronRight } from "lucide-react"
import Link from "next/link"
import { useParams, usePathname } from "next/navigation"
import * as React from "react"
import {
  AccountOutlinedIcon,
  BoxOutlinedIcon,
  HelpOutlinedIcon,
  WalletOutlinedIcon,
} from "sharepal-icons"

type MenuItem = {
  title: string
  key: string
  icon: React.ElementType
  href: string
  target?: string
  subItems?: { key: string; title: string; href: string }[]
  badge?: {
    icon?: React.ReactNode
    text: string
    variant: string
  }
}

const MenuItemComponent: React.FC<{
  item: MenuItem
  isActive: boolean
  isMobile: boolean
  onClick: () => void
}> = ({ item, isActive, isMobile, onClick }) => (
  <Button
    asChild
    variant='ghost'
    className={cn(
      "relative h-9 justify-start gap-0 whitespace-nowrap rounded-b-none rounded-t-xl text-xs font-medium text-neutral-600 hover:bg-neutral-150 hover:text-neutral-900 lg:h-11 lg:w-full lg:gap-2 lg:rounded-xl lg:text-sm",
      isActive &&
        "bg-transparent text-primary after:absolute after:bottom-0 after:left-0 after:h-0.5 after:w-full after:bg-primary lg:bg-neutral-150 lg:text-neutral-900 lg:after:hidden",
    )}
    onClick={onClick}
  >
    <Link
      href={item.href}
      target={item.target}
      className='flex w-full items-center gap-1 px-3 lg:px-4'
      prefetch={false}
    >
      <item.icon className='h-5 w-5 shrink-0' />
      <span className='ml-2 flex-1 text-left'>{item.title}</span>

      {item.badge ? (
        <Badge
          variant='outline'
          className={cn(
            "ml-auto shrink-0",
            item.badge.variant === "success" &&
              "border-success-500 bg-success-100 text-success-600",
          )}
        >
          {item.badge.icon}
          {item.badge.text}
        </Badge>
      ) : null}
      {item.subItems && !isMobile && (
        <ChevronRight
          className={cn(
            "ml-2 h-4 w-4 shrink-0 text-neutral-400 transition-transform",
            isActive && "rotate-90",
          )}
        />
      )}
    </Link>
  </Button>
)

const SubMenuComponent: React.FC<{
  subItems: MenuItem["subItems"]
  isMobile: boolean
}> = ({ subItems, isMobile }) => {
  const pathname = usePathname()

  return (
    <motion.div
      initial={{ height: 0, opacity: 0 }}
      animate={{ height: "auto", opacity: 1 }}
      exit={{ height: 0, opacity: 0 }}
      transition={{ duration: 0.2 }}
      className={cn(
        "w-full",
        isMobile ? "hide-scrollbar flex overflow-x-auto" : "space-y-1",
      )}
    >
      {subItems?.map((subItem) => (
        <Button
          key={subItem.title}
          variant='ghost'
          asChild
          className={cn(
            "h-9 justify-start whitespace-nowrap rounded-xl text-xs text-neutral-600 hover:bg-neutral-150 hover:text-neutral-900 lg:mt-2 lg:h-11 lg:w-full lg:text-sm",
            "px-4",
            !isMobile && "pl-11",
            pathname === subItem.href
              ? "bg-neutral-150 text-neutral-900"
              : "bg-transparent",
          )}
        >
          <Link href={subItem.href}>{subItem.title}</Link>
        </Button>
      ))}
    </motion.div>
  )
}

export function DashboardSidebar() {
  const pathname = usePathname()
  const params = useParams()
  const { wallet } = useUserStore()
  const [activeSubmenu, setActiveSubmenu] = React.useState<string | null>(null)
  const isMobile = useMediaQuery("(max-width: 1023px)")

  const menuItems: MenuItem[] = React.useMemo(
    () => [
      {
        title: "My Account",
        key: "account",
        icon: AccountOutlinedIcon,
        href: "/dashboard/account",
      },
      {
        title: "My Orders",
        icon: BoxOutlinedIcon,
        key: "orders",
        href: "/dashboard/orders",
      },

      {
        title: "Pal Wallet",
        icon: WalletOutlinedIcon,
        key: "wallet",
        href: "/dashboard/wallet",
        badge: {
          icon: <WalletOutlinedIcon className='mr-1' />,
          text: `${moneyFormatter(wallet?.amount ?? 0)}`,
          variant: "success",
        },
      },

      {
        title: "Help & Support",
        icon: HelpOutlinedIcon,
        key: "support",
        href: "https://wa.me/************",
        target: "_blank",
      },
    ],
    [wallet?.amount],
  )

  const isOrderDetailsPage = React.useMemo(
    () => !!params.orderId,
    [params.orderId],
  )

  const activeMenuItem = React.useMemo(
    () =>
      menuItems.find(
        (item) =>
          item.href === pathname ||
          item.subItems?.some((subItem) => subItem.href === pathname),
      ),
    [menuItems, pathname],
  )

  React.useEffect(() => {
    if (activeMenuItem?.subItems) {
      setActiveSubmenu(activeMenuItem.key)
    }
  }, [activeMenuItem])

  const toggleSubmenu = React.useCallback((title: string) => {
    setActiveSubmenu((prev) => (prev === title ? null : title))
  }, [])

  const renderMobileView = () => {
    const subItems = menuItems.find(
      (item) => item.key === activeSubmenu,
    )?.subItems

    return (
      <nav className='hide-scrollbar flex flex-col'>
        <div className='hide-scrollbar flex overflow-x-auto px-4 py-3'>
          {menuItems.map((item) => (
            <MenuItemComponent
              key={item.title}
              item={item}
              // isActive={item.href === pathname}
              isActive={activeSubmenu === item.key}
              isMobile={true}
              onClick={() => toggleSubmenu(item.key)}
            />
          ))}
        </div>
        <AnimatePresence>
          {subItems && (
            <div className='rounded-none border-t px-4 py-1'>
              <SubMenuComponent subItems={subItems} isMobile={true} />
            </div>
          )}
        </AnimatePresence>
      </nav>
    )
  }

  const renderDesktopView = () => (
    <nav className='space-y-2 p-4'>
      {menuItems.map((item) => (
        <div key={item.title}>
          <MenuItemComponent
            item={item}
            // isActive={item.href === pathname}
            isActive={activeSubmenu === item.key}
            isMobile={false}
            onClick={() => toggleSubmenu(item.key)}
          />
          <AnimatePresence>
            {item.subItems && activeSubmenu === item.key && (
              <SubMenuComponent subItems={item.subItems} isMobile={false} />
            )}
          </AnimatePresence>
        </div>
      ))}
    </nav>
  )

  return (
    <AnimatePresence>
      {!isOrderDetailsPage && (
        <motion.div
          initial={{ opacity: 0, x: -50 }}
          animate={{ opacity: 1, x: 0 }}
          exit={{ opacity: 0, x: -50 }}
          transition={{ duration: 0.3, ease: "easeInOut" }}
          className='sticky top-0 z-50 max-h-[calc(100vh-5rem)] w-full rounded-b-xl bg-gray-100 lg:top-6 lg:w-80 lg:rounded-3xl lg:bg-gray-100'
        >
          {isMobile ? renderMobileView() : renderDesktopView()}
        </motion.div>
      )}
    </AnimatePresence>
  )
}
