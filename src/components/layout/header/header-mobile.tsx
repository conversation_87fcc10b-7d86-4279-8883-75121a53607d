"use client"
import { <PERSON>hare<PERSON><PERSON><PERSON><PERSON> } from "@/components/common/logo"

import { But<PERSON> } from "@/components/ui/button"
import { Skeleton } from "@/components/ui/skeleton"
import { Typography } from "@/components/ui/typography"
import { cn } from "@/lib/utils"
// import { City } from "@/types/address"
import { User } from "@/types/user"
import {
  getHeaderBorderColor,
  getSuperCategoryBgColor,
} from "@/utils/get-bg-color"
import Link from "next/link"
import React, { useState } from "react"

import { BikeRentalInputs } from "@/components/bike-rental/bike-rental-inputs"
import { formatTimeDisplay } from "@/lib/bike-rental-utils"
import { useBikeRentalStore } from "@/store/bike-rental-store"
import { City } from "@/types/common"
import { formatDate } from "@/utils/date-logics"
import { AnimatePresence, motion } from "framer-motion"
import { ArrowRight, XIcon } from "lucide-react"
import { usePathname } from "next/navigation"
import {
  CalendarAddFilledIcon,
  ChevronDownIcon,
  LocationPointerOutlinedIcon,
  UserOutlinedIcon,
} from "sharepal-icons"

interface HeaderMobileProps {
  selectedCity: City
  setCityModalOpen: React.Dispatch<React.SetStateAction<boolean>>
  setIsProfileOpen: React.Dispatch<React.SetStateAction<boolean>>
  delivery_date: Date | null
  openBikeRental: () => void

  pickup_date: Date | null
  isLoggedIn: boolean
  setIsLoggedIn: (isTrue: boolean) => void
  user: User | null
  specialRoute?: boolean
  category?: string
}

export const HeaderMobile: React.FC<HeaderMobileProps> = ({
  selectedCity,
  openBikeRental,
  setCityModalOpen,
  specialRoute,
  setIsProfileOpen,
  category,
}) => {
  // const { scrollY } = useScroll()
  const isScrolled = false // Now
  const pathname = usePathname()
  const [isMobileDrawerOpen, setIsMobileDrawerOpen] = useState(false)

  const { pickup_date, dropoff_date, pickup_time, dropoff_time } =
    useBikeRentalStore()

  const handleMobileSearchClick = () => {
    setIsMobileDrawerOpen(false)
  }
  //close mobile drawer on page change
  React.useEffect(() => {
    setIsMobileDrawerOpen(false)
  }, [pathname])

  // console.log("mobile header re render")
  return (
    <div className='w-full lg:hidden'>
      <div className={cn("mobile w-full bg-gray-50")}>
        <div className='container flex h-full w-full items-center justify-between gap-1 pb-2'>
          <Link
            href={selectedCity ? `/${selectedCity.city_url}` : "/"}
            className='logo flex h-10 flex-col items-center justify-end gap-1 rounded-bl-xl rounded-br-xl bg-primary-500 px-3 pb-1 pt-3'
            prefetch={true}
          >
            <div className='flex w-full max-w-28 items-center justify-center'>
              <SharePalLogo />
            </div>
          </Link>
          <div
            className={cn(
              "flex w-full items-center justify-end gap-1.5 pt-1.5 md:gap-4",
              specialRoute && !isScrolled
                ? "fill-gray-100 text-gray-900"
                : "fill-gray-900 text-gray-100",
            )}
          >
            <button
              type='button'
              onClick={(e) => {
                e.preventDefault()
                setCityModalOpen(true)
              }}
              className={cn(
                "city flex items-center justify-center gap-1 rounded-l-full border border-input bg-neutral-200 px-2 py-0.5 text-xs text-primary-900 max-lg:rounded-full",
                specialRoute &&
                  !isScrolled &&
                  getSuperCategoryBgColor(category),

                !isScrolled && getHeaderBorderColor(pathname),
                specialRoute &&
                  !isScrolled &&
                  "fill-gray-100 font-medium text-gray-100 shadow-md", // Additional styles if needed
              )}
            >
              <LocationPointerOutlinedIcon
                className={cn(
                  "w-4 md:w-5",
                  specialRoute && !isScrolled && "fill-gray-100",
                )}
              />

              {selectedCity.city_name ? (
                <Typography as={"p"} className='min-w-4 text-bt4'>
                  {selectedCity.city_name}{" "}
                </Typography>
              ) : (
                <Skeleton className='h-4 w-16 bg-gray-200' />
              )}
              <ChevronDownIcon className='w-3 font-bold md:w-4' />
            </button>

            <div className='profile items-center justify-center gap-1 p-0 sm:flex'>
              <Button
                onClick={() => setIsProfileOpen(true)}
                variant='normal'
                className={cn(
                  "h-8 min-h-8 w-8 min-w-8 cursor-pointer rounded-full border-2 border-solid border-neutral-200 p-0.5",
                  specialRoute && !isScrolled
                    ? // ? 'bg-gray-100 fill-neutral-900 hover:bg-gray-200'
                      "bg-neutral-900 text-gray-100 hover:bg-neutral-950"
                    : "bg-neutral-900 text-gray-100 hover:bg-neutral-950",
                )}
              >
                <UserOutlinedIcon />
              </Button>
            </div>
          </div>
        </div>
      </div>

      <div className='container w-full rounded-b-2xl border-t border-neutral-200 bg-gradient-to-b from-gray-50 to-bike-200 py-2 pb-3 lg:hidden'>
        {/* mobile bike rental selector visible part */}
        {pickup_date && dropoff_date && pickup_time && dropoff_time ? (
          <div
            className={cn(
              "mb-2.5 flex w-full items-center justify-between gap-1 rounded-2xl border-2 border-neutral-200 bg-gray-100",
              !isScrolled && getHeaderBorderColor(pathname),
            )}
          >
            <div className='relative flex w-full items-center justify-between rounded-2xl bg-neutral-50 p-3 shadow-sm'>
              <div className='flex-1 text-start'>
                <Typography
                  as='p'
                  className='font-ubuntu text-b4 text-neutral-700 md:text-b3'
                >
                  {formatDate(pickup_date)}
                </Typography>
                <Typography
                  as='p'
                  className='font-inter text-b6 font-bold text-neutral-500 md:hidden'
                >
                  {formatTimeDisplay(pickup_time)}
                </Typography>
              </div>
              <div className='flex -translate-y-2 items-center gap-2'>
                <ArrowRight className='h-4 w-4 text-neutral-900' />
              </div>
              <div className='flex-1 text-end'>
                <Typography
                  as='p'
                  className='font-ubuntu text-b4 text-neutral-700 md:text-b3'
                >
                  {formatDate(dropoff_date)}
                </Typography>
                <Typography
                  as='p'
                  className='font-inter text-b6 font-bold text-neutral-500 md:hidden'
                >
                  {formatTimeDisplay(dropoff_time)}
                </Typography>
              </div>

              {/* Edit button in bottom center */}
              <div className='absolute bottom-[-14px] left-1/2 -translate-x-1/2'>
                <Button
                  onClick={() => setIsMobileDrawerOpen(true)}
                  size={"sm"}
                  className={
                    "add-date h-full gap-1 px-2 py-[6px] pr-3 hover:bg-primary-900 max-lg:text-xs"
                  }
                  // className="add-date h-full border-2 border-gray-100 px-2 py-[5px] max-lg:text-xs"
                >
                  <CalendarAddFilledIcon className='min-h-3 min-w-3' />
                  Edit
                </Button>
              </div>
            </div>
            {/* <div className='mt-2 text-center'>
              <div className='flex items-center justify-center gap-2'>
                <div className='flex-1 border-t border-dashed border-neutral-300'></div>
                <Typography
                  as='span'
                  className='rounded bg-neutral-100 px-2 font-ubuntu text-b5 font-semibold text-primary-600 md:text-b4'
                >
                  {formattedDuration}
                </Typography>
                <div className='flex-1 border-t border-dashed border-neutral-300'></div>
              </div>
            </div> */}
          </div>
        ) : (
          <div
            className={cn(
              "flex w-full items-center justify-between gap-1 rounded-full border-2 border-neutral-200 bg-gray-100",
              !isScrolled && getHeaderBorderColor(pathname),
            )}
          >
            <div className='bike-rental-dates flex items-center justify-center gap-1 px-0 pl-2 text-xs font-semibold lg:text-sm'>
              <CalendarAddFilledIcon className='mx-1 w-4' />

              <Typography as={"span"} className='text-sh5 text-neutral-700'>
                Select Date & Time
              </Typography>
            </div>

            <Button
              onClick={() => setIsMobileDrawerOpen(true)}
              className='add-date h-full gap-1 px-2 py-[6px] pr-3 hover:bg-primary-900 max-lg:text-xs'
              // className="add-date h-full border-2 border-gray-100 px-2 py-[5px] max-lg:text-xs"
            >
              <CalendarAddFilledIcon className='min-h-3 min-w-3' />
              Select
            </Button>
          </div>
        )}

        {/* mobile bike rental selector modal part */}
        <AnimatePresence>
          {isMobileDrawerOpen && (
            <>
              {/* Backdrop with blur */}
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                transition={{ duration: 0.3 }}
                className='fixed inset-0 z-50 bg-black/50 backdrop-blur-sm lg:hidden'
                onClick={() => setIsMobileDrawerOpen(false)}
              />

              {/* Drawer Content - Coming from top */}
              <motion.div
                initial={{ y: "-100%", opacity: 0 }}
                animate={{ y: 0, opacity: 1 }}
                exit={{ y: "-100%", opacity: 0 }}
                transition={{ duration: 0.3, ease: "easeInOut" }}
                className='fixed left-0 right-0 top-0 z-50 rounded-b-2xl bg-gradient-to-b from-gray-50 to-bike-200 p-4 shadow-xl lg:hidden lg:p-6'
              >
                {/* Date/Time Inputs with Search Button */}
                <div className='space-y-4'>
                  <BikeRentalInputs
                    isDesktop={false}
                    onSearchClick={handleMobileSearchClick}
                  />
                </div>

                {/* Floating Close Button */}
                <button
                  onClick={() => setIsMobileDrawerOpen(false)}
                  className='absolute -bottom-16 left-1/2 flex h-12 w-12 -translate-x-1/2 items-center justify-center rounded-full bg-white shadow-lg'
                >
                  <XIcon className='h-6 w-6 text-gray-600' />
                </button>
              </motion.div>
            </>
          )}
        </AnimatePresence>
      </div>
    </div>
  )
}
