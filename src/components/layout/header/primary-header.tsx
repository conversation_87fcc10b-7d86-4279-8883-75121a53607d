"use client"

import { BikeRentalSelector } from "@/components/bike-rental/bike-rental-selector"
import CityDialog from "@/components/custom/select-city"
import { UserProfileSheet } from "@/components/modals/user-profile-sheet"
import { OnboardingFlow } from "@/components/onboarding"
import { But<PERSON> } from "@/components/ui/button"
import { useHeaderLogic } from "@/hooks/use-header"
import { cn } from "@/lib/utils"
import { AnimatePresence, motion } from "framer-motion"
import { Suspense } from "react"

import { IconWhatsapp } from "@/components/icons/common"
import WhatsappSupportModal from "@/components/modals/whatsapp-support"
import { City } from "@/types/common"
import { HeaderDesktop } from "./header-desktop"
import { HeaderMobile } from "./header-mobile"

interface IHeaderProps {
  cities: City[]
}

const Header = ({ cities }: IHeaderProps) => {
  const {
    setIsWhatsappSupportModalOpen,
    setCityModalOpen,
    setIsProfileOpen,
    delivery_date,
    pickup_date,
    selectedCity,
    user,
    isLoggedIn,
    setIsLoggedIn,

    cat,
    isProfileOpen,
    specialRoute,
    isCityModalOpen,
    openBikeRental,
    isWhatsappSupportModalOpen,
  } = useHeaderLogic()

  return (
    <>
      <CityDialog
        isOpen={isCityModalOpen}
        onOpenChange={setCityModalOpen}
        cities={cities}
      />
      <UserProfileSheet
        isOpen={isProfileOpen}
        setIsProfileOpen={setIsProfileOpen}
      />
      <WhatsappSupportModal
        openView={isWhatsappSupportModalOpen}
        setOpenView={setIsWhatsappSupportModalOpen}
      />

      <AnimatePresence>
        <motion.header
          className={cn(
            "absolute left-0 right-0 top-0 z-50 flex h-max w-full flex-col items-center justify-center overflow-hidden pb-3 opacity-100 transition-all duration-500 md:pb-4 lg:flex-row",
          )}
        >
          <HeaderDesktop
            selectedCity={selectedCity}
            delivery_date={delivery_date}
            pickup_date={pickup_date}
            openBikeRental={openBikeRental}
            isLoggedIn={isLoggedIn}
            setIsLoggedIn={setIsLoggedIn}
            user={user}
            setCityModalOpen={setCityModalOpen}
            setIsProfileOpen={setIsProfileOpen}
            specialRoute={specialRoute}
            category={cat}
          />
          <HeaderMobile
            selectedCity={selectedCity}
            delivery_date={delivery_date}
            pickup_date={pickup_date}
            openBikeRental={openBikeRental}
            isLoggedIn={isLoggedIn}
            setIsLoggedIn={setIsLoggedIn}
            user={user}
            setCityModalOpen={setCityModalOpen}
            specialRoute={specialRoute}
            setIsProfileOpen={setIsProfileOpen}
            category={cat}
          />

          {/* onboarding is currenty in primary header when needed we can move this to layout itself */}
          {/* onboarding is currenty in primary header when needed we can move this to layout itself */}
          <Suspense>
            <OnboardingFlow />
          </Suspense>
        </motion.header>
      </AnimatePresence>

      {/* Bike Rental Selector */}
      <BikeRentalSelector />

      <Button
        onClick={() => setIsWhatsappSupportModalOpen(true)}
        className='fixed bottom-20 right-3 z-50 h-12 w-12 rounded-full bg-secondary-600 p-2 hover:bg-secondary-500 md:bottom-8 md:right-5 md:min-h-16 md:min-w-16'
      >
        <IconWhatsapp className='scale-125 md:scale-150' />
      </Button>
    </>
  )
}

export default Header
