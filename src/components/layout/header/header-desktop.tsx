"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import Link from "next/link"
import React from "react"

// import CartItems from "@/components/modals/cart"
import { Typography } from "@/components/ui/typography"
import { cn } from "@/lib/utils"
import { User } from "@/types/user"
import { getHeaderBorderColor } from "@/utils/get-bg-color"
import { usePathname } from "next/navigation"
import {
  ChevronDownIcon,
  LocationPointerOutlinedIcon,
  UserOutlinedIcon,
} from "sharepal-icons"

import { BikeRentalInputs } from "@/components/bike-rental/bike-rental-inputs"
import { SharePalLogo } from "@/components/common/logo"
import { City } from "@/types/common"
import { Skeleton } from "../../ui/skeleton"

interface HeaderDesktopProps {
  selectedCity: City
  delivery_date: Date | null

  pickup_date: Date | null
  isLoggedIn: boolean
  setIsLoggedIn: (isTrue: boolean) => void

  openBikeRental: () => void
  setCityModalOpen: React.Dispatch<React.SetStateAction<boolean>>
  setIsProfileOpen: React.Dispatch<React.SetStateAction<boolean>>

  user: User | null
  specialRoute?: boolean

  category?: string
}

export const HeaderDesktop: React.FC<HeaderDesktopProps> = ({
  selectedCity,
  isLoggedIn,
  setCityModalOpen,
  user,
  openBikeRental,
  specialRoute,
  setIsProfileOpen,
}) => {
  const isScrolled = false // Now
  const pathname = usePathname()

  return (
    <div className='w-full'>
      <div className={cn("desktop hidden w-full bg-gray-50 lg:block")}>
        {/* first row */}
        <div className='container w-full items-center justify-between gap-1 pb-3 transition-all lg:flex'>
          {/* left */}
          <div className='flex items-end justify-start gap-28'>
            <Link
              href={
                selectedCity ? `/${selectedCity?.city_url ?? "bangalore"}` : "/"
              }
              className='left h-full flex-[1]'
              prefetch={true}
            >
              <div className='logo flex h-[68px] w-40 flex-col items-center justify-end gap-1 rounded-bl-2xl rounded-br-2xl bg-primary-500 p-3 pt-[18px] shadow-sm'>
                <div className='flex items-center justify-center'>
                  <SharePalLogo />
                </div>
              </div>
            </Link>
          </div>

          {/* don't remove the bottom comment out the below span as it solves tailwind color loading issue */}

          {/* <span className='border-category-orange border-carepal-dark  border-category-green border-category-purple border-category-red'></span> */}
          {/* {'DONT REMOVE THE ABOVE SPAN'} */}

          {/* middle part */}

          <div className='flex flex-row items-center justify-center gap-3'>
            <Typography
              as='h1'
              className='font-ubuntu text-h5 font-semibold text-primary-900'
            >
              Hourly Bike Rentals in
            </Typography>
            <button
              type='button'
              onClick={(e) => {
                e.preventDefault()
                setCityModalOpen(true)
              }}
              className='flex items-center justify-center gap-2 rounded-full border-2 border-neutral-200 bg-gray-100 px-4 py-2 text-neutral-700 transition-colors hover:bg-gray-200'
            >
              <LocationPointerOutlinedIcon className='h-5 w-5' />
              {selectedCity?.city_name ? (
                <Typography as='span' className='text-sh4 font-medium'>
                  {selectedCity.city_name}
                </Typography>
              ) : (
                <Skeleton className='h-5 w-20 bg-gray-200' />
              )}
              <ChevronDownIcon className='h-4 w-4' />
            </button>
          </div>

          {/* right  */}
          <div
            className={cn(
              "right flex items-end justify-end gap-3 transition-colors duration-300",
              specialRoute && !isScrolled
                ? "fill-gray-100 text-gray-100"
                : "fill-gray-900 text-gray-900",
            )}
          >
            {/* <Button
          onClick={() => {
            setIsSearchOpen(true)
          }}
          variant='ghost'
          size={"icon"}
          className={cn(
            "search relative h-11 w-11 p-2",
            specialRoute && !isScrolled
              ? "text-gray-100 hover:bg-neutral-150 hover:text-neutral-900"
              : "text-neutral-900",
          )}
        >
          <SearchOutlinedIcon className='min-h-7 lg:min-w-7' />
        </Button> */}

            <div
              onClick={() => setIsProfileOpen(true)}
              className='profile flex cursor-pointer items-center justify-end gap-3'
            >
              <Button
                className={cn(
                  "flex h-11 w-11 cursor-pointer items-center justify-center rounded-full border-2 border-solid border-neutral-200 p-0.5 transition-colors duration-300",
                  specialRoute && !isScrolled
                    ? "bg-gray-100 text-neutral-900 hover:bg-gray-200"
                    : "bg-neutral-900 text-gray-100 hover:bg-neutral-950",
                  !isScrolled && getHeaderBorderColor(pathname),
                )}
              >
                <UserOutlinedIcon className='min-h-6 min-w-6 fill-inherit' />
              </Button>

              {isLoggedIn ? (
                <span className='!text-bt2 font-medium'>
                  Hi, {user?.first_name}
                </span>
              ) : (
                <span className='!text-bt2 font-medium'>Hi, Login</span>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* second row */}

      <div className='hidden w-full rounded-b-3xl border-t border-neutral-200 bg-gradient-to-b from-gray-50 to-bike-200 py-2 pb-4 lg:block'>
        <div className='container'>
          <BikeRentalInputs isDesktop={true} />
        </div>
      </div>
    </div>
  )
}
