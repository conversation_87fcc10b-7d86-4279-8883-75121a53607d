"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Typography } from "@/components/ui/typography"
import { cn } from "@/lib/utils"
import { AnimatePresence, motion } from "framer-motion"
import { X } from "lucide-react"

import React, { useEffect, useState } from "react"
// Shared dialog content for both mobile and desktop
interface DialogContentProps {
  title: string
  onClose: () => void
  children: React.ReactNode
  onContinue?: () => void
  continueDisabled?: boolean
  continueText?: string
  footer?: React.ReactNode
}

const DialogContent = React.memo<DialogContentProps>(
  ({
    title,
    onClose,
    children,
    onContinue,
    continueDisabled,
    continueText,
    footer,
  }) => (
    <div
      className={cn(
        "flex max-h-[90vh] w-full flex-col overflow-hidden rounded-t-3xl bg-white shadow-xl md:max-w-3xl md:rounded-3xl",
      )}
      onClick={(e: React.MouseEvent) => e.stopPropagation()}
    >
      {/* Header */}
      <div className='relative flex items-center justify-between gap-4 border-b border-bike-200 px-6 py-5'>
        <div className='absolute left-1/2 top-3 h-1 w-12 -translate-x-1/2 rounded-full bg-gray-300 md:hidden' />
        <Typography
          as='h2'
          className={cn(
            "font-semibold",
            "text-center text-xl text-bike-800 md:text-left md:text-2xl",
          )}
        >
          {title}
        </Typography>
        <Button variant='ghost' size='sm' onClick={onClose}>
          <X className='size-4 md:size-5' />
          <span className='sr-only'>Close</span>
        </Button>
      </div>
      {/* Content */}
      <div className='flex-1 overflow-y-auto p-6'>{children}</div>
      {/* Sticky Footer */}
      {(footer || onContinue) && (
        <div className='sticky bottom-0 z-10 border-t border-bike-200 bg-white px-6 py-5'>
          {footer ? (
            footer
          ) : (
            <Button
              onClick={onContinue}
              disabled={continueDisabled}
              className={cn(
                "w-full bg-bike-600 text-white hover:bg-bike-700 disabled:opacity-50",
                "py-3 md:h-12 md:px-8 md:py-2",
              )}
            >
              {continueText}
            </Button>
          )}
        </div>
      )}
    </div>
  ),
)
DialogContent.displayName = "DialogContent"

interface DialogToDrawerProps {
  open?: boolean
  onOpenChange?: (open: boolean) => void
  trigger?: React.ReactNode
  title?: string
  children: React.ReactNode
  onContinue?: () => void
  continueDisabled?: boolean
  continueText?: string
  className?: string
  footer?: React.ReactNode
}

const DialogToDrawer: React.FC<DialogToDrawerProps> = ({
  open,
  onOpenChange,
  trigger,
  title = "Select Your Rental Period",
  children,
  onContinue,
  continueDisabled = false,
  continueText = "Continue",
  className,
  footer,
}) => {
  const [isVisible, setIsVisible] = useState(false)

  useEffect(() => {
    if (open) {
      setIsVisible(true)
      document.body.style.overflow = "hidden"
    } else {
      document.body.style.overflow = "unset"
      const timer = setTimeout(() => setIsVisible(false), 200)
      return () => clearTimeout(timer)
    }
    return () => {
      document.body.style.overflow = "unset"
    }
  }, [open])

  const handleBackdropClick = React.useCallback(
    (e: React.MouseEvent) => {
      if (e.target === e.currentTarget) {
        onOpenChange?.(false)
      }
    },
    [onOpenChange],
  )

  const handleTriggerClick = React.useCallback(() => {
    onOpenChange?.(true)
  }, [onOpenChange])

  if (!isVisible && !open)
    return trigger ? <div onClick={handleTriggerClick}>{trigger}</div> : null

  // Simple fade/slide animation variants
  const backdropVariants = {
    hidden: { opacity: 0 },
    visible: { opacity: 1 },
  }
  const dialogVariants = {
    hidden: { y: 40, opacity: 0 },
    visible: { y: 0, opacity: 1, transition: { duration: 0.2 } },
  }

  return (
    <>
      {trigger && !open && <div onClick={handleTriggerClick}>{trigger}</div>}

      <AnimatePresence>
        {open && (
          <>
            {/* Backdrop */}
            <motion.div
              variants={backdropVariants}
              initial='hidden'
              animate='visible'
              exit='hidden'
              transition={{ duration: 0.2 }}
              className='fixed inset-0 z-50 bg-black/50'
              onClick={handleBackdropClick}
            />

            {/* Responsive Dialog (bottom sheet on mobile, centered on desktop) */}
            <motion.div
              variants={dialogVariants}
              initial='hidden'
              animate='visible'
              exit='hidden'
              className={cn(
                "fixed left-0 right-0 z-50 flex justify-center md:items-center",
                "bottom-0 md:inset-0 md:p-4",
                className,
              )}
              style={{ pointerEvents: "auto" }}
              onClick={handleBackdropClick}
            >
              <DialogContent
                title={title}
                onClose={() => onOpenChange?.(false)}
                onContinue={onContinue}
                continueDisabled={continueDisabled}
                continueText={continueText}
                footer={footer}
              >
                {children}
              </DialogContent>
            </motion.div>
          </>
        )}
      </AnimatePresence>
    </>
  )
}

export default DialogToDrawer
