import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { FieldPath, FieldValues, UseFormReturn } from "react-hook-form"

import { CountryCodeSelect } from "./country-code-select"
import { PhoneInputWrapper } from "./phone-input-wrapper"

interface PhoneInputProps<TFieldValues extends FieldValues> {
  form: UseFormReturn<TFieldValues>
  phoneFieldName: FieldPath<TFieldValues>
  countryCodeFieldName: FieldPath<TFieldValues>
  label: string
  placeholder: string
  required?: boolean
  readOnly?: boolean
}

export function PhoneInput<TFieldValues extends FieldValues>({
  form,
  phoneFieldName,
  countryCodeFieldName,
  label,
  placeholder,
  required = false,
  readOnly = false,
}: PhoneInputProps<TFieldValues>) {
  return (
    <FormField
      control={form.control}
      name={phoneFieldName}
      render={({ field }) => (
        <FormItem>
          <FormLabel className='!text-b4'>
            {label} {required && <span className='text-red-500'>*</span>}
          </FormLabel>
          <PhoneInputWrapper>
            <FormField
              control={form.control}
              name={countryCodeFieldName}
              render={({ field: countryCodeField }) => (
                <FormControl>
                  <CountryCodeSelect
                    value={countryCodeField.value}
                    onChange={countryCodeField.onChange}
                    disabled={true}
                  />
                </FormControl>
              )}
            />
            <div className='h-12 w-px bg-neutral-200' />
            <FormControl>
              <Input
                {...field}
                type='tel'
                pattern='\d*'
                placeholder={placeholder}
                className='h-12 !rounded-none border-0 focus-visible:border-0'
                readOnly={readOnly}
              />
            </FormControl>
          </PhoneInputWrapper>
          <FormMessage />
        </FormItem>
      )}
    />
  )
}
