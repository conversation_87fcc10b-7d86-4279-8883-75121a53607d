import { cn } from "@/lib/utils"
import * as React from "react"

interface PhoneInputWrapperProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode
}

export function PhoneInputWrapper({
  className,
  children,
  ...props
}: PhoneInputWrapperProps) {
  return (
    <div
      className={cn(
        "flex w-full overflow-hidden rounded-xl border-2 border-neutral-200 bg-gray-100 focus-within:border-primary-500 focus-within:ring-0 focus-within:ring-primary",
        className,
      )}
      {...props}
    >
      {children}
    </div>
  )
}
