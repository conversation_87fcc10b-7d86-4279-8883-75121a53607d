// BikeOrderSummary Component
"use client"

import { Separator } from "@/components/ui/separator"
import { motion } from "framer-motion"
import { Charges } from "./charge-item"
// import { ClaimGSTInput } from "./claim-gst-input"
import { CouponInput } from "./coupon-input"
import { PaymentSection } from "./payment-selection"
import { WalletCheckbox } from "./wallet-checkbox"

import { Typography } from "@/components/ui/typography"
import { moneyFormatter } from "@/functions/small-functions"
import useCoupon from "@/hooks/use-coupon"
import { useCheckoutStore } from "@/store/checkout-store"
import { RentalPeriodDisplay } from "../rental-period-display"
import { ExtraHelmetCheckbox } from "./extra-helmet-checkbox"
import PlaceOrderButton from "./place-order"
import type { BikeOrderSummaryProps } from "./type"

export function BikeOrderSummaryNew({}: BikeOrderSummaryProps) {
  const { final_amount, selected_bike, rental_params, payment_option } =
    useCheckoutStore()

  const couponProps = useCoupon()

  return (
    <motion.div
      initial={{ opacity: 0, x: 20 }}
      animate={{ opacity: 1, x: 0 }}
      className='space-y-4 rounded-radius bg-gray-100 p-6 font-inter'
    >
      <div className='flex flex-wrap items-center justify-between'>
        <Typography as={"h2"} className='mb-2 min-w-max text-h2'>
          Order Summary
        </Typography>
      </div>

      <Separator />

      {/* Rental Period and Location Details */}
      {selected_bike && rental_params && (
        <RentalPeriodDisplay showLocation={true} />
      )}

      <Separator />

      <div className='space-y-4'>
        {/* {wallet_balance > 0 && ( */}
        <WalletCheckbox />
        {/* )} */}

        <ExtraHelmetCheckbox />

        <CouponInput
          {...couponProps}
          // appliedCoupon={applied_coupon_code}
          // onApply={onApplyCoupon}
          // onRemove={onRemoveCoupon}
        />
      </div>

      <Separator />

      <div className='space-y-3'>
        <Charges />
      </div>

      <div className='flex items-center justify-between'>
        <div>
          <Typography as={"h6"} className='!text-h6'>
            Order Total
          </Typography>
          <Typography as={"span"} className='block !text-b6 text-gray-500'>
            Price incl. of all taxes
          </Typography>
        </div>

        <Typography as={"h1"} className='!text-h1'>
          {moneyFormatter(final_amount)}
        </Typography>
      </div>

      <Separator />

      <PaymentSection />

      <div className='space-y-4'>
        {/* <Button
          variant={"primary"}
          onClick={handlePlaceOrder}
          disabled={isProcessing}
          className="w-full"
          size={'lg'}
        >
          {isProcessing ? 'Processing...' : 'Place Your Order & Pay'}
        </Button> */}
        <PlaceOrderButton className='w-full !text-bt2' />

        <Typography as={"p"} className='text-center text-b4 text-gray-500'>
          {payment_option?.payment_type === 7
            ? `Advance of ${moneyFormatter(final_amount * 0.1)} is non-refundable if you cancel. If SharePal
          cancels, it will be refunded within 48 hrs.`
            : `By placing your order, you agree to SharePal&apos;s privacy notice and
          conditions of use.`}
        </Typography>
      </div>
    </motion.div>
  )
}
