// ExtraHelmetCheckbox Component

import { Checkbox } from "@/components/ui/checkbox"
import { Typography } from "@/components/ui/typography"
import { moneyFormatter } from "@/functions/small-functions"
import { cn } from "@/lib/utils"
import {
  DEFAULT_EXTRA_HELMET_FEE,
  useCheckoutStore,
} from "@/store/checkout-store"

export function ExtraHelmetCheckbox() {
  const { extra_helmet_selected, setExtraHelmetSelected } = useCheckoutStore()
  return (
    <div
      className={cn(
        "flex",
        "cursor-pointer items-center justify-between rounded-3xl bg-bike-200 px-5 py-2.5",
      )}
      onClick={() => setExtraHelmetSelected(!extra_helmet_selected)}
    >
      <div className={cn("flex items-center gap-2")}>
        <Checkbox
          id='extra_helmet'
          checked={extra_helmet_selected}
          // onCheckedChange={(checked) =>
          //   setExtraHelmetSelected(checked as boolean)
          // }
          className={cn(
            "!border-1 !h-4 !w-4 !rounded-[3px] !border-neutral-500",
            extra_helmet_selected && "!border-primary-500",
          )}
        />
        <label className='cursor-pointer'>
          <Typography as='span' className='!text-sh5 !text-neutral-900'>
            Add an extra helmet
          </Typography>
        </label>
      </div>
      <div className='flex justify-between'>
        <Typography as='span' className='!text-sh4 !text-gray-900'>
          @ {moneyFormatter(DEFAULT_EXTRA_HELMET_FEE)}
        </Typography>
      </div>
    </div>
  )
}
