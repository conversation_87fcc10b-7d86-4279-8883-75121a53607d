import { CarepalHeartIcon } from "@/components/icons/carepal-heart"
import { Badge } from "@/components/ui/badge"
import { Typography } from "@/components/ui/typography"
import { moneyFormatter } from "@/functions/small-functions"
import {
  DEFAULT_EXTRA_HELMET_FEE,
  useCheckoutStore,
} from "@/store/checkout-store"
import { AnimatePresence, motion } from "framer-motion"
import React, { useMemo } from "react"
import { OfferOutlinedIcon, ShieldPalPromiseOutlinedIcon } from "sharepal-icons"

export interface ChargeItem {
  label: string
  amount: number
  strikethrough?: number
  badge?: {
    text: string
    variant?: "primary" | "success" | "carepal"
    icon?: React.ReactNode
  }
  items_count?: number
  items_count_text?: string
  tooltip?: {
    content: React.ReactNode
    title?: string
  }
}

interface ChargeItemProps {
  item: ChargeItem
  type?: "discount" | "charge"
}

export const ChargeItem = React.memo(
  ({ item, type = "charge" }: ChargeItemProps) => {
    const LabelComponent = React.memo(() => (
      <motion.span
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        className='!text-b6 text-gray-900 underline decoration-dotted underline-offset-4 md:!text-b4'
      >
        {item.label}
      </motion.span>
    ))

    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: -20 }}
        transition={{ duration: 0.3 }}
        className='flex items-center justify-between'
      >
        <div className='flex flex-wrap items-center justify-start gap-0 text-right md:gap-2'>
          {item.tooltip ? (
            <div className='group relative flex items-center gap-1'>
              <LabelComponent />
              <div className='inline-flex items-center'>
                <div className='flex h-4 w-4 cursor-help items-center justify-center rounded-full bg-primary-100 transition-colors hover:bg-primary-200'>
                  <div className='h-2 w-2 rounded-full bg-primary-600'></div>
                </div>
              </div>
              {/* Tooltip */}
              <div className='absolute left-0 top-6 z-50 hidden w-80 rounded-lg border border-neutral-200 bg-gray-50 p-3 shadow-lg group-hover:block'>
                <div className='absolute -top-1 left-4 h-2 w-2 rotate-45 border-l border-t border-neutral-200 bg-gray-50'></div>
                {item.tooltip.title && (
                  <div className='mb-2 text-left text-bt3 text-neutral-800'>
                    {item.tooltip.title}
                  </div>
                )}
                {item.tooltip.content}
              </div>
            </div>
          ) : (
            <LabelComponent />
          )}
          {item.items_count !== undefined && item.items_count > 0 && (
            <motion.span
              initial={{ scale: 0.8 }}
              animate={{ scale: 1 }}
              className='block rounded-full bg-neutral-150 px-2 py-1 !text-o4 text-gray-500 md:!text-b6'
            >
              {item.items_count} {item.items_count_text || "items added"}
            </motion.span>
          )}
          {item.badge?.text && (
            <motion.div
              initial={{ opacity: 0, x: -10 }}
              animate={{ opacity: 1, x: 0 }}
            >
              <Badge
                variant={item.badge.variant}
                className='mx-1 px-1 py-[3px]'
              >
                {item.badge.icon ? (
                  item.badge.icon
                ) : (
                  <ShieldPalPromiseOutlinedIcon className='h-4 w-4' />
                )}
                <Typography as={"p"} className='!text-o4 md:!text-b6'>
                  {item.badge.text}
                </Typography>
              </Badge>
            </motion.div>
          )}
        </div>
        <div className='flex items-center justify-end gap-1'>
          {item.strikethrough !== undefined && item.strikethrough > 0 && (
            <motion.span
              initial={{ opacity: 0, x: 10 }}
              animate={{ opacity: 1, x: 0 }}
              className='!text-b5 text-gray-400 line-through md:!text-b3'
            >
              {moneyFormatter(item.strikethrough)}
            </motion.span>
          )}
          <motion.span
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className={`!text-sh5 md:!text-sh3 ${
              type === "discount" && "text-success-600"
            }`}
          >
            {item.amount < 0 || type === "discount" ? "-" : ""}

            {moneyFormatter(Math.abs(item.amount))}
          </motion.span>
        </div>
      </motion.div>
    )
  },
)

interface ChargesProps {
  total_rent: number
  gst_amount: number
  total_hours?: number
  wallet_balance_used?: number
  applied_coupon_code?: string
  coupon_discount?: number
  wallet_used?: boolean
  carepal_selected?: boolean
  carepal_fee?: number
  extra_helmet_selected?: boolean
}

export function Charges({ chargesData }: { chargesData?: ChargesProps }) {
  const {
    total_rent,
    gst_amount,
    wallet_balance_used,
    applied_coupon_code,
    coupon_discount,
    wallet_used,
    total_hours,
    carepal_selected,
    carepal_fee,
    extra_helmet_selected,
  } = useCheckoutStore()
  console.log("total rent", total_rent)

  const charges: ChargeItem[] = useMemo(() => {
    const baseCharges: ChargeItem[] = [
      {
        label: "Bike Rental Charges",
        amount: chargesData ? chargesData.total_rent : total_rent,
        items_count: chargesData?.total_hours || total_hours || 1,
        items_count_text: "hours",
      },
    ]

    // Add GST charges for bike rental
    if (gst_amount > 0) {
      baseCharges.push({
        label: "GST (28%)",
        amount: gst_amount,
        tooltip: {
          title: "GST Breakdown",
          content: (
            <div className='space-y-2 text-xs'>
              <div className='space-y-1'>
                <div className='flex justify-between'>
                  <span className='text-neutral-600'>Rental Amount:</span>
                  <span className='font-medium'>
                    {moneyFormatter(total_rent)}
                  </span>
                </div>
                <div className='flex justify-between'>
                  <span className='text-neutral-600'>GST Rate:</span>
                  <span className='font-medium'>28%</span>
                </div>
                <div className='flex justify-between border-t border-neutral-200 pt-1'>
                  <span className='text-neutral-600'>GST Amount:</span>
                  <span className='font-semibold text-primary-700'>
                    {moneyFormatter(gst_amount)}
                  </span>
                </div>
              </div>
              <div className='mt-3 text-center text-o4 text-neutral-500'>
                GST is calculated on the rental amount only
              </div>
            </div>
          ),
        },
      })
    }

    if (!chargesData) {
      // Remove delivery charges and carepal charges for bike rental
      // Only keep essential charges like coupons, wallet, and handling charges
      if (coupon_discount !== undefined && applied_coupon_code) {
        baseCharges.push({
          label: "Coupon Discount",
          amount: -coupon_discount,
          badge: {
            icon: (
              <OfferOutlinedIcon className='mr-1 !h-4 !w-4 text-success-600' />
            ),

            text: applied_coupon_code,
            variant: "success",
          },
        })
      }

      if (carepal_selected && carepal_fee > 0) {
        baseCharges.push({
          label: "Damage Waiver Fee",
          amount: carepal_fee,
          badge: {
            text: "Carepal Assure",
            variant: "carepal",
            icon: (
              <CarepalHeartIcon className='mr-1 !h-4 !w-4 fill-carepal-darker' />
            ),
          },
        })
      }

      if (extra_helmet_selected) {
        baseCharges.push({
          label: "Extra Helmet",
          amount: DEFAULT_EXTRA_HELMET_FEE,
        })
      }

      if (wallet_balance_used !== undefined && wallet_used) {
        baseCharges.push({
          label: "Wallet Used",
          amount: -wallet_balance_used,
        })
      }
    }
    // handle custom charge data
    else {
      // Remove delivery charges and carepal charges for bike rental
      // Only keep essential charges like coupons, wallet, and handling charges
      if (chargesData !== undefined && chargesData.applied_coupon_code) {
        baseCharges.push({
          label: "Coupon Discount",
          amount: -(chargesData?.coupon_discount || 0),
          badge: {
            icon: (
              <OfferOutlinedIcon className='mr-1 !h-4 !w-4 text-success-600' />
            ),

            text: chargesData?.applied_coupon_code || "",
            variant: "success",
          },
        })
      }

      if (chargesData !== undefined && chargesData.gst_amount) {
        baseCharges.push({
          label: "GST (28%)",
          amount: chargesData.gst_amount,
          tooltip: {
            title: "GST Breakdown",
            content: (
              <div className='space-y-2 text-xs'>
                <div className='space-y-1'>
                  <div className='flex justify-between'>
                    <span className='text-neutral-600'>Rental Amount:</span>
                    <span className='font-medium'>
                      {moneyFormatter(chargesData?.total_rent)}
                    </span>
                  </div>
                  <div className='flex justify-between'>
                    <span className='text-neutral-600'>GST Rate:</span>
                    <span className='font-medium'>28%</span>
                  </div>
                  <div className='flex justify-between border-t border-neutral-200 pt-1'>
                    <span className='text-neutral-600'>GST Amount:</span>
                    <span className='font-semibold text-primary-700'>
                      {moneyFormatter(chargesData?.gst_amount)}
                    </span>
                  </div>
                </div>
                <div className='mt-3 text-center text-o4 text-neutral-500'>
                  GST is calculated on the rental amount only
                </div>
              </div>
            ),
          },
        })
      }

      if (chargesData !== undefined && chargesData.wallet_used) {
        baseCharges.push({
          label: "Wallet Used",
          amount: -(chargesData.wallet_balance_used || 0),
        })
      }

      if (chargesData !== undefined && chargesData.carepal_selected) {
        baseCharges.push({
          label: "Damage Waiver Fee",
          amount: chargesData.carepal_fee || 0,
          badge: {
            text: "Carepal Assure",
            variant: "carepal",
            icon: (
              <CarepalHeartIcon className='mr-1 !h-4 !w-4 fill-carepal-darker' />
            ),
          },
        })
      }

      if (chargesData !== undefined && chargesData.extra_helmet_selected) {
        baseCharges.push({
          label: "Extra Helmet",
          amount: DEFAULT_EXTRA_HELMET_FEE,
        })
      }
    }

    return baseCharges
  }, [
    chargesData,
    total_rent,
    gst_amount,
    wallet_balance_used,
    applied_coupon_code,
    coupon_discount,
    wallet_used,
    total_hours,
    carepal_selected,
    carepal_fee,
    extra_helmet_selected,
  ])

  return (
    <div className='w-full space-y-3'>
      <AnimatePresence>
        {charges.map((item) => (
          <motion.div
            key={item.label}
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: "auto" }}
            exit={{ opacity: 0, height: 0 }}
            transition={{ duration: 0.3 }}
          >
            <ChargeItem item={item} />
          </motion.div>
        ))}
      </AnimatePresence>
    </div>
  )
}
