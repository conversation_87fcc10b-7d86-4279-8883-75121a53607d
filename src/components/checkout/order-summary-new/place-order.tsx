"use client"

import { But<PERSON> } from "@/components/ui/button"
import { moneyFormatter } from "@/functions/small-functions"
import usePlaceOrder from "@/hooks/use-place-order"
import { cn } from "@/lib/utils"
import { useCheckoutStore } from "@/store/checkout-store"

export default function PlaceOrderButton({
  className,
}: {
  className?: string
}) {
  const { handlePayment, isLoading } = usePlaceOrder()
  const { selected_bike, payment_option, final_amount } = useCheckoutStore()
  return (
    <Button
      className={cn(
        "h-full w-max rounded-full max-md:w-full md:h-11 lg:min-w-72",
        className,
      )}
      type='button'
      variant='primary'
      size='lg'
      onClick={handlePayment}
      disabled={isLoading || !selected_bike}
    >
      {isLoading
        ? "Processing..."
        : payment_option?.payment_type === 6
          ? "Place Your Order"
          : payment_option?.payment_type === 7
            ? `Pay ${moneyFormatter(final_amount * 0.1)} to Book`
            : "Place Your Order & Pay"}
    </Button>
  )
}
