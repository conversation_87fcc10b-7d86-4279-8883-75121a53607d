"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { RadioGroup } from "@/components/ui/radio-group"
import { Separator } from "@/components/ui/separator"
import { Typography } from "@/components/ui/typography"
import { moneyFormatter } from "@/functions/small-functions"
import { useToggle } from "@/hooks/use-toggle"
import { fetchFaqs } from "@/services/common"
import { useCheckoutStore } from "@/store/checkout-store"
import { useQuery } from "@tanstack/react-query"
import { motion } from "framer-motion"
import { AlertTriangleIcon, InfoIcon } from "lucide-react"
import { Charges } from "../order-summary-new/charge-item"
import { SectionCard } from "../section-card"
import CoverageSection from "./carepal-coverage"
import CarepalInformation from "./carepal-information"
import { CarepalOption } from "./carepal-option"

export function CarepalForm() {
  const {
    total_goods_value,
    setCarepalSelected,
    carepal_selected,
    carepal_fee,
    carepal_coverage,
    goToNextSection,
    final_amount,
    setStepCompleted,
    contact_details,
  } = useCheckoutStore()

  const { value: isInfoOpen, onOpen, onClose } = useToggle()

  const handleCarepalChange = (value: string) => {
    setCarepalSelected(value === "yes")
    onClose()
  }

  const handleNext = () => {
    setStepCompleted("carepal", true)
    goToNextSection()
  }

  // fetch carepal faqs
  const { data: faqs } = useQuery({
    queryKey: ["carepa-faqs"],
    refetchOnWindowFocus: false,

    queryFn: async () => await fetchFaqs("carepal", "", "carepal"),
  })
  // const handlePrevious = () => {
  //   goToPrevSection()
  // }

  return (
    <>
      <motion.div initial='hidden' animate='visible' exit='exit'>
        <SectionCard className='space-y-6'>
          {/* Header Section */}
          <div className='flex items-center justify-between gap-3 md:gap-6'>
            <div className='flex flex-col items-start justify-start'>
              <Typography
                as='p'
                className='hidden text-bt2 font-semibold text-neutral-900 md:block md:text-sh2'
              >
                Value of the good you&apos;re renting
              </Typography>

              <div className='flex w-full items-center justify-center gap-2 text-center md:hidden'>
                <Typography as='p' className='text-sh5'>
                  Total value of goods in cart is
                </Typography>
                <Typography
                  as='p'
                  className='text-bt2 font-bold text-neutral-900 md:text-sh2'
                >
                  {moneyFormatter(total_goods_value)}
                </Typography>
              </div>
              <Typography
                as='p'
                className='mt-1 text-center text-b7 text-gray-800 md:text-left md:text-b6'
              >
                Worried about accidental damage? With CarePal, you can rent with
                complete peace of mind.
              </Typography>
            </div>
            <div className='hidden text-right md:block'>
              <Typography
                as='p'
                className='text-bt2 font-bold text-neutral-900 md:text-sh2'
              >
                {moneyFormatter(total_goods_value)}
              </Typography>
              <Typography as='p' className='text-b6 text-gray-800'>
                Total Value
              </Typography>
            </div>
          </div>

          <div className='w-full space-y-2'>
            {/* Coverage Information */}
            <CoverageSection onOpen={onOpen} />

            {/* Info Section */}
            <div className='flex w-full flex-wrap items-center justify-between gap-0 md:gap-2'>
              <div className='flex items-center justify-center gap-1 text-neutral-400 md:gap-2'>
                <InfoIcon className='size-3 md:size-4' />

                <Typography as='p' className='text-b6'>
                  The waiver premium is non-refundable.
                </Typography>
              </div>
              <Button
                size={"sm"}
                onClick={onOpen}
                variant='link'
                className='px-0'
              >
                Learn More
              </Button>
            </div>
          </div>

          {/* Question Section */}
          <div>
            <Typography
              as='h3'
              className='mb-4 text-sh4 font-semibold text-neutral-900 md:text-bt2'
            >
              Would you like to cover upto {moneyFormatter(carepal_coverage)} of
              accidental damage costs?
            </Typography>

            <RadioGroup
              value={carepal_selected ? "yes" : "no"}
              onValueChange={handleCarepalChange}
              className='grid-cols-1'
            >
              <CarepalOption
                id='carepal-yes'
                value='yes'
                label='Yes, cover accidental damages.'
                price={moneyFormatter(carepal_fee)}
                badge={"Recommended"}
              />
              <CarepalOption
                id='carepal-no'
                value='no'
                label='No, I will take the risk.'
                price='₹0'
                icon={<AlertTriangleIcon className='h-4 w-4 text-amber-500' />}
              />
            </RadioGroup>
          </div>

          <div className='flex w-full flex-col items-center justify-center gap-3 md:hidden'>
            <Separator />

            <div className='w-full border-b border-dashed border-neutral-200 pb-4'>
              <Charges />
            </div>

            <div className='flex w-full items-center justify-between'>
              <div>
                <Typography as={"h4"} className='!text-sh4'>
                  Order Total
                </Typography>
                <Typography as={"span"} className='block text-o4 text-gray-500'>
                  Price incl. of all taxes
                </Typography>
              </div>
              <Typography as={"h1"} className='!text-h4'>
                {moneyFormatter(final_amount)}
              </Typography>
            </div>
            <Separator />

            {/* contact details */}
            <div className='w-full gap-3'>
              <Typography
                as='h3'
                className='mb-2 mt-4 text-sh4 font-semibold text-neutral-900 md:text-bt2'
              >
                Contact Details
              </Typography>
              <div className='text-b6 text-neutral-500'>
                <Typography as={"p"} className='text-b6'>
                  {contact_details?.first_name} {contact_details?.last_name}
                </Typography>
                <Typography as={"p"} className='text-b6'>
                  {contact_details?.calling_number} |{" "}
                  {contact_details?.whatsapp_number} (WhatsApp)
                </Typography>
                <Typography as={"p"} className='text-b6'>
                  {contact_details?.email}
                </Typography>
              </div>
            </div>
          </div>

          {/* Navigation Buttons */}
          <div className='flex justify-between pt-4'>
            {/* <Button
            variant='outline'
            onClick={handlePrevious}
            className='border-neutral-300 px-6 hover:bg-neutral-50'
          >
            Previous
          </Button> */}
            <div className='fixed bottom-0 left-0 right-0 z-50 flex flex-wrap justify-end gap-4 bg-gray-100 max-md:p-4 md:static md:flex md:justify-end md:gap-4 md:space-y-4 md:bg-transparent md:px-0 md:pt-0'>
              <Button
                variant={"primary"}
                className='w-full md:min-w-44'
                size={"lg"}
                onClick={handleNext}
              >
                {carepal_selected ? "Continue" : "Skip & Continue"}
              </Button>
            </div>
          </div>
        </SectionCard>
      </motion.div>

      <CarepalInformation
        isOpen={isInfoOpen}
        onOpenChange={onClose}
        faqs={faqs ?? []}
        showActionButtons={true}
        // checkoutButtons={true}
      >
        <div className='flex w-full flex-col items-center justify-center gap-2 md:flex-row'>
          <Button
            size={"lg"}
            className='w-full flex-1 rounded-full'
            variant={"primary"}
            onClick={() => handleCarepalChange("yes")}
          >
            Get CarePal at {moneyFormatter(carepal_fee)}
          </Button>
          <Button
            size={"lg"}
            className='w-full flex-1 rounded-full'
            variant={"outline-primary"}
            onClick={() => handleCarepalChange("no")}
          >
            Take the Risk
          </Button>
        </div>
      </CarepalInformation>
    </>
  )
}
