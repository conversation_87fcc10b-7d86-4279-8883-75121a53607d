"use client"

import { HARD_CODED_IMAGE_URL } from "@/constants"
import { moneyFormatter } from "@/functions/small-functions"
import useCoupon from "@/hooks/use-coupon"
import { fetchPaymentOptions } from "@/services/checkout"
import SpImage from "@/shared/SpImage/sp-image"
import { useCheckoutStore } from "@/store/checkout-store"
import { PaymentOption } from "@/types/checkout"
import { useQuery } from "@tanstack/react-query"
import { motion } from "framer-motion"
import { Separator } from "../ui/separator"
import { Typography } from "../ui/typography"
import { BikeInfoCard } from "./bike-info-card"
import { Charges } from "./order-summary-new/charge-item"
import { CouponInput } from "./order-summary-new/coupon-input"
import { ExtraHelmetCheckbox } from "./order-summary-new/extra-helmet-checkbox"
import { PaymentSection } from "./order-summary-new/payment-selection"
import PlaceOrderButton from "./order-summary-new/place-order"
import { WalletCheckbox } from "./order-summary-new/wallet-checkbox"
import { PaymentOptionsSelection } from "./payment-options"
import { RentalPeriodDisplay } from "./rental-period-display"
import { SectionCard } from "./section-card"

export function ReviewAndPay() {
  const {
    payment_option,
    final_amount,
    selected_bike,
    rental_params,
    setPaymentOption,
    setPaymentType,
  } = useCheckoutStore()

  // Fetch payment options
  const { data: paymentOptions } = useQuery({
    queryKey: ["payment_options"],
    queryFn: async () => {
      const data = await fetchPaymentOptions(
        sessionStorage.getItem("backend_order") ?? "",
      )
      return data
    },
  })

  const onPaymentOptionChange = (type: string) => {
    const selectedPayment = paymentOptions?.find(
      (payment: PaymentOption) => payment.payment_type?.toString() === type,
    )

    if (selectedPayment) {
      // Set payment option to the store
      setPaymentOption(selectedPayment)
      // Optionally set the payment type
      setPaymentType(selectedPayment.payment_type)
    }
  }

  const CouponProps = useCoupon()

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      transition={{ duration: 0.3 }}
      className='space-y-6'
    >
      <SectionCard>
        {/* Mobile Rental Period Display */}
        {/* <div className='md:hidden'>
          {bike_rental && <RentalPeriodCompact bikeRental={bike_rental} />}
        </div> */}

        {/* Simple Bike Info Card */}

        {/* Rental Period and Location Details */}
        {selected_bike && rental_params && (
          <div className='mb-4 lg:hidden'>
            <RentalPeriodDisplay showLocation={true} />
          </div>
        )}

        {selected_bike && (
          <div className='mb-3 lg:mb-6'>
            <BikeInfoCard className='bg-gray-50' />
          </div>
        )}

        <div className='flex w-full flex-col items-center justify-center gap-3 md:hidden'>
          <Separator />

          <div className='w-full space-y-4'>
            <WalletCheckbox />
            <ExtraHelmetCheckbox />
            {/* <ClaimGSTInput /> */}

            <CouponInput {...CouponProps} />
          </div>

          <Separator />

          <div className='mb-3 w-full'>
            <Charges />
          </div>
        </div>

        <div className='mt-4 space-y-4 md:mt-10'>
          {/* <h3 className='text-bt2'>How would you like to pay?</h3> */}
          <div className='space-y-6'>
            {paymentOptions?.length > 1 && (
              <PaymentOptionsSelection
                paymentOptions={paymentOptions}
                selectedPaymentOption={
                  payment_option?.payment_type?.toString() || "1"
                }
                onPaymentOptionChange={onPaymentOptionChange}
              />
            )}

            <div className='flex items-center justify-between'>
              <div>
                <Typography as={"h4"} className='!text-h4'>
                  Order Total
                </Typography>
                <Typography as={"span"} className='block text-b6 text-gray-500'>
                  Price incl. of all taxes
                </Typography>
              </div>
              <Typography as={"h1"} className='!text-h1'>
                {moneyFormatter(final_amount)}
              </Typography>
            </div>

            <div className='block md:hidden'>
              <PaymentSection />
            </div>

            <div className='flex items-center justify-start gap-4'>
              <div className='fixed bottom-0 left-0 right-0 z-50 flex flex-wrap justify-end gap-4 bg-gray-100 max-md:p-4 md:static md:flex md:justify-start md:gap-4 md:space-y-4 md:bg-transparent md:px-0 md:pt-0'>
                <PlaceOrderButton />

                <Typography
                  as={"p"}
                  className='block text-center text-b6 text-gray-500 md:hidden'
                >
                  {payment_option?.payment_type === 7
                    ? `Advance of ${moneyFormatter(final_amount * 0.1)} is non-refundable if you cancel. If SharePal
          cancels, it will be refunded within 48 hrs.`
                    : `By placing your order, you agree to SharePal&apos;s privacy notice and
          conditions of use.`}
                </Typography>
              </div>

              <div className='hidden w-full items-center justify-start gap-[5px] rounded-lg bg-neutral-150 px-2 py-[6px] md:flex md:w-auto'>
                <SpImage
                  src={`${HARD_CODED_IMAGE_URL}/razorpay.webp`}
                  alt='Razorpay'
                  width={20}
                  height={20}
                  className='rounded'
                />
                <div className='flex flex-col items-start justify-start'>
                  <Typography
                    as={"span"}
                    className='!text-sh5 !text-neutral-900'
                  >
                    Pay via Razorpay
                  </Typography>
                  <Typography as={"span"} className='text-b7 text-neutral-500'>
                    Cards, Netbanking, Wallet & UPI
                  </Typography>
                </div>
              </div>
            </div>
          </div>
        </div>
      </SectionCard>
    </motion.div>
  )
}
