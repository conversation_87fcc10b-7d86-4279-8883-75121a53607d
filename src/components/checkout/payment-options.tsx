"use client"

import { RadioGroup } from "@/components/ui/radio-group"
import { PaymentOption } from "@/types/checkout"
import { PaymentCard } from "../common/payment-radio-box"

// Payment method icons
const RazorpayIcon = () => (
  <svg
    width='32'
    height='24'
    viewBox='0 0 32 24'
    fill='none'
    xmlns='http://www.w3.org/2000/svg'
  >
    <rect width='32' height='24' rx='4' fill='#072654' />
    <path
      d='M19.0537 6.49742H12.9282V17.5026H19.0537V6.49742Z'
      fill='#3395FF'
    />
  </svg>
)

const CashIcon = () => (
  <svg
    width='32'
    height='24'
    viewBox='0 0 32 24'
    fill='none'
    xmlns='http://www.w3.org/2000/svg'
  >
    <rect width='32' height='24' rx='4' fill='#047857' />
    <path
      d='M16 7V17M13 10H19M13 14H19'
      stroke='white'
      strokeWidth='2'
      strokeLinecap='round'
    />
  </svg>
)

interface PaymentOptionsSelectionProps {
  selectedPaymentOption: string
  paymentOptions: PaymentOption[]
  onPaymentOptionChange: (type: string) => void
}

export function PaymentOptionsSelection({
  selectedPaymentOption,
  paymentOptions,
  onPaymentOptionChange,
}: PaymentOptionsSelectionProps) {
  // const { getCartCount } = useCheckoutStore()
  return (
    <RadioGroup
      value={selectedPaymentOption}
      onValueChange={onPaymentOptionChange}
      className='space-y-3'
      // disabled={getCartCount() === 0}
    >
      {paymentOptions?.map((payment) => (
        <PaymentCard
          key={payment.id}
          value={payment.payment_type?.toString()}
          id={`payment-${payment.id}`}
          icon={payment.payment_type === 6 ? <CashIcon /> : <RazorpayIcon />}
          label={payment.visible_text}
          description={payment.visible_text}
        />
      ))}
    </RadioGroup>
  )
}
