"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Separator } from "@/components/ui/separator"
import usePlaceOrder from "@/hooks/use-place-order"
import { fetchOrderWithoutCart } from "@/services/orders"
import { getUserVerification } from "@/services/user"
import { useCheckoutStore } from "@/store/checkout-store"
import { CreateBikeOrder } from "@/types/order"
import { fetchWithAuthPost } from "@/utils/fetchWithAuth"
import { UseMutateFunction, useMutation, useQuery } from "@tanstack/react-query"
import { motion } from "framer-motion"
import { Loader2 } from "lucide-react"
import Image from "next/image"
import Link from "next/link"
import { useParams, useRouter, useSearchParams } from "next/navigation"
import React, { useEffect, useState } from "react"
import CircularTimer from "../custom/circular-timer"
import { IconCancelled, IconSuccess } from "../icons/checkout"
import CheckoutRedirectLoading from "../loadings/checkout-redirect-loading"
import { Skeleton } from "../ui/skeleton"
import { Typography } from "../ui/typography"

export type OrderStatus =
  | "payment_failed"
  | "payment_confirmed"
  | "process_failed"
  | "process_confirmed"

export default function OrderConfirmation() {
  const { order_id } = useParams<{ order_id: string }>()
  const router = useRouter()

  const searchParams = useSearchParams()

  const [autoVerification, setAutoVerification] = useState<
    "pending" | "verified" | "failed" | "redirecting"
  >("pending")

  const { setCheckoutRedirecting } = useCheckoutStore()

  const {
    data: orderData,
    refetch,
    isPending: isOrderFetching,
    isFetching: isOrderRefetching,
    isLoading: isOrderLoading,
  } = useQuery({
    queryKey: ["orderData", order_id],
    queryFn: () => fetchOrderWithoutCart(order_id),
    refetchOnWindowFocus: false,
  })

  const { data: userVerification, refetch: refetchVerification } = useQuery({
    queryKey: ["user_verification"],
    queryFn: getUserVerification,
    refetchOnWindowFocus: false,
    retry(failureCount) {
      return failureCount < 5
    },
  })

  useEffect(() => {
    setCheckoutRedirecting(false)
  }, [setCheckoutRedirecting])

  const { processOrderAndNotify, handleRazorpayPayment } = usePlaceOrder()

  const { mutate: placeOrderForCOD, isPending: isPlaceOrderCODPending } =
    useMutation({
      mutationFn: () =>
        processOrderAndNotify({
          bike_order: orderData ?? ({} as CreateBikeOrder),
          rzp_order_id: "",
        }),
      onSuccess: () => {
        refetch()
      },
    })

  const {
    mutate: handleRetryPaymentProcess,
    isPending: isHandleRetryPaymentProcess,
  } = useMutation({
    mutationFn: async () => {
      const response = await fetchWithAuthPost<string>(
        "https://api.sharepal.in/api:AIoqxnqr/payments/create-link",
        {
          order_id: orderData?.order_id,
        },
      )
      return handleRazorpayPayment({
        bike_order: orderData ?? ({} as CreateBikeOrder),
        rzp_order_id: response,
      })
    },
    onSuccess: () => {
      refetch()
    },
  })

  useEffect(() => {
    let intervalId: NodeJS.Timeout

    const checkVerificationStatus = () => {
      if (!userVerification?.last_order_processed) {
        // If order is not processed, keep polling every 20 seconds
        intervalId = setInterval(() => {
          refetchVerification()
        }, 20000)
      } else {
        // Clear interval once order is processed
        if (intervalId) {
          clearInterval(intervalId)
        }

        // Set verification status based on seon_result
        if (userVerification.last_seon_result === "APPROVE") {
          setAutoVerification("verified")
        } else if (userVerification.last_seon_result === "DECLINE") {
          setAutoVerification("failed")
        } else {
          setAutoVerification("redirecting")
        }
      }
    }

    checkVerificationStatus()

    return () => {
      if (intervalId) {
        clearInterval(intervalId)
      }
    }
  }, [userVerification, refetchVerification])

  useEffect(() => {
    if (autoVerification === "redirecting") {
      setTimeout(() => {
        router.replace("/complete-verification")
      }, 5000)
    }
  }, [autoVerification, router])

  if (isPlaceOrderCODPending || isHandleRetryPaymentProcess) {
    return <CheckoutRedirectLoading />
  }

  if (isOrderFetching || isOrderRefetching || isOrderLoading) {
    return <Skeleton className='h-96 w-full rounded-3xl' />
  }

  // const isProcessed = orderData.processed
  if (!orderData) return <></>
  if (searchParams.get("status") == "process_failed") return <></>
  if (searchParams.get("status") == "payment_failed")
    return (
      <CancelledOrderDetails
        order={orderData}
        handleRetryPaymentProcess={handleRetryPaymentProcess}
        placeOrderForCOD={placeOrderForCOD}
      />
    )
  if (autoVerification === "pending")
    return (
      <VerificationPendingCard
        setAutoVerification={setAutoVerification}
        order={orderData}
      />
    )
  if (autoVerification === "verified")
    return <VerificationVerifiedCard order={orderData} />
  if (autoVerification === "redirecting")
    return <VerificationRedirectingCard order={orderData} />
  if (autoVerification === "failed")
    return <VerificationFailedCard order={orderData} />
}

const OrderProcessedInfo = ({ order }: { order: CreateBikeOrder }) => (
  <Card className='rounded-lg px-3 py-2 sm:rounded-xl sm:px-4 sm:py-3 md:rounded-2xl md:px-6 md:py-5'>
    <CardHeader className='flex flex-row items-center justify-between space-y-0 p-0 pb-4'>
      <CardTitle className='text-base font-bold text-gray-900 sm:text-lg md:text-xl'>
        Order Details
      </CardTitle>
      <span className='text-sm text-gray-500 sm:text-base md:text-lg'>
        #{order.order_id}
      </span>
    </CardHeader>
    <Separator />
    <CardContent className='flex w-full flex-col items-center justify-start gap-4 p-0 pt-4 sm:gap-5'>
      <div className='grid w-full grid-cols-1 items-start justify-start gap-4 sm:grid-cols-2'>
        <div>
          <h3 className='mb-2 text-sm font-semibold text-neutral-900 sm:text-base'>
            Contact Details
          </h3>
          <div className='text-xs text-neutral-500 sm:text-sm'>
            <p className=''>{order.first_name}</p>
            <p className=''>{order.whatsapp_number} (WhatsApp)</p>
            {/* <p className=''>{order.}</p> */}
          </div>
        </div>
        {/* <div>
          <h3 className='mb-2 text-sm font-semibold text-neutral-900 sm:text-base'>
            Delivery Address
          </h3>
          <p className='text-wrap break-words text-xs text-neutral-500 sm:text-sm'></p>
        </div> */}
      </div>

      <div className='grid w-full grid-cols-1 items-start justify-start gap-4 sm:grid-cols-2'>
        {/* {order.payment_option === 2 && (
          <div>
            <h3 className='mb-2 text-sm font-semibold text-neutral-900 sm:text-base'>
              Transaction Details
            </h3>
            <p className='text-xs text-gray-600 sm:text-sm'>Transaction ID:</p>
            <p className='text-xs text-gray-700 sm:text-sm'>
              {order.rzp_order_id}
            </p>
          </div>
        )} */}
        {
          //   <div className=''>
          //   <h3 className='mb-2 text-sm font-semibold text-neutral-900 sm:text-base'>
          //     Payment Method
          //   </h3>
          //   <div className='text-xs text-neutral-500 sm:text-sm'>
          //     <p className=''>
          //       Payment Mode:{" "}
          //       {/* {(order.cod_handling_charges ?? 0 > 0)
          //         ? "Cash On Delivery"
          //         : "Pre-Payment"} */}
          //       {/* {order.payment_option === 6
          //         ? "Paid Online"
          //         : "Pay on Delivery (via link)"} */}
          //     </p>
          //     {/* {order.payment_option === 7 && (
          //       <p className=''>Payment Processed by: {"Razor Pay"}</p>
          //     )} */}
          //   </div>
          // </div>
        }
      </div>
    </CardContent>
  </Card>
)

function OrderStatusDetails({
  description,
  image,
  title,
}: {
  image: string
  title: string
  description: string
}) {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay: 0.3 }}
      className='flex w-full flex-col gap-4 sm:gap-6'
    >
      <Card className='flex-col rounded-xl border-0 bg-secondary-100 md:flex-row md:rounded-2xl'>
        <CardContent className='px-4 py-3 sm:px-6 sm:py-4'>
          <div className='flex flex-col items-center gap-2 text-center md:flex-row md:items-start md:gap-4 md:text-start'>
            <Image
              src={image}
              width={60}
              height={60}
              alt='Sharepal Promise Image'
              className='h-12 w-12 sm:h-16 sm:w-16'
            />
            <div>
              <h3 className='pb-1 text-sm font-semibold text-secondary-850 sm:text-base md:text-lg'>
                {title}
              </h3>
              <p className='text-xs text-secondary-850 sm:text-sm'>
                {description}
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  )
}

const VerificationPendingCard = ({
  order,
  setAutoVerification,
}: {
  order: CreateBikeOrder
  setAutoVerification: React.Dispatch<
    React.SetStateAction<"pending" | "verified" | "failed" | "redirecting">
  >
}) => (
  <CustomWrapperCard
    title_1={`Thanks, ${order?.first_name}! We have received your order.`}
    title_2={"Auto-Verification in Progress"}
    title_3={`Order No: #${order?.order_id}`}
    title_4={" This usually takes about 2 minutes."}
    icon={
      <CircularTimer
        minutes={2}
        direction={"anticlockwise"}
        theme={"default"}
        strokeWidth={20}
        showControls={false}
        onComplete={() => {
          setAutoVerification("redirecting")
        }}
      />
    }
  >
    <OrderStatusDetails
      description='Auto-Verification means your order gets instantly confirmed if we can verify your details. This process takes up to 2 minutes—no extra documents, no waiting!'
      title='What’s Auto-Verification?'
      image='https://images.sharepal.in/verification-images//image 3.svg'
    />

    <OrderProcessedInfo order={order} />
  </CustomWrapperCard>
)

const VerificationVerifiedCard = ({ order }: { order: CreateBikeOrder }) => (
  <CustomWrapperCard
    title_1='We have Received your Order Succcessfully!.'
    title_2={"Order Received!"}
    title_3={`Order No: #${order.order_id}`}
    title_4={
      "Sit back and relax. You’ll receive order confirmation via WhatsApp."
    }
    icon={<IconSuccess className='h-16 w-16 md:h-24 md:w-24' />}
  >
    {/* <Button
      variant={"primary"}
      size={"lg"}
      className='mx-auto !text-bt2'
      asChild
      // onClick={() => console.log("View order details")}
    >
      <Link href={`/dashboard/orders/${order?.order_id}`}>
        View Order Details
      </Link>
    </Button> */}

    <OrderProcessedInfo order={order} />
  </CustomWrapperCard>
)

const VerificationRedirectingCard = ({ order }: { order: CreateBikeOrder }) => (
  <CustomWrapperCard
    title_1='Oh-no, Auto-Verification didn’t go through.'
    title_2={" Redirecting to Verification"}
    title_3={` Order No: #${order.order_id}`}
    title_4={"Don’t Worry, you’ll complete it in no time."}
    icon={
      <Loader2 className='h-16 w-16 animate-spin text-primary-500 md:h-24 md:w-24' />
    }
  >
    <OrderStatusDetails
      description='Your details are used only for verification - never shared or sold. We securely store your information and use it strictly for security purposes.!'
      title='Your Data & Privacy is Always Protected'
      image='https://images.sharepal.in/verification-images//image 4.svg'
    />
  </CustomWrapperCard>
)

const VerificationFailedCard = ({ order }: { order: CreateBikeOrder }) => (
  <CustomWrapperCard
    title_1='Based on our automated checks, your order has been declined.'
    title_2={"Order Declined"}
    title_3={` Order No: #${order.order_id}`}
    title_4={
      "You can connect with our Verification Support Team to explore alternative options available."
    }
    icon={<IconCancelled className='h-16 w-16 md:h-24 md:w-24' />}
    showNeedHelp={false}
  >
    <Button
      variant={"primary"}
      size={"lg"}
      className='mx-auto !text-bt2'
      // onClick={() =>
      //   window.open(
      //     "https://api.whatsapp.com/send?phone=+917619220543&text=Hi",
      //     "_blank",
      //   )
      // }
      asChild
    >
      <Link href={"https://wa.me/917619220543"} target='_blank'>
        Contact Team
      </Link>
    </Button>
  </CustomWrapperCard>
)

const CustomWrapperCard = ({
  icon,
  title_1,
  title_2,
  title_3,
  title_4,
  children,
  showNeedHelp = true,
}: {
  title_1: string
  title_2: string
  title_3: string
  title_4: string
  icon?: React.ReactNode
  showNeedHelp?: boolean
  children: React.ReactNode
}) => (
  <div className='mx-auto flex max-w-2xl flex-col items-center justify-center gap-4 rounded-xl bg-gray-100 px-4 py-6 sm:gap-5 sm:rounded-2xl sm:px-12 sm:py-8 md:rounded-3xl md:px-16 md:py-10'>
    <motion.div
      initial={{ scale: 0 }}
      animate={{ scale: 1 }}
      transition={{ type: "spring", duration: 0.5 }}
    >
      {icon}
    </motion.div>

    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay: 0.2 }}
      className='w-full space-y-3 text-center md:space-y-4'
    >
      <Typography as='h3' className='!text-sh5 md:!text-sh3'>
        {title_1}
      </Typography>

      <Typography as='h1' className='!text-h4 text-gray-900 md:!text-h1'>
        {title_2}
      </Typography>

      <Typography as='p' className='w-full !text-b6 text-gray-600 md:!text-b4'>
        {title_3}
      </Typography>

      <Typography as='p' className='!text-b4 text-gray-700 md:!text-b2'>
        {title_4}
      </Typography>
    </motion.div>

    {children}

    {showNeedHelp && (
      <motion.a
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.6 }}
        href='#'
        className='mt-8 text-bt2 text-blue-600 hover:underline'
      >
        Need help? Contact us
      </motion.a>
    )}
  </div>
)

const CancelledOrderDetails = ({
  placeOrderForCOD,
  order,
  handleRetryPaymentProcess,
}: {
  placeOrderForCOD: UseMutateFunction<void, Error, void, unknown>
  order: CreateBikeOrder
  handleRetryPaymentProcess: UseMutateFunction<void, Error, void, unknown>
}) => (
  <CustomWrapperCard
    title_1='Not able to process your order, Please try again or Contact support'
    title_2={"Order Declined"}
    title_3={` Order No: #${order.order_id}`}
    title_4={
      "Not able to process your order,Please try again or contact support"
    }
    icon={<IconCancelled className='h-16 w-16 md:h-24 md:w-24' />}
    showNeedHelp={false}
  >
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay: 0.3 }}
      className='mx-auto flex w-full max-w-sm flex-col items-center justify-center gap-3 sm:gap-4'
    >
      <Button
        variant={"primary"}
        size={"lg"}
        className='w-full'
        onClick={() => handleRetryPaymentProcess()}
      >
        Retry Payment
      </Button>
      <Button
        variant={"normal"}
        className='flex h-max w-full flex-col rounded-full bg-violet-200 text-center hover:bg-violet-300'
        onClick={() => placeOrderForCOD()}
      >
        <span className='text-xs font-semibold leading-none text-blue-800 sm:text-sm'>
          Switch to Cash-on-Delivery
        </span>
        <span className='text-[10px] font-normal leading-none text-blue-800 sm:text-xs'>
          Pay ₹99 Delivery Charges for Switching
        </span>
      </Button>
    </motion.div>
  </CustomWrapperCard>
)
