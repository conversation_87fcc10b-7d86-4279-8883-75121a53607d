"use client"

import { Skeleton } from "@/components/ui/skeleton"
import { check_coupon_validity } from "@/methods/coupon"
import { useCheckoutStore } from "@/store/checkout-store"
import { Coupon } from "@/types/coupon"
import { useQuery } from "@tanstack/react-query"
import React, { useMemo } from "react"
// import { toast } from 'sonner'
import { useThrottle } from "@/hooks/use-throttle"
import { cn } from "@/lib/utils"
import { fetchDiscountCoupons } from "@/services/common"
import { motion } from "framer-motion"
import { CouponCard } from "../cards/coupon-card"
// import { addDays } from 'date-fns'

type Props = {
  open: boolean
  onOpenChange: React.Dispatch<React.SetStateAction<boolean>>
  onApplyCoupon: (
    code: string,
    total_rental_amount?: number,
    removeCoupon?: true,
  ) => unknown
  inCart: boolean
}

const ApplyCoupon: React.FC<Props> = ({
  open,
  onOpenChange,
  onApplyCoupon,
  inCart,
}) => {
  const { total_rent } = useCheckoutStore()
  const { rental_params, total_hours } = useCheckoutStore()

  const { data: coupons = [], isLoading } = useQuery<Coupon[], Error>({
    queryKey: ["coupons"],
    queryFn: fetchDiscountCoupons,
    enabled: open,
    staleTime: 5 * 60 * 1000,
  })

  const validCoupons = useMemo(() => {
    if (
      !rental_params?.pickup_timestamp ||
      !rental_params?.dropoff_timestamp ||
      !total_hours
    )
      return coupons.map((coupon) => ({ ...coupon, isValid: false }))

    const { pickup_timestamp, dropoff_timestamp } = rental_params
    return coupons.map((coupon) => ({
      ...coupon,
      isValid: check_coupon_validity({
        pickup_timestamp,
        dropoff_timestamp,
        total_hours,
        total_amount: total_rent,
        coupon_active: coupon.coupon_active,
        show_on_website: coupon.show_on_website,
        cart_active: coupon.cart_active,
        cart_minimum_value: coupon.cart_minimum_value,
      }),
    }))
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [coupons, total_hours])

  const handleApplyCoupon = async (couponCode: string) => {
    await onApplyCoupon(couponCode, undefined, true)
    onOpenChange(false)
  }

  const throttleHanldeApplyCoupon = useThrottle(handleApplyCoupon, 1000)

  const { applied_coupon_code } = useCheckoutStore()

  return (
    <motion.div
      initial={{ height: 0, opacity: 0 }}
      animate={{ height: open ? "auto" : 0, opacity: open ? 1 : 0 }}
      transition={{ duration: 0.3 }}
      className=''
    >
      <div
        className={cn(
          "hide-scrollbar z-0 flex w-full items-center justify-start gap-4 overflow-x-auto",
          !open && "pointer-events-none",
          inCart && "!p-0",
        )}
      >
        {isLoading ? (
          <div>
            <Skeleton />
          </div>
        ) : (
          validCoupons.map((coupon, index) => (
            <CouponCard
              className={cn("min-w-72 max-w-72")}
              key={index}
              coupon={coupon}
              onApply={() => throttleHanldeApplyCoupon(coupon.coupon_code)}
              isValid={coupon.isValid}
              showCheckbox
              isSelected={applied_coupon_code === coupon.coupon_code}
            />
          ))
        )}
      </div>
    </motion.div>
  )
}

export default ApplyCoupon
