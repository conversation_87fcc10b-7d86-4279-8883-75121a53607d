import { Button } from "../ui/button"

import { useBikeRentalHandlers } from "@/hooks/use-bike-rental-handlers"
import { format } from "date-fns"
import { useEffect } from "react"
import { CalendarEditFilledIcon, CalendarStartFilledIcon } from "sharepal-icons"
import { Typography } from "../ui/typography"

export const CheckoutDateSelectDesktop = ({
  handleRemoveCoupon,
}: {
  handleRemoveCoupon: () => void
}) => {
  const { pickup_date, dropoff_date, pickup_time, dropoff_time } =
    useBikeRentalHandlers()

  useEffect(() => {
    handleRemoveCoupon()
  }, [pickup_date, dropoff_date, pickup_time, dropoff_time])

  const formatDateTime = (date: Date | null, time: string | null) => {
    if (!date || !time) return null
    return `${format(date, "MMM dd")} at ${time}`
  }

  return (
    <div
      aria-label='Bike Rental Schedule'
      className='flex w-full items-center justify-between gap-2 rounded-full bg-gray-100 ring-2 ring-gray-200'
    >
      <div className='flex items-center justify-center gap-2 pl-4'>
        <CalendarStartFilledIcon className='!h-5 !w-5' />
        {pickup_date && dropoff_date && pickup_time && dropoff_time ? (
          <div>
            <Typography as={"span"} className='!text-b4 !text-neutral-400'>
              Pickup:{" "}
            </Typography>
            <Typography as={"span"} className='!text-sh5 text-neutral-700'>
              {formatDateTime(pickup_date, pickup_time)}
            </Typography>
            <br />
            <Typography as={"span"} className='!text-b4 !text-neutral-400'>
              Drop-off:{" "}
            </Typography>
            <Typography as={"span"} className='!text-sh5 text-neutral-700'>
              {formatDateTime(dropoff_date, dropoff_time)}
            </Typography>
          </div>
        ) : (
          <Typography as={"span"} className='text-sm'>
            Select Schedule
          </Typography>
        )}
      </div>

      <Button className='hover:bg-primary-900' disabled>
        <CalendarEditFilledIcon className='!h-4 !w-4' />
        Edit
      </Button>
    </div>
  )
}
