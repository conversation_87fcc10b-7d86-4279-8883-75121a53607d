"use client"

import { Typography } from "@/components/ui/typography"
import { getImage, moneyFormatter } from "@/functions/small-functions"
import SpImage from "@/shared/SpImage/sp-image"
import { useCheckoutStore } from "@/store/checkout-store"
import { motion } from "framer-motion"
import { FC } from "react"

interface BikeInfoCardProps {
  className?: string
}

export const BikeInfoCard: FC<BikeInfoCardProps> = ({ className = "" }) => {
  const selectedBike = useCheckoutStore((state) => state.selected_bike)

  if (!selectedBike) return null

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      className={`flex items-start gap-2 rounded-lg p-2 py-3 hover:bg-neutral-50 md:items-center md:gap-6 md:p-2 md:px-4 ${className}`}
    >
      <SpImage
        src={getImage(selectedBike.bike_images)}
        alt={selectedBike.name}
        width={68}
        height={68}
        className='h-full w-full rounded-lg object-contain'
        containerClassName='w-[60px] flex items-center justify-center h-[60px] md:w-20 md:h-20 bg-neutral-150 rounded-lg flex-shrink-0'
      />

      <div className='flex flex-1 flex-col gap-1'>
        <Typography as={"h3"} className='text-sh6 text-neutral-900 md:text-sh4'>
          {selectedBike.name}
        </Typography>
        <Typography
          as={"p"}
          className='text-o4 capitalize text-neutral-600 md:text-b6 md:text-sm'
        >
          {selectedBike.type}
        </Typography>
      </div>

      <div className='min-w-min text-right'>
        <Typography as={"p"} className='text-sh6 font-bold md:text-sh2'>
          {moneyFormatter(selectedBike.price_per_hour)} /hour
        </Typography>
      </div>
    </motion.div>
  )
}
