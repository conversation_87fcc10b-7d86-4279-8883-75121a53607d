"use client"

import { Typography } from "@/components/ui/typography"
import {
  formatDurationFromHours,
  formatTimestampForRental,
} from "@/functions/date"
import { useCheckoutStore } from "@/store/checkout-store"
import { motion } from "framer-motion"
import { ArrowRight } from "lucide-react"
import { FC } from "react"

interface RentalPeriodDisplayProps {
  className?: string
  showLocation?: boolean
  showRentPeriodOnly?: boolean
}

export const RentalPeriodDisplay: FC<RentalPeriodDisplayProps> = ({
  className = "",
  showLocation = true,
  showRentPeriodOnly,
}) => {
  const rental_params = useCheckoutStore((state) => state.rental_params)

  if (!rental_params) return null

  if (showRentPeriodOnly) {
    return <RentalPeriodOnly />
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      className={`space-y-4 ${className}`}
    >
      <div>
        <Typography
          as='h3'
          className='mb-1 font-ubuntu text-b5 font-semibold text-neutral-700'
        >
          Rental Period
        </Typography>

        <RentalPeriodOnly />
      </div>
      {showLocation && (
        <div>
          <Typography
            as='h4'
            className='mb-1 font-ubuntu text-b5 font-semibold text-neutral-700'
          >
            Pickup/Drop-off Location
          </Typography>
          <div className='w-full'>
            <Typography
              as='p'
              className='font-inter text-b6 leading-relaxed text-neutral-700'
            >
              {rental_params.hub_name ||
                "Basement 2 Parking, Soul Space Paradigm, Marathahalli Village, Marathahalli, Bengaluru, Karnataka 560037"}
            </Typography>
          </div>
        </div>
      )}
    </motion.div>
  )
}

const RentalPeriodOnly = () => {
  const { rental_params, total_hours } = useCheckoutStore()

  if (!rental_params) return null

  const pickupDateTime = formatTimestampForRental(
    rental_params.pickup_timestamp,
  )
  const dropoffDateTime = formatTimestampForRental(
    rental_params.dropoff_timestamp,
  )
  const formattedDuration = formatDurationFromHours(total_hours)

  return (
    <>
      <div className='flex items-center justify-between rounded-xl bg-gray-100 p-3 shadow-sm md:bg-neutral-50'>
        <div className='text-center'>
          <Typography
            as='p'
            className='font-ubuntu text-b4 text-neutral-700 md:text-b3'
          >
            {pickupDateTime.displayFormat}
          </Typography>
          <Typography
            as='p'
            className='font-inter text-b6 text-neutral-400 md:hidden'
          >
            Pickup
          </Typography>
        </div>
        <div className='flex items-center gap-2'>
          <ArrowRight className='h-4 w-4 text-neutral-400' />
        </div>
        <div className='text-center'>
          <Typography
            as='p'
            className='font-ubuntu text-b4 font-semibold text-neutral-700 md:text-b3'
          >
            {dropoffDateTime.displayFormat}
          </Typography>
          <Typography
            as='p'
            className='font-inter text-b6 text-neutral-400 md:hidden'
          >
            Drop-off
          </Typography>
        </div>
      </div>
      <div className='mt-2 text-center'>
        <div className='flex items-center justify-center gap-2'>
          <div className='flex-1 border-t border-dashed border-neutral-300'></div>
          <Typography
            as='span'
            className='rounded bg-neutral-100 px-2 font-ubuntu text-b5 font-semibold text-primary-600 md:text-b4'
          >
            {formattedDuration}
          </Typography>
          <div className='flex-1 border-t border-dashed border-neutral-300'></div>
        </div>
      </div>
    </>
  )
}
