"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  <PERSON><PERSON>,
  <PERSON>alogContent,
  DialogDescription,
  <PERSON>alog<PERSON>ooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import { fetchWithAuthPost } from "@/utils/fetchWithAuth"
import { zodResolver } from "@hookform/resolvers/zod"
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query"
import { Loader2, XIcon } from "lucide-react"
import type { Dispatch, SetStateAction } from "react"
import { useForm } from "react-hook-form"
import { toast } from "sonner"
import * as z from "zod"

import { fetchCancellationReasons } from "@/services/orders"

const formSchema = z.object({
  order_id: z.string(),
  cancellation_reason: z
    .string()
    .min(1, "Please select a reason for cancellation"),
  description: z
    .string()
    .max(200, "Description must be 200 characters or less")
    .optional(),
})

type FormValues = z.infer<typeof formSchema>

async function cancelOrder(data: FormValues) {
  const response = await fetchWithAuthPost(
    "https://api.sharepal.in/api:AIoqxnqr/order/cancel",
    {
      ...data,
      reason_type: "external",
    },
  )
  return response
}

interface OrderCancellationDialogProps {
  setOrderCancelModal: Dispatch<SetStateAction<boolean>>
  orderCancelModal: boolean
  orderId: string
}

export function OrderCancellationDialog({
  orderId,
  orderCancelModal,
  setOrderCancelModal,
}: OrderCancellationDialogProps) {
  const { data: cancellationReasons, isLoading: isLoadingReasons } = useQuery({
    queryKey: ["cancellation_reasons"],
    queryFn: fetchCancellationReasons,
    staleTime: 1000 * 60 * 5, // 5 minutes
    enabled: orderCancelModal,
    refetchOnWindowFocus: false,
  })

  const queryClient = useQueryClient()

  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      order_id: orderId,
      cancellation_reason: "",
      description: "",
    },
  })

  const mutation = useMutation({
    mutationFn: cancelOrder,
    onSuccess: () => {
      toast.success(
        "We've received your cancellation request and will process it shortly.",
      )
      form.reset()
      setOrderCancelModal(false)
      queryClient.invalidateQueries({
        queryKey: ["orderStatus"],
      })
    },
    onError: (error) => {
      console.error("Error submitting cancellation request:", error)
      toast.error(
        error instanceof Error
          ? JSON.parse(error.message).message
          : "There was an error submitting your cancellation request. Please try again.",
      )
    },
  })

  const onSubmit = (values: FormValues) => {
    if (
      values.cancellation_reason.toLowerCase() === "others" &&
      !values.description
    ) {
      toast.info("Please provide a description for your reason.")
      return
    }
    mutation.mutate(values)
  }

  return (
    <Dialog open={orderCancelModal} onOpenChange={setOrderCancelModal}>
      <DialogContent className='max-w-xl !rounded-3xl bg-gray-100 p-1 max-md:max-w-[95%] md:p-4'>
        <div className='p-4'>
          <DialogHeader className='w-full pt-5'>
            <DialogTitle className='w-full text-center md:!text-h4'>
              Submit Order Cancellation Request
            </DialogTitle>
            <DialogDescription className='text-center !text-b4'>
              Order No: #{orderId}
            </DialogDescription>
            <DialogDescription className='!mb-3 text-center !text-b4'>
              Please let us know why you want to cancel this order. This will
              help us improve our services and provide you with a better
              experience in the future.
            </DialogDescription>
            <button type='button' className='absolute right-5 top-5'>
              <XIcon />
            </button>
          </DialogHeader>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className='space-y-6'>
              <FormField
                control={form.control}
                name='cancellation_reason'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className='!text-b4'>
                      Why do you want to cancel this order?
                    </FormLabel>
                    <Select onValueChange={field.onChange} value={field.value}>
                      <FormControl>
                        <SelectTrigger className='rounded-3xl border-2 px-4 py-5 shadow-transparent'>
                          {isLoadingReasons ? (
                            <Loader2 className='mr-2 h-4 w-4 animate-spin' />
                          ) : (
                            <SelectValue
                              className='!text-sh3'
                              placeholder='Select from the given reasons'
                            />
                          )}
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent className='rounded-2xl !text-sh3'>
                        {cancellationReasons?.map((reason) => (
                          <SelectItem
                            className='!text-sh3'
                            key={reason.id}
                            value={reason.reason}
                          >
                            {reason.reason}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name='description'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className='!text-b4'>
                      Please tell us more (Optional)
                    </FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder='Type your details here...'
                        className='resize-none md:!text-b2'
                        rows={7}
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                    <div className='text-right text-o4 text-muted-foreground'>
                      {field.value?.length || 0}/200
                    </div>
                  </FormItem>
                )}
              />
              <DialogFooter className='flex items-center gap-2'>
                <Button
                  type='button'
                  variant='outline'
                  className='w-full md:max-w-[70%]'
                  size='lg'
                  onClick={() => {
                    setOrderCancelModal(false)
                    toast.success("Thank you for deciding to keep your order.")
                  }}
                >
                  Keep My Order
                </Button>
                <Button
                  type='submit'
                  disabled={mutation.isPending}
                  variant='destructive'
                  size='lg'
                  className='w-full md:max-w-[70%]'
                >
                  {mutation.isPending ? "Submitting..." : "Cancel My Order"}
                </Button>
              </DialogFooter>
            </form>
          </Form>
        </div>
      </DialogContent>
    </Dialog>
  )
}
