import SpImage from "@/shared/SpImage/sp-image"
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "../ui/card"

type Props = {
  title: string
  description: string
  image_url: string
}

export const WhySharepalRentingCard = ({
  title,
  description,
  image_url,
}: Props) => {
  return (
    <Card className='relative w-full flex-1 space-y-0 rounded-2xl border border-neutral-200 bg-gray-100 p-0 md:max-w-full md:rounded-3xl'>
      <CardHeader className='relative flex h-[198px] flex-col items-end justify-center px-3'>
        <SpImage
          src={image_url}
          alt={title}
          width={400}
          height={400}
          containerClassName='absolute -top-[70px] left-1/2 z-[1] -translate-x-1/2 md:w-[300px] w-[260px]'
        />
      </CardHeader>
      <CardContent className='h-auto space-y-3 p-3 pb-5 md:p-6'>
        <CardTitle className='w-full text-center text-sh1 text-primary-900 md:!text-h4'>
          {title}
        </CardTitle>
        <CardDescription className='w-full text-center !text-b4 text-neutral-400 md:!text-sh3'>
          {description}
        </CardDescription>
      </CardContent>
    </Card>
  )
}
