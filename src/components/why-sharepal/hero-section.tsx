"use client"

import SpImage from "@/shared/SpImage/sp-image"
import { motion } from "framer-motion"

export function Herosection() {
  return (
    <section className='relative h-auto min-h-[400px] bg-gray-100 py-16 md:min-h-[650px] lg:py-24'>
      <div className='relative mx-auto flex-col gap-4 px-4'>
        {/* main text */}
        <div className='z-10 mx-auto flex w-full max-w-4xl flex-col items-center justify-center gap-3 px-4 py-8 md:py-16'>
          <motion.p
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className='mb-3 text-b6 font-semibold tracking-widest md:mb-6 md:text-sh1'
          >
            WHY RENT FROM SHAREPAL
          </motion.p>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className='space-y-8 text-center font-ubuntu text-d7 md:text-d3 lg:space-y-2'
          >
            <div className=''>
              Choose Confidence <br />
              <span className='relative inline-block'>
                Choose SharePal.
                <SpImage
                  src='https://images.sharepal.in/carepal/waiver-line.svg'
                  width={350}
                  height={50}
                  alt='Waiver Line'
                  className='inline-block h-auto w-full'
                  containerClassName='absolute w-full bottom-[-10px] md:bottom-[-27px]'
                />
              </span>
            </div>
          </motion.div>
        </div>

        {/* images  */}
        <div className='flex items-center justify-center gap-4'>
          <SpImage
            src={`https://images.sharepal.in/common-icons/sharepal-adventure-logo.svg`}
            width={350}
            height={100}
            alt='SharePal Adventure'
            className='h-auto w-[122px] md:h-auto md:w-[244px]'
          />
          <SpImage
            src='https://images.sharepal.in/common-icons/carepal-assured-logo.svg'
            width={350}
            height={100}
            alt='CarePal Assured'
            className='h-auto w-[122px] md:h-auto md:w-[244px]'
          />
          <SpImage
            src='https://images.sharepal.in/common-icons/sharepal-assured-logo.svg'
            width={350}
            height={100}
            alt='SharePal Assured'
            className='h-auto w-[122px] md:h-auto md:w-[244px]'
          />
        </div>
      </div>
    </section>
  )
}
