import { cn } from "@/lib/utils"
import SpImage from "@/shared/SpImage/sp-image"
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "../ui/card"

type Props = {
  title1: string
  title2: string
  description: string
  image_url: string
  color: string
  bgColor: string
  logo: string
}

export const WhatMakesusCard = ({
  title1,
  title2,
  description,
  image_url,
  color,
  bgColor,
  logo,
}: Props) => (
  <Card
    className={cn(
      "relative min-h-[424px] w-full min-w-[248px] flex-1 space-y-0 rounded-3xl border border-primary-100 p-0 bg-blend-multiply md:h-[536px] md:min-w-[360px] md:max-w-full",

      bgColor,
    )}
  >
    <CardHeader className='p-3 pb-0 md:px-6 md:py-4'>
      <SpImage
        src={logo}
        width={350}
        height={100}
        alt={title1 + title2}
        className='h-[65px] w-[122px] md:w-[164px]'
      />
      <CardTitle className='w-full font-bold text-primary-900 md:!text-h2'>
        {title1}
        <br />
        <span className={cn(color)}>{title2}</span>
      </CardTitle>
      <CardDescription className='w-full text-xs font-medium leading-4 text-neutral-500 md:text-base md:leading-6'>
        {description}
      </CardDescription>
    </CardHeader>
    <CardContent className='flex flex-col items-end justify-center p-0'>
      <SpImage
        src={image_url}
        alt={title1 + title2}
        width={400}
        height={400}
        className='h-full w-full'
        containerClassName='absolute bottom-0 right-0 left-1/2 z-[1] -translate-x-1/2 max-w-[350px] w-full'
      />
    </CardContent>
  </Card>
)
