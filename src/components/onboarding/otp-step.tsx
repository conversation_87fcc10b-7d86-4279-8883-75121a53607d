"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormMessage,
} from "@/components/ui/form"
import {
  InputOTP,
  InputOTPGroup,
  InputOTPSlot,
} from "@/components/ui/input-otp"
import { trackLoginOtpSent, trackNumberSubmitted } from "@/lib/gtag-event"
import { cn } from "@/lib/utils"
import { otpSchema, type OtpFormData } from "@/lib/validations/auth"
import { sendOTP, verifyOTP } from "@/services/login"
import { getLoggedInUser } from "@/services/user"
import { useCityStore } from "@/store/city-store"
import { useOnboardingStore } from "@/store/onboarding-store"
import { useUserStore } from "@/store/user-store"
import { zodResolver } from "@hookform/resolvers/zod"
import { useMutation } from "@tanstack/react-query"
import { Clock, Loader2, RefreshCcwIcon } from "lucide-react"
import { useEffect, useState } from "react"
import { useForm } from "react-hook-form"
import { EditOutlinedIcon } from "sharepal-icons"

interface OtpStepProps {
  phoneNumber: string
  //promise oncomplete async
  isOnboarding?: boolean
  onCompleteAction?: () => void
}

export function OtpStep({
  phoneNumber,
  isOnboarding = true,
  onCompleteAction,
}: OtpStepProps) {
  const [timer, setTimer] = useState(30)
  const [isResending, setIsResending] = useState(false)
  const { setIsLoggedIn, fetchUser } = useUserStore()
  const { setStep, closeModal, setIsNonCloseable } = useOnboardingStore()
  const { selectedCity } = useCityStore()

  const form = useForm<OtpFormData>({
    resolver: zodResolver(otpSchema),
    defaultValues: {
      otp: "",
    },
  })

  useEffect(() => {
    const interval = setInterval(() => {
      setTimer((prev) => (prev > 0 ? prev - 1 : 0))
    }, 1000)
    return () => clearInterval(interval)
  }, [])

  const handleResendOtp = async () => {
    setIsResending(true)

    await sendOTP("91", phoneNumber)
    trackLoginOtpSent("91", phoneNumber, selectedCity.city_url)
    setTimer(30)
    setIsResending(false)
  }

  const { mutate: onSubmit, isPending } = useMutation({
    mutationFn: async (data: OtpFormData) => {
      const response = await verifyOTP("91", phoneNumber, data.otp)
      return response
    },
    onSuccess: async (response) => {
      if (response?.message == "Incorrect OTP") {
        form.setError("otp", {
          type: "manual",
          message: "Incorrect OTP",
        })
      } else if (typeof response === "string") {
        localStorage.setItem("token", response)
        setIsLoggedIn(true)
        trackNumberSubmitted("91", phoneNumber, selectedCity.city_url)
        const user = await getLoggedInUser(selectedCity.city_url)

        if (
          user &&
          (user?.first_name === "" || user?.last_name === "")
          //  || user.email === null
        ) {
          setStep("profile")
        } else {
          // Update User City
          // const city = {
          //   user_city: city,
          //   city_type: city_type,
          //   user_partner: localStorage.getItem("partner") ?? " ",
          // };
          // updateUser(city);
          setIsNonCloseable(false)
          closeModal()
          // handle other operations here
          if (!isOnboarding && onCompleteAction) {
            onCompleteAction()
          }
          fetchUser(selectedCity.city_url)
        }
      }
    },
  })

  return (
    <Form {...form}>
      <form
        onSubmit={form.handleSubmit((data) => onSubmit(data))}
        className='flex h-full flex-col justify-between gap-10'
      >
        <div className='flex flex-col gap-6'>
          <div className='space-y-2 text-center'>
            <h2 className='text-xl font-bold tracking-tight'>
              Verify OTP to continue!
            </h2>
            <p className='flex justify-center gap-1 text-sm font-bold text-gray-900'>
              <span className='font-medium text-neutral-500'>
                Enter 6 digit OTP sent to
              </span>{" "}
              {phoneNumber}
              <button onClick={() => setStep("phone")}>
                <EditOutlinedIcon />
              </button>
            </p>
          </div>
          <div className='mx-auto flex h-[180px] max-w-max flex-col gap-0.5'>
            <FormField
              control={form.control}
              name='otp'
              render={({ field }) => (
                <FormItem>
                  <FormControl>
                    <InputOTP pattern='\d*' maxLength={6} {...field}>
                      <InputOTPGroup className='w-full justify-center gap-1'>
                        {Array.from({ length: 6 }).map((_, index) => (
                          <InputOTPSlot
                            className='h-12 w-12 rounded-2xl border-2 border-neutral-200'
                            key={index}
                            index={index}
                          />
                        ))}
                      </InputOTPGroup>
                    </InputOTP>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className=''>
              {timer > 0 ? (
                <p className='flex items-center gap-2 text-sm text-neutral-400'>
                  <Clock className='h-4 w-4' />
                  Resend OTP in <strong>{timer} sec</strong>
                </p>
              ) : (
                <Button
                  variant='link'
                  className='m-0 p-0 text-sm text-primary-500'
                  disabled={isResending}
                  onClick={handleResendOtp}
                >
                  {isResending && (
                    <Loader2 className='mr-2 h-4 w-4 animate-spin' />
                  )}
                  Resend OTP <RefreshCcwIcon />
                </Button>
              )}
            </div>
          </div>
        </div>
        <div>
          <Button
            type='submit'
            disabled={!form.formState.isValid}
            className={cn(
              "flex h-12 w-full items-center justify-center gap-2 rounded-full bg-primary-500 text-base font-semibold leading-tight text-white hover:bg-primary-600 disabled:bg-neutral-200 disabled:text-neutral-500",
            )}
          >
            {isPending ? (
              <Loader2 className='animate-spin' />
            ) : (
              <span>Verify OTP</span>
            )}
          </Button>
          <p className='pt-5 text-center text-xs text-muted-foreground'>
            By continuing, you agree to the{" "}
            <Button variant='link' className='h-auto p-0'>
              Terms of Service
            </Button>{" "}
            and acknowledge the{" "}
            <Button variant='link' className='h-auto p-0'>
              Privacy Policy
            </Button>
            .
          </p>
        </div>
      </form>
    </Form>
  )
}
