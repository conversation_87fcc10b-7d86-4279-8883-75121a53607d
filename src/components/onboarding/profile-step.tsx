"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { trackCreateAccount } from "@/lib/gtag-event"
import { cn } from "@/lib/utils"
import { profileSchema, type ProfileFormData } from "@/lib/validations/auth"
import { updateUser } from "@/services/user"
import { useOnboardingStore } from "@/store/onboarding-store"
import { useUserStore } from "@/store/user-store"
import { zodResolver } from "@hookform/resolvers/zod"
import { useState } from "react"
import { useForm } from "react-hook-form"

interface ProfileStepProps {
  onComplete: () => void
}

export function ProfileStep({}: ProfileStepProps) {
  const [, setIsSubmitting] = useState(false)
  const { setUser, user } = useUserStore()
  const { closeModal } = useOnboardingStore()
  const form = useForm<ProfileFormData>({
    resolver: zod<PERSON><PERSON><PERSON><PERSON>(profileSchema),
    defaultValues: {
      first_name: "",
      last_name: "",
      email: "",
    },
  })

  const onSubmit = async () => {
    setIsSubmitting(true)
    // const data = await fetchWithAuth("/update_user_new", {
    //   method: "PUT",
    //   body: JSON.stringify({
    //     first_name: form.getValues("first_name"),
    //     last_name: form.getValues("last_name"),
    //     email: form.getValues("email"),
    //   }),
    // })

    const data = await updateUser({
      first_name: form.getValues("first_name"),
      last_name: form.getValues("last_name"),
      email: form.getValues("email"),
    })

    if (user)
      trackCreateAccount(
        {
          ...user,
          first_name: form.getValues("first_name"),
          last_name: form.getValues("last_name"),
          email: form.getValues("email"),
          whatsapp_number: user?.whatsapp_number || user?.calling_number || "",
          country_code_whatsapp: 91,
        },
        user,
      )
    if (data) setUser(data)
    closeModal()
    setIsSubmitting(false)
  }

  return (
    <Form {...form}>
      <form
        onSubmit={form.handleSubmit(onSubmit)}
        className='flex h-full flex-col justify-between gap-10'
      >
        <div className='flex flex-col gap-6'>
          <div className='space-y-2 text-center'>
            <h2 className='text-xl font-semibold tracking-tight'>
              Let&apos;s Complete Your Profile!
            </h2>
            <p className='text-sm text-muted-foreground'>
              We need a few details to complete your profile
            </p>
          </div>
          <FormField
            control={form.control}
            name='first_name'
            render={({ field }) => (
              <FormItem>
                <FormLabel>
                  First Name <span className='text-red-500'>*</span>
                </FormLabel>
                <FormControl>
                  <Input placeholder='Your name' {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name='last_name'
            render={({ field }) => (
              <FormItem>
                <FormLabel>
                  Last Name <span className='text-red-500'>*</span>
                </FormLabel>
                <FormControl>
                  <Input placeholder='Your surname' {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name='email'
            render={({ field }) => (
              <FormItem>
                <FormLabel>
                  Email Address <span className='text-red-500'>*</span>
                </FormLabel>
                <FormControl>
                  <Input placeholder='<EMAIL>' {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
        <div>
          <Button
            type='submit'
            disabled={!form.formState.isValid}
            className={cn(
              "flex h-12 w-full items-center justify-center gap-2 rounded-full bg-primary-500 text-base font-semibold leading-tight text-white hover:bg-primary-600 disabled:bg-neutral-200 disabled:text-neutral-500",
            )}
          >
            <span>Create Account</span>
          </Button>
          <p className='pt-5 text-center text-xs text-muted-foreground'>
            By continuing, you agree to the{" "}
            <Button variant='link' className='h-auto p-0'>
              Terms of Service
            </Button>{" "}
            and acknowledge the{" "}
            <Button variant='link' className='h-auto p-0'>
              Privacy Policy
            </Button>
            .
          </p>
        </div>
      </form>
    </Form>
  )
}
