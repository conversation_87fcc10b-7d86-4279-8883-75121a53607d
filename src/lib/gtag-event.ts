import { getCookie } from "@/functions/cookies"

import { User } from "@/types/user"
import { sendGTMEvent } from "@next/third-parties/google"

const getFirstString = (productCodesString: string, splitWith = ";") => {
  // Check if the string is empty or undefined
  if (!productCodesString) {
    return null
  }

  // Split the string using ';' as delimiter
  const productCodes = productCodesString.split(splitWith)

  // Return the first element (first product code)
  return productCodes?.[0]
}

// Helper function to trigger Google Analytics events
function triggerEvent(eventName: string, properties: unknown) {
  sendGTMEvent({
    event: eventName,
    eventModel: properties,
  })
}

// Helper function to get common properties
function getCommonProperties(
  title: string,
  price: number,
  productId: string,
  image: string,
  category: string,
  additionalProps = {},
) {
  // const cityData = JSON.parse(localStorage.getItem('cityData') || '{}')
  // or
  const city_cookie = getCookie("selected_city")

  return {
    Title: title,
    Price: Number(price), // Assuming price is a number
    "Product Code": getFirstString(productId),
    Image: image,
    Category: category,
    ...additionalProps,
  }
}

function getDateData() {
  if (typeof window === "undefined") return

  if (!JSON.parse(localStorage.getItem("rental-storage") || "{}")?.state) return

  const dateData = JSON.parse(
    localStorage.getItem("rental-storage") || "{}",
  ).state
  return {
    // startDate: `${new Date(dateData.startDate)}`,
    // endDate: `${new Date(dateData.endDate)}`,
    startDate: new Date(dateData?.delivery_date),
    endDate: new Date(dateData?.pickup_date),
    // testdate: new Date(dateData.endDate)?.toISOString(),

    // startDate: new Date(dateData.startDate)
    //   .toISOString()
    //   .slice(0, 19)
    //   .replace("T", " "),
    // endDate: new Date(dateData.endDate)
    //   .toISOString()
    //   .slice(0, 19)
    //   .replace("T", " "),
    totalDays: dateData.total_days,
  }
}

export function trackCitySelected(cityUrl: string) {
  triggerEvent("City Selected", { City: cityUrl })
}

export function trackCategoryViewed(
  showHeading: string,
  city: string,
  type = "Bike",
) {
  triggerEvent("Category Viewed", {
    "Category Name": showHeading?.trim(),
    City: city,
    Type: type,
  })
}

export function trackSuperCategoryViewed(
  showHeading: string,
  city: string,
  super_category_name: string,
  type = "Bike",
) {
  triggerEvent("Super Category Viewed", {
    "SuperCategory Name": showHeading?.trim(),
    City: city,
    Type: type,
  })
}

export function trackSubCategoryViewed(
  showHeading: string,
  city: string,
  super_category_name: string,
  type = "Bike",
) {
  triggerEvent("Sub Category Viewed", {
    "SubCategory Name": showHeading?.trim(),
    City: city,
    Type: type,
    super_category_name,
  })
}

export function trackProductViewed(
  pName: string,
  discountRent: number,
  itemCode: string,
  image: string,
  cat_sname: string,
  super_category_name: string,
  pageType = "Bike",
) {
  const properties = getCommonProperties(
    pName,
    discountRent,
    itemCode,
    image,
    cat_sname,
    { Type: pageType, super_category_name },
  )
  triggerEvent("Product Viewed", properties)
}

export function trackRentChartViewed(
  title: string,
  price: number,
  productId: string,
  image: string,
  category: string,
) {
  const properties = getCommonProperties(
    title,
    price,
    productId,
    image,
    category,
  )
  triggerEvent("Rent Chart Viewed", properties)
}

export function trackDateSelectInitiated(
  eventName: string,
  title: string,
  price: number,
  productId: string,
  image: string,
  duration: string,
  category: string,
) {
  const properties = getCommonProperties(
    title,
    price,
    productId,
    image,
    category,
    { Duration: duration },
  )
  triggerEvent(eventName, properties)
}

export function trackCalendarDateSelect() {
  const dateData = getDateData()

  const { startDate, endDate, totalDays } = dateData || {}

  const properties = {
    "Start Date": startDate,
    "End Date": endDate,
    "Total Days": totalDays,
  }
  triggerEvent("Rent Plan selected", properties)
}

export function trackCalenderOpened() {
  const dateData = getDateData()
  const { startDate, endDate, totalDays } = dateData || {}

  const properties = {
    "Start Date": startDate,
    "End Date": endDate,
    "Total Days": totalDays,
  }
  triggerEvent("Rent Plan selected", properties)
}

export function trackAddToCart(
  title: string,
  price: number,
  productId: string,
  image: string,
  category: string,
  size: string,
  type: string,
  super_category_name: string,
) {
  const dateData = getDateData()
  const { startDate, endDate, totalDays } = dateData || {}

  const properties = getCommonProperties(
    title,
    price,
    productId,
    image,
    category,
    {
      "Start Date": startDate,
      "End Date": endDate,
      "Total Days": totalDays,
      Size: size,
      Type: type, // it is basically page type Rent/Buy
      quantity: 1,
      super_category_name,
    },
  )
  triggerEvent("Add to Cart", properties)
}

export function trackRemoveFromCart(
  title: string,
  price: number,
  productId: string,
  image: string,
  category: string,
  size: string,
  quantity: number,
  type: string,
) {
  const dateData = getDateData()
  const { startDate, endDate, totalDays } = dateData || {}

  const properties = getCommonProperties(
    title,
    price,
    productId,
    image,
    category,
    {
      "Start Date": startDate,
      "End Date": endDate,
      "Total Days": totalDays,
      Size: size,
      Quantity: quantity,
      Type: type, // it is basically page type Rent/Buy
    },
  )
  triggerEvent("Removed from Cart", properties)
}

export function trackLikeDislike(
  eventName: string,
  title: string,
  price: number,
  productId: string,
  image: string,
  category: string,
  size: string,
  type: string,
) {
  const dateData = getDateData()
  const { startDate, endDate, totalDays } = dateData || {}

  const properties = getCommonProperties(
    title,
    price,
    productId,
    image,
    category,
    {
      "Start Date": startDate,
      "End Date": endDate,
      "Total Days": totalDays,
      Size: size,
      Type: type, // it is basically page type Rent/Buy
    },
  )
  triggerEvent(eventName, properties)
}

export function trackPaymentFailed(
  total: number,
  reason: string,
  orderId: string,
) {
  triggerEvent("Payment Failed", {
    Total: total,
    Reason: reason,
    "Order Id": orderId,
  })
}

export function trackLoginInitiated(city: string) {
  triggerEvent("Login Opened", { City: city })
}

export function trackLoginOtpSent(
  country_code: string,
  number: string,
  city: string,
) {
  triggerEvent("Login OTP Sent", {
    "Country Code": country_code,
    "Calling Number": number,
    City: city,
  })
}

export function trackNumberSubmitted(
  country_code: string,
  number: string,
  city: string,
) {
  triggerEvent("Logged In", {
    "Country Code": country_code,
    "Calling Number": number,
    City: city,
  })
}
export function trackOTPVerification(eventName: string, user: User) {
  triggerEvent(eventName, {
    "User Id": user?.id,
    "Country Code": user?.country_code_calling,
    "Calling Number": user?.calling_number,
    "Country Code Whatsapp": user?.country_code_whatsapp,
    "Whatsapp Number": user?.whatsapp_number,
    Email: user?.email,
    "First Name": user?.first_name,
    "Last Name": user?.last_name,
  })
}

export function trackCreateAccount(userFormData: User, user: User) {
  triggerEvent("user_signup_details_submitted", {
    "User Id": user?.id,
    "Country Code": user?.country_code_calling,
    "Calling Number": user?.calling_number,
    "Country Code Whatsapp": userFormData?.country_code_whatsapp,
    "Whatsapp Number": userFormData?.whatsapp_number,
    Email: userFormData?.email,
    "First Name": userFormData?.first_name,
    "Last Name": userFormData?.last_name,
  })
}
export function trackAccountDetailsUpdated() {
  // number:string, email:string
  triggerEvent("Account Details Updated", {
    // "Calling Number": number,
    // Email: email,
  })
}

export function trackCompleteVerificationInitiated(user: User) {
  triggerEvent("Complete Verification Initiated", {
    "User Id": user?.id,
    "Country Code": user?.country_code_calling,
    "Calling Number": user?.calling_number,
    "Country Code Whatsapp": user?.country_code_whatsapp,
    "Whatsapp Number": user?.whatsapp_number,
    Email: user?.email,
    "First Name": user?.first_name,
    "Last Name": user?.last_name,
    // "Calling Number": number,
    // Email: email,
  })
}
export function trackCompleteVerificationStarted(
  user: User,
  verificationType: string,
) {
  triggerEvent("Complete Verification Started", {
    "User Id": user?.id,
    "Country Code": user?.country_code_calling,
    "Calling Number": user?.calling_number,
    "Country Code Whatsapp": user?.country_code_whatsapp,
    "Whatsapp Number": user?.whatsapp_number,
    Email: user?.email,
    "First Name": user?.first_name,
    "Last Name": user?.last_name,
    "Verification Type": verificationType,

    // "Calling Number": number,
    // Email: email,
  })
}
export function OtpRequestInVerification(
  eventName: string,
  user: User,
  verificationType: string,
) {
  triggerEvent(eventName, {
    "User Id": user?.id,
    "Country Code": user?.country_code_calling,
    "Calling Number": user?.calling_number,
    "Country Code Whatsapp": user?.country_code_whatsapp,
    "Whatsapp Number": user?.whatsapp_number,
    Email: user?.email,
    "First Name": user?.first_name,
    "Last Name": user?.last_name,
    "Verification Type": verificationType,
    // "Calling Number": number,
    // Email: email,
  })
}
export function trackCompleteVerification(
  eventName: string,
  user: User,
  verificationType: string,
  isSuccess: boolean,
) {
  triggerEvent(eventName, {
    "User Id": user?.id,
    "Country Code": user?.country_code_calling,
    "Calling Number": user?.calling_number,
    "Country Code Whatsapp": user?.country_code_whatsapp,
    "Whatsapp Number": user?.whatsapp_number,
    Email: user?.email,
    "First Name": user?.first_name,
    "Last Name": user?.last_name,
    "Verification Type": verificationType,
    "Verification Success": isSuccess,
    "Verification Failure": !isSuccess,
    // "Calling Number": number,
    // Email: email,
  })
}

export function trackContactSupportSelected({
  order_id,
  user_id,
}: {
  order_id: string
  user_id: number
}) {
  triggerEvent("Selected Contact Support", {
    "Order Id": order_id,
    "User Id": user_id,
  })
}

export function trackExtension({
  order_id,
  user_id,
  eventName,
  pickup_date,
  new_pickup_date,
  amount,
  days,
}: {
  order_id: string
  user_id: number
  eventName: string
  pickup_date: Date
  new_pickup_date: Date
  amount: number
  days: number
}) {
  triggerEvent(eventName, {
    "Order Id": order_id,
    "User Id": user_id,
    "Pickup Date": pickup_date,
    "New Pickup Date": new_pickup_date,
    Amount: amount,
    Days: days,
  })
}
