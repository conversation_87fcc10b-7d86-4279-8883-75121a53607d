import { Metadata } from "next"

interface MetadataProps {
  title?: string
  description?: string
  image?: string | null
  icons?: Metadata["icons"]
  noIndex?: boolean
  keywords?: string[]
  author?: string
  twitterHandle?: string
  type?: "website" | "article" | "profile"
  locale?: string
  alternates?: Record<string, string>
  publishedTime?: string
  modifiedTime?: string
}

export const generateStaticMetadata = ({
  title = `${process.env.NEXT_PUBLIC_APP_NAME ?? "SharePal"}  | Rent Bikes in Bangalore`,
  description = `Rent top-quality adventure bikes for your next road trip or weekend getaway. Book online for flexible hourly or daily plans. Bike Rentals available in Bangalore.`,

  image = "https://images.sharepal.in/misc/hard-coded/sharepal/sharepal-logo-new.jpg",
  icons = [
    {
      rel: "apple-touch-icon",
      sizes: "32x32",
      url: "/icons/apple-touch-icon.png",
    },
    {
      rel: "icon",
      sizes: "32x32",
      url: "/icons/favicon-32x32.png",
    },
    {
      rel: "icon",
      sizes: "16x16",
      url: "/icons/favicon-16x16.png",
    },
  ],
  noIndex = false,
  keywords = [
    "Bike Rentals",
    "Adventure Bike Rentals",
    "Premium Bike Hire",
    "Motorcycle Rentals",
    "Rent Bikes Bangalore",
    "Self-drive Bike Rentals",
    "Rent Himalayan",
    "Rent Royal Enfield",
    "Bike rentals near me",
  ],
  author = process.env.NEXT_PUBLIC_AUTHOR_NAME || "SharePal",
  twitterHandle = "@sharepal",
  type = "website",
  locale = "en_US",
  alternates = {},
  publishedTime,
  modifiedTime,
}: MetadataProps = {}): Metadata => {
  const metadataBase = new URL(
    process.env.NEXT_PUBLIC_APP_URL || "https://bikerentals.sharepal.in/",
  )
  const imageUrl = image ? new URL(image, metadataBase).toString() : null

  return {
    metadataBase,
    title: {
      template: `%s | ${process.env.NEXT_PUBLIC_APP_NAME ?? "SharePal"}`, // use this if you want other title should be filled at the place of %s
      // template: `${process.env.NEXT_PUBLIC_APP_NAME}`,
      default: title,
      // absolute
    },
    description,
    keywords,
    authors: [{ name: author }],
    creator: author,
    publisher: process.env.NEXT_PUBLIC_APP_NAME,
    formatDetection: {
      email: false,
      address: false,
      telephone: false,
    },
    icons,

    // OpenGraph
    openGraph: {
      type,
      siteName: process.env.NEXT_PUBLIC_APP_NAME,
      title,
      description,
      ...(imageUrl && {
        images: [
          {
            url: imageUrl,
            width: 1200,
            height: 630,
            alt: title,
          },
        ],
      }),
      locale,
      alternateLocale: Object.keys(alternates),
      ...(publishedTime && { publishedTime }),
      ...(modifiedTime && { modifiedTime }),
    },

    // Twitter
    twitter: {
      card: imageUrl ? "summary_large_image" : "summary",
      site: twitterHandle,
      creator: twitterHandle,
      title,
      description,
      ...(imageUrl && { images: [imageUrl] }),
    },

    // Robots
    robots: {
      index: !noIndex,
      follow: !noIndex,
      googleBot: {
        index: !noIndex,
        follow: !noIndex,
        "max-video-preview": -1,
        "max-image-preview": "large",
        "max-snippet": -1,
      },
    },

    // Verification
    verification: {
      google: process.env.NEXT_PUBLIC_GOOGLE_SITE_VERIFICATION,
      yandex: process.env.NEXT_PUBLIC_YANDEX_VERIFICATION,
      yahoo: process.env.NEXT_PUBLIC_YAHOO_VERIFICATION,
    },
  }
}
