import {
  format,
  isAfter,
  isBefore,
  isSameDay,
  isToday,
  isValid,
  setHours,
  setMinutes,
  startOfDay,
} from "date-fns"

export interface TimeSlot {
  value: string
  label: string
  disabled: boolean
  cost?: number
}

// Business hours configuration for bike rentals
export const BUSINESS_HOURS = {
  START_HOUR: 10,
  END_HOUR: 22,
  MIN_RENTAL_HOURS: 24,
  MAX_RENTAL_DAYS: 30, // Configurable max rental period
  BUFFER_MINUTES: 30, // Buffer time for same-day bookings
} as const

// Generate bike rental time slots with 30-minute intervals using business hours
export const generateTimeSlots = (date: Date): TimeSlot[] => {
  const slots: TimeSlot[] = []
  const now = new Date()

  // Generate slots using business hours configuration
  for (
    let hour = BUSINESS_HOURS.START_HOUR;
    hour <= BUSINESS_HOURS.END_HOUR;
    hour++
  ) {
    for (let minute = 0; minute < 60; minute += 30) {
      const slotTime = setMinutes(setHours(date, hour), minute)
      const timeString = format(slotTime, "HH:mm")
      const displayTime = format(slotTime, "h:mm aa")

      // For today, disable slots that have already passed (with buffer)
      // For future dates, all slots are available
      const bufferTime = new Date(
        now.getTime() + BUSINESS_HOURS.BUFFER_MINUTES * 60 * 1000,
      )
      const isDisabled = isToday(date) ? isBefore(slotTime, bufferTime) : false

      slots.push({
        value: timeString,
        label: displayTime,
        disabled: isDisabled,
        cost: 0,
      })
    }
  }

  // Debug log for today's slots
  if (isToday(date)) {
    // console.log(
    //   `Generated ${slots.length} slots for today. Available slots:`,
    //   slots.filter((s) => !s.disabled).map((s) => s.label),
    // )
  }

  return slots
}

// Check if date is valid for pickup/dropoff (not in the past)
export const isDateValid = (date: Date): boolean => {
  if (!date || !isValid(date)) return false
  const today = startOfDay(new Date())
  return !isBefore(date, today)
}

// Check if dropoff date is valid (not before pickup date)
export const isDropoffDateValid = (
  date: Date,
  pickupDate: Date | null,
): boolean => {
  if (!isDateValid(date)) return false
  if (!pickupDate || !isValid(pickupDate)) return true
  return isAfter(date, pickupDate) || date.getTime() === pickupDate.getTime()
}

// Parse time string and return hours/minutes - example: parseTime("16:30") returns {hours: 16, minutes: 30}
export const parseTime = (
  timeString: string,
): { hours: number; minutes: number } | null => {
  try {
    const [hours, minutes] = timeString.split(":").map(Number)
    if (isNaN(hours) || isNaN(minutes)) return null
    return { hours, minutes }
  } catch {
    return null
  }
}

// Format time string to display format - example: formatTimeDisplay("16:30") returns "4:30 PM"
export const formatTimeDisplay = (timeString: string): string => {
  const parsed = parseTime(timeString)
  if (!parsed) return timeString

  try {
    const timeDate = setMinutes(
      setHours(new Date(), parsed.hours),
      parsed.minutes,
    )
    return format(timeDate, "h:mm aa")
  } catch {
    return timeString
  }
}

//  Validate if timestamp falls within business hours

export const isWithinBusinessHours = (timestamp: number): boolean => {
  const date = new Date(timestamp * 1000)
  const hour = date.getHours()
  return hour >= BUSINESS_HOURS.START_HOUR && hour <= BUSINESS_HOURS.END_HOUR
}

//  Validate rental duration and business rules

export const validateRentalPeriod = (
  pickupTimestamp: number,
  dropoffTimestamp: number,
): { isValid: boolean; errors: string[] } => {
  const errors: string[] = []

  // Check if both timestamps are within business hours
  if (!isWithinBusinessHours(pickupTimestamp)) {
    errors.push(
      `Pickup time must be between ${BUSINESS_HOURS.START_HOUR}:00 AM and ${BUSINESS_HOURS.END_HOUR}:00 PM`,
    )
  }

  if (!isWithinBusinessHours(dropoffTimestamp)) {
    errors.push(
      `Dropoff time must be between ${BUSINESS_HOURS.START_HOUR}:00 AM and ${BUSINESS_HOURS.END_HOUR}:00 PM`,
    )
  }

  // Check duration constraints
  const durationHours = (dropoffTimestamp - pickupTimestamp) / 3600

  if (durationHours < BUSINESS_HOURS.MIN_RENTAL_HOURS) {
    errors.push(
      `Minimum rental duration is ${BUSINESS_HOURS.MIN_RENTAL_HOURS} hour(s)`,
    )
  }

  if (durationHours > BUSINESS_HOURS.MAX_RENTAL_DAYS * 24) {
    errors.push(
      `Maximum rental duration is ${BUSINESS_HOURS.MAX_RENTAL_DAYS} days`,
    )
  }

  // Check if pickup is before dropoff
  if (pickupTimestamp >= dropoffTimestamp) {
    errors.push("Pickup time must be before dropoff time")
  }

  return {
    isValid: errors.length === 0,
    errors,
  }
}

//  Validate if rental selection is complete and ready for checkout

export const validateRentalForCheckout = (
  pickupDate: Date | null,
  dropoffDate: Date | null,
  pickupTime: string | null,
  dropoffTime: string | null,
): { canProceed: boolean; errors: string[] } => {
  const errors: string[] = []

  if (!pickupDate) errors.push("Please select pickup date")
  if (!dropoffDate) errors.push("Please select dropoff date")
  if (!pickupTime) errors.push("Please select pickup time")
  if (!dropoffTime) errors.push("Please select dropoff time")

  // If basic fields are missing, return early
  if (errors.length > 0) {
    return { canProceed: false, errors }
  }

  // Validate dates
  if (!isValid(pickupDate!) || !isValid(dropoffDate!)) {
    errors.push("Invalid date selection")
  }

  if (!isDateValid(pickupDate!)) {
    errors.push("Pickup date cannot be in the past")
  }

  if (!isDropoffDateValid(dropoffDate!, pickupDate!)) {
    errors.push("Dropoff date must be after pickup date")
  }

  // Create timestamps and validate rental period using business rules
  try {
    const pickupTimestamp = createTimestampFromDateAndTime(
      pickupDate!,
      pickupTime!,
    )
    const dropoffTimestamp = createTimestampFromDateAndTime(
      dropoffDate!,
      dropoffTime!,
    )

    if (pickupTimestamp && dropoffTimestamp) {
      const rentalValidation = validateRentalPeriod(
        pickupTimestamp,
        dropoffTimestamp,
      )
      if (!rentalValidation.isValid) {
        errors.push(...rentalValidation.errors)
      }
    }
  } catch (error) {
    errors.push("Failed to validate rental period")
  }

  return {
    canProceed: errors.length === 0,
    errors,
  }
}

// Helper function to create timestamp from date and time
const createTimestampFromDateAndTime = (
  date: Date,
  time: string,
): number | null => {
  try {
    const parsed = parseTime(time)
    if (!parsed) return null

    const timestamp = new Date(date)
    timestamp.setHours(parsed.hours, parsed.minutes, 0, 0)

    // Return timestamp in seconds for consistency with validateRentalPeriod
    return Math.floor(timestamp.getTime() / 1000)
  } catch (error) {
    return null
  }
}

//  Check if a date should be disabled in calendar

export const shouldDisableDate = (
  date: Date,
  type: "pickup" | "dropoff",
  referenceDate?: Date | null,
): boolean => {
  // Disable past dates
  if (!isDateValid(date)) return true

  // // For pickup dates, check if there are any available time slots for today
  // if (type === "pickup" && isToday(date)) {
  //   const timeSlots = generateTimeSlots(date)
  //   const hasAvailableSlots = timeSlots.some((slot) => !slot.disabled)

  //   // If no time slots are available for today, disable the date
  //   if (!hasAvailableSlots) return true
  // }

  // For pickup dates, check if there are any available time slots for today
  if (type === "pickup" && isToday(date)) {
    const timeSlots = generateTimeSlots(date)
    const hasAvailableSlots = timeSlots.some((slot) => !slot.disabled)

    // If no time slots are available for today, disable the date
    if (!hasAvailableSlots) return true
  }

  // For dropoff dates, check against pickup date
  if (type === "dropoff" && referenceDate) {
    if (!isDropoffDateValid(date, referenceDate)) return true

    // For dropoff on the same day as pickup, disable if past business hours
    if (isToday(date) && isToday(referenceDate)) {
      const now = new Date()
      const currentHour = now.getHours()

      // Disable dropoff for today if it's past business hours
      if (currentHour >= BUSINESS_HOURS.END_HOUR) return true
    }

    // Disable dates beyond max rental period
    const daysDiff = Math.ceil(
      (date.getTime() - referenceDate.getTime()) / (1000 * 60 * 60 * 24),
    )
    if (daysDiff > BUSINESS_HOURS.MAX_RENTAL_DAYS) return true
  }

  // For pickup dates, disable dates beyond 90 days in future (booking window)
  if (type === "pickup") {
    const today = startOfDay(new Date())
    const maxBookingDate = new Date(today.getTime() + 90 * 24 * 60 * 60 * 1000)
    if (isAfter(date, maxBookingDate)) return true
  }

  return false
}

//  Check if one time is before another (same day comparison)

export const isTimeBefore = (time1: string, time2: string): boolean => {
  const parsed1 = parseTime(time1)
  const parsed2 = parseTime(time2)

  if (!parsed1 || !parsed2) return false

  if (parsed1.hours !== parsed2.hours) {
    return parsed1.hours < parsed2.hours
  }
  return parsed1.minutes < parsed2.minutes
}

//  Check if one time is after another (same day comparison)

export const isTimeAfter = (time1: string, time2: string): boolean => {
  const parsed1 = parseTime(time1)
  const parsed2 = parseTime(time2)

  if (!parsed1 || !parsed2) return false

  if (parsed1.hours !== parsed2.hours) {
    return parsed1.hours > parsed2.hours
  }
  return parsed1.minutes > parsed2.minutes
}

//  Check if two times are equal

export const isTimeEqual = (time1: string, time2: string): boolean => {
  const parsed1 = parseTime(time1)
  const parsed2 = parseTime(time2)

  if (!parsed1 || !parsed2) return false

  return parsed1.hours === parsed2.hours && parsed1.minutes === parsed2.minutes
}

//  Filter time slots based on pickup/dropoff constraints for same day rentals

export const filterTimeSlotsForSameDay = (
  slots: TimeSlot[],
  type: "pickup" | "dropoff",
  pickupTime?: string | null,
  dropoffTime?: string | null,
): TimeSlot[] => {
  if (type === "dropoff" && pickupTime) {
    // For dropoff on same day, ONLY enable times AFTER pickup time
    return slots.map((slot) => ({
      ...slot,
      disabled: slot.disabled || !isTimeAfter(slot.value, pickupTime),
    }))
  } else if (type === "pickup" && dropoffTime) {
    // For pickup on same day, ONLY enable times BEFORE dropoff time
    return slots.map((slot) => ({
      ...slot,
      disabled: slot.disabled || !isTimeBefore(slot.value, dropoffTime),
    }))
  }

  return slots
}

//  Get filtered time slots for a specific date and type

export const getFilteredTimeSlots = (
  date: Date | null,
  type: "pickup" | "dropoff",
  pickupDate: Date | null,
  dropoffDate: Date | null,
  pickupTime: string | null,
  dropoffTime: string | null,
): TimeSlot[] => {
  if (!date || !isValid(date)) {
    // console.log(`No valid date provided for ${type}:`, date)
    return []
  }

  const baseSlots = generateTimeSlots(date)
  // console.log(
  //   `Generated ${baseSlots.length} base slots for ${type} on ${format(date, "yyyy-MM-dd")}`,
  // )

  // Handle same-day pickup/dropoff conflicts
  if (pickupDate && dropoffDate && isSameDay(pickupDate, dropoffDate)) {
    const filteredSlots = filterTimeSlotsForSameDay(
      baseSlots,
      type,
      pickupTime,
      dropoffTime,
    )
    // console.log(
    //   `Same day rental - filtered to ${filteredSlots.filter((s) => !s.disabled).length} available slots for ${type}`,
    // )
    return filteredSlots
  }

  // console.log(
  //   `Different day rental - returning all ${baseSlots.filter((s) => !s.disabled).length} available slots for ${type}`,
  // )
  return baseSlots
}
