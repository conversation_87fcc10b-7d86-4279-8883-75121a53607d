import { isValid } from "date-fns"
import { validateRentalPeriod } from "./bike-rental-utils"

// Indian timezone constant
const INDIAN_TIMEZONE = "Asia/Kolkata"

// Convert seconds to milliseconds - example: toMilliseconds(1640995200) returns 1640995200000
export const toMilliseconds = (seconds: number): number => seconds * 1000

// Convert milliseconds to seconds - example: toSeconds(1640995200000) returns 1640995200
export const toSeconds = (milliseconds: number): number =>
  Math.floor(milliseconds / 1000)

// Create Date from timestamp seconds - example: createDateFromSeconds(1640995200) returns Date object
const createDateFromSeconds = (timestampSeconds: number): Date => {
  return new Date(toMilliseconds(timestampSeconds))
}

// Get current timestamp in seconds for Indian timezone
const getCurrentIndianTimestamp = (): number => {
  const now = new Date()
  return toSeconds(now.getTime())
}

// Format time from Date to HH:mm - example: formatTimeString(new Date()) returns "16:30"
const formatTimeString = (date: Date): string => {
  const hours = date.getHours()
  const minutes = date.getMinutes()
  return `${String(hours).padStart(2, "0")}:${String(minutes).padStart(2, "0")}`
}

export interface BikeRentalUrlParams {
  pickup: string // Unix timestamp as string
  dropoff: string // Unix timestamp as string
  bike_code?: string
  hub_code?: string
}

export interface BikeRentalSearchParams {
  pickup_timestamp: number
  dropoff_timestamp: number
  pickup_date: Date
  dropoff_date: Date
  pickup_time: string
  dropoff_time: string
}

// Create bike search URL with timestamps - example: createBikeRentalSearchUrl("bangalore", 1640995200, 1641081600)
export const createBikeRentalSearchUrl = (
  city: string,
  pickup_timestamp: number,
  dropoff_timestamp: number,
  additionalParams?: Record<string, string>,
): string => {
  const params = new URLSearchParams({
    pickup: pickup_timestamp.toString(),
    dropoff: dropoff_timestamp.toString(),
    ...additionalParams,
  })

  return `/${city}/search?${params.toString()}`
}

// Create bike checkout URL with rental details - example: createBikeRentalCheckoutUrl(1640995200, 1641081600, "BIKE001", "HUB001")
export const createBikeRentalCheckoutUrl = (
  pickup_timestamp: number,
  dropoff_timestamp: number,
  bike_code: string,
  hub_code: string,
  additionalParams?: Record<string, string>,
): string => {
  const params = new URLSearchParams({
    pickup: pickup_timestamp.toString(),
    dropoff: dropoff_timestamp.toString(),
    bike_code,
    hub_code,
    ...additionalParams,
  })

  return `/checkout?${params.toString()}`
}

// Parse bike rental URL parameters and extract normalized data with detailed logging
export const parseBikeRentalUrlParams = (
  searchParams: URLSearchParams,
): { isValid: boolean; data?: BikeRentalSearchParams; errors: string[] } => {
  // console.log("🔍 [parseBikeRentalUrlParams] Starting URL parameter parsing")
  // console.log(
  //   "📋 [parseBikeRentalUrlParams] Raw URL params:",
  //   Object.fromEntries(searchParams.entries()),
  // )

  const errors: string[] = []

  const pickupParam = searchParams.get("pickup")
  const dropoffParam = searchParams.get("dropoff")

  // console.log("📅 [parseBikeRentalUrlParams] Extracted params:", {
  //   pickupParam,
  //   dropoffParam,
  // })

  if (!pickupParam) {
    // console.error("❌ [parseBikeRentalUrlParams] Missing pickup timestamp")
    errors.push("Missing pickup timestamp")
  }

  if (!dropoffParam) {
    // console.error("❌ [parseBikeRentalUrlParams] Missing dropoff timestamp")
    errors.push("Missing dropoff timestamp")
  }

  if (errors.length > 0) {
    // console.error(
    //   "❌ [parseBikeRentalUrlParams] Early validation failed:",
    //   errors,
    // )
    return { isValid: false, errors }
  }

  // Parse and normalize timestamps from URL
  const rawPickupTimestamp = parseInt(pickupParam!, 10)
  const rawDropoffTimestamp = parseInt(dropoffParam!, 10)

  // Validate raw timestamp values
  if (isNaN(rawPickupTimestamp) || rawPickupTimestamp <= 0) {
    console.error(
      "❌ [parseBikeRentalUrlParams] Invalid pickup timestamp:",
      rawPickupTimestamp,
    )
    errors.push("Invalid pickup timestamp")
  }

  if (isNaN(rawDropoffTimestamp) || rawDropoffTimestamp <= 0) {
    console.error(
      "❌ [parseBikeRentalUrlParams] Invalid dropoff timestamp:",
      rawDropoffTimestamp,
    )
    errors.push("Invalid dropoff timestamp")
  }

  if (rawPickupTimestamp >= rawDropoffTimestamp) {
    console.error(
      "❌ [parseBikeRentalUrlParams] Pickup time not before dropoff time:",
      { rawPickupTimestamp, rawDropoffTimestamp },
    )
    errors.push("Pickup time must be before dropoff time")
  }

  // Check if timestamps are in the future using Indian timezone
  const now = getCurrentIndianTimestamp()

  if (rawPickupTimestamp < now) {
    console.error("❌ [parseBikeRentalUrlParams] Pickup time is in the past")
    errors.push("Pickup time must be in the future")
  }

  if (errors.length > 0) {
    console.error("❌ [parseBikeRentalUrlParams] Validation failed:", errors)
    return { isValid: false, errors }
  }

  // Create Date objects from timestamp seconds
  const pickup_date = new Date(toMilliseconds(rawPickupTimestamp))
  const dropoff_date = new Date(toMilliseconds(rawDropoffTimestamp))

  if (!isValid(pickup_date) || !isValid(dropoff_date)) {
    console.error(
      "❌ [parseBikeRentalUrlParams] Invalid date conversion from timestamps",
    )
    errors.push("Invalid date conversion from timestamps")
    return { isValid: false, errors }
  }

  // Extract time strings in HH:mm format using our utility function
  const pickup_time = formatTimeString(pickup_date)
  const dropoff_time = formatTimeString(dropoff_date)

  // Use original timestamps (already in seconds) for consistency
  const pickup_timestamp = rawPickupTimestamp
  const dropoff_timestamp = rawDropoffTimestamp

  const result = {
    pickup_timestamp,
    dropoff_timestamp,
    pickup_date,
    dropoff_date,
    pickup_time,
    dropoff_time,
  }

  return {
    isValid: true,
    data: result,
    errors: [],
  }
}

// Parse checkout URL parameters for bike rental with validation
export const parseCheckoutUrlParams = (
  searchParams: URLSearchParams,
): {
  isValid: boolean
  data?: BikeRentalSearchParams & {
    bike_code: string
    hub_code: string
  }
  errors: string[]
} => {
  const errors: string[] = []

  // Parse basic bike rental parameters first
  const rentalParseResult = parseBikeRentalUrlParams(searchParams)

  if (!rentalParseResult.isValid) {
    return { isValid: false, errors: rentalParseResult.errors }
  }

  // Parse bike-specific checkout parameters
  const bike_code = searchParams.get("bike_code")
  const hub_code = searchParams.get("hub_code")

  if (!bike_code) {
    errors.push("Missing bike_code parameter")
  }

  if (!hub_code) {
    errors.push("Missing hub_code parameter")
  }

  if (errors.length > 0) {
    return { isValid: false, errors }
  }

  return {
    isValid: true,
    data: {
      ...rentalParseResult.data!,
      bike_code: bike_code!,
      hub_code: hub_code!,
    },
    errors: [],
  }
}

// Validate if bike rental timestamps are still valid (not expired)
export const validateUrlParamsTimestamps = (
  pickup_timestamp: number,
  dropoff_timestamp: number,
): { isValid: boolean; errors: string[] } => {
  const errors: string[] = []
  const now = Math.floor(Date.now() / 1000) // Current time in seconds

  // Check if timestamps have expired
  if (pickup_timestamp < now) {
    errors.push("Pickup time has passed")
  }

  if (dropoff_timestamp < now) {
    errors.push("Dropoff time has passed")
  }

  // Use comprehensive rental period validation for business rules

  const rentalValidation = validateRentalPeriod(
    pickup_timestamp,
    dropoff_timestamp,
  )

  if (!rentalValidation.isValid) {
    errors.push(...rentalValidation.errors)
  }

  return {
    isValid: errors.length === 0,
    errors,
  }
}
