import { DEFAULT_CITY } from "@/constants"
import { capitalizeFirstLetter } from "@/functions/small-functions"
import { Metadata, ResolvingMetadata } from "next"

interface GenerateCityMetadataProps {
  city?: string
  parent: ResolvingMetadata
}

export async function generateCityMetadata({
  city = DEFAULT_CITY,
  parent,
}: GenerateCityMetadataProps): Promise<Metadata> {
  const previousImages = (await parent).openGraph?.images || []
  const formattedCity = city ? capitalizeFirstLetter(city) : "Bangalore"

  const title = `Rent Bikes in ${formattedCity} | Adventure Bike Rentals near me | Zero Deposit Bike Rentals`
  const description = `Rent top-quality adventure bikes for your next road trip or weekend getaway. Book online for flexible hourly or daily plans. Bike Rentals available in ${formattedCity}.`

  return {
    title,
    description,
    openGraph: {
      title,
      description,
      images: [
        "https://images.sharepal.in/misc/hard-coded/sharepal/sharepal-logo-new.jpg",
        ...previousImages,
      ],
    },
    keywords: [
      `Bike Rentals in ${formattedCity}`,
      `Adventure Bike Rentals in ${formattedCity}`,
      `Premium Bike Hire in ${formattedCity}`,
      `Motorcycle Rentals in ${formattedCity}`,
      `Rent Bikes ${formattedCity}`,
      `Self-drive Bike Rentals in ${formattedCity}`,
      `Rent Himalayan in ${formattedCity}`,
      `Rent Royal Enfield in ${formattedCity}`,
      `Bike rentals near me in ${formattedCity}`,
    ],
  }
}
