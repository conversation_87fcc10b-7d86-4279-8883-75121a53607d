import { City } from "@/types/common"
import { create } from "zustand"
import { persist } from "zustand/middleware"

interface CityState {
  selectedCity: City
  city_surge: number
  cityMinthMap: Record<string, number>
  setSelectedCity: (selectedCity: City) => void
  setCitySurge: (city_surge: number) => void
  // setCityMinthMap: (data: Record<string, number>) => void
}

export const useCityStore = create<CityState>()(
  persist(
    (set) => ({
      selectedCity: {
        id: 1,
        city_name: "Bangalore",
        city_url: "bangalore",
        sequence: 1,
      },
      city_surge: 1,
      cityMinthMap: {},
      setSelectedCity: (selectedCity: City) => set({ selectedCity }),
      setCitySurge: (city_surge: number) => set({ city_surge }),
      // setCityMinthMap: (data: Record<string, number>) =>
      //   set({ cityMinthMap: data }),
    }),
    {
      name: "city-storage",
      version: 1,
    },
  ),
)
