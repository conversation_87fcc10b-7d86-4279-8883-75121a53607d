import { roundValue } from "@/functions/business-logics"
import { validateRentalPeriod } from "@/lib/bike-rental-utils"
import type {
  CheckoutSection,
  ContactDetails,
  PaymentOption,
} from "@/types/checkout"
import { Bike } from "@/types/common"
import { OrderData } from "@/types/order"
import { create } from "zustand"
import { devtools } from "zustand/middleware"

export const DEFAULT_GST_RATE = 28 // 28% GST for bike rentals
export const DEFAULT_PICKUP_LOCATION = "Same as pickup"
export const DEFAULT_DROPOFF_LOCATION = "Same as pickup"
export const DEFAULT_EXTRA_HELMET_FEE = 99

export const DEFAULT_PAYMENT_OPTION = {
  id: 8,
  created_at: 1753966524885,
  payment_active: true,
  payment_type: 7,
  payment_priority: 5,
  visible_text: "Pay 10% Now",
  backend_order: false,
  type: "bike",
}

/**
 * Rental timing and location parameters
 */
export interface BikeRentalParams {
  pickup_timestamp: number
  dropoff_timestamp: number
  hub_code: string
  hub_name?: string
}

export const calculateCarepalCoverage = (totalGoodsValue: number): number => {
  const coverage = Math.round(totalGoodsValue * 0.5)
  // Cap the coverage at 25000
  return Math.min(coverage, 25000)
}

export const calculateCarepalFee = (totalGoodsValue: number): number => {
  // Calculate coverage first (with cap)
  const coverage = calculateCarepalCoverage(totalGoodsValue)
  // Fee is 1% of coverage amount
  return Math.round(coverage * 0.01)
}

export interface BikeRentalCheckoutParams {
  bike_code: string
  hub_code: string
  pickup_timestamp: number
  dropoff_timestamp: number
  contact_details: ContactDetails
  payment_option: number
  total_amount: number
  total_rent: number
  deposit_amount: number
  gst_amount: number
  coupon_code?: string
  coupon_discount?: number
  wallet_balance_used?: number
  seon_fingerprint: string
  carepal_selected?: boolean
  carepal_coverage?: number
  carepal_fee?: number
  extra_helmet_selected?: boolean
}

export const calculateBikeRentalAmount = (
  hourly_rate: number,
  duration_hours: number,
): number => roundValue(hourly_rate * duration_hours)

export const calculateGSTAmount = (
  rent: number,
  gst_rate: number = DEFAULT_GST_RATE,
): number => {
  if (rent < 0) {
    console.warn("Negative rent amount provided for GST calculation:", rent)
    return 0
  }
  return roundValue(rent * (gst_rate / 100))
}

/**
 * Calculate rental duration in hours from timestamps
 */
export const calculateRentalDuration = (
  pickup_timestamp: number,
  dropoff_timestamp: number,
): number => {
  if (dropoff_timestamp <= pickup_timestamp) {
    throw new Error("Dropoff time must be after pickup time")
  }
  return (dropoff_timestamp - pickup_timestamp) / 3600
}

/**
 * Validates bike rental initialization parameters
 */
export const validateBikeRentalParams = (
  bike: Bike,
  rentalParams: BikeRentalParams,
): { isValid: boolean; errors: string[] } => {
  const errors: string[] = []

  // Validate bike object
  if (!bike) {
    errors.push("Bike data is required")
  } else {
    if (!bike.bike_code) errors.push("Bike code is missing")
    if (!bike.name) errors.push("Bike name is missing")
    if (bike.price_per_hour <= 0) {
      errors.push("Invalid hourly rate")
    }
    if (bike.goods_value < 0) {
      errors.push("Invalid goods value")
    }
  }

  // Validate timestamps
  if (!rentalParams.pickup_timestamp || rentalParams.pickup_timestamp <= 0) {
    errors.push("Valid pickup timestamp is required")
  }
  if (!rentalParams.dropoff_timestamp || rentalParams.dropoff_timestamp <= 0) {
    errors.push("Valid dropoff timestamp is required")
  }
  if (
    rentalParams.pickup_timestamp &&
    rentalParams.dropoff_timestamp &&
    rentalParams.dropoff_timestamp <= rentalParams.pickup_timestamp
  ) {
    errors.push("Dropoff time must be after pickup time")
  }

  // Validate hub
  if (!rentalParams.hub_code) {
    errors.push("Hub ID is required")
  }

  return {
    isValid: errors.length === 0,
    errors,
  }
}

interface BikeRentalCheckoutState {
  // Section navigation
  active_section: CheckoutSection
  seon_fingerprint: string

  // Section completion flags
  is_contact_completed: boolean
  is_review_completed: boolean
  is_carepal_completed: boolean

  // Customer details
  contact_details: ContactDetails | null

  // Payment details
  payment_option: PaymentOption | null
  payment_type: number

  // Bike rental specific data
  selected_bike: Bike | null
  rental_params: BikeRentalParams | null
  pickup_location: string
  dropoff_location: string

  // Calculated values
  total_hours: number
  total_rent: number
  deposit_amount: number
  gst_amount: number
  gst_rate: number
  carepal_fee: number
  carepal_coverage: number
  handling_charges: number

  // Discounts and wallet
  applied_coupon_code: string
  coupon_discount: number
  wallet_balance: number
  wallet_used: boolean
  wallet_balance_used: number

  // Final amounts
  total_amount: number // Before discounts and wallet
  final_amount: number // After all adjustments
  total_goods_value: number

  // Additional options
  gst_claimed: boolean
  gst_number: string
  payment_mode: "full" | "partial"

  checkout_redirect_loading: boolean
  orderData: OrderData
  carepal_selected: boolean
  extra_helmet_selected: boolean

  // Methods
  setOrderData: (order_data: OrderData) => void
  calculateFinalAmount: () => number
  recalculateAmounts: () => void
  validateCheckoutState: () => { isValid: boolean; errors: string[] }
  canProceedToPayment: () => boolean
  setSeonFingerprint: (fingerprint: string) => void
  setActiveSection: (section: CheckoutSection) => void
  setStepCompleted: (step: CheckoutSection, completed: boolean) => void
  goToNextSection: () => void
  goToPrevSection: () => void

  setContactDetails: (details: ContactDetails) => void
  setPaymentType: (type: number) => void
  setPaymentOption: (paymentOption: PaymentOption) => void

  // Bike rental specific methods
  initializeBikeRental: (bike: Bike, rentalParams: BikeRentalParams) => void
  setPickupLocation: (location: string) => void
  setDropoffLocation: (location: string) => void

  // Charges and discounts
  setHandlingCharges: (charges: number) => void
  setAppliedCouponCode: (code: string, discount: number) => void
  setWalletBalance: (balance: number) => void
  setCouponDiscount: (discount: number) => void
  setUseWallet: (use: boolean) => void
  setWalletBalanceUsed: (amount: number) => void
  setUseGST: (use: boolean) => void
  setGSTNumber: (number: string) => void
  setPaymentMode: (mode: "full" | "partial") => void
  applyGst: (apply: boolean, gstRate?: number) => void
  setCarepalSelected: (selected: boolean) => void
  setExtraHelmetSelected: (selected: boolean) => void

  setCheckoutRedirecting: (loading: boolean) => void
  resetCheckout: () => void
}

export const useCheckoutStore = create<BikeRentalCheckoutState>()(
  devtools((set, get) => ({
    // Initial State
    active_section: "contact",
    seon_fingerprint: "",
    is_contact_completed: false,
    is_review_completed: false,
    is_carepal_completed: false,

    contact_details: null,
    payment_type: DEFAULT_PAYMENT_OPTION.payment_type,
    payment_option: DEFAULT_PAYMENT_OPTION,
    checkout_redirect_loading: false,

    // Bike rental specific
    selected_bike: null,
    rental_params: null,
    pickup_location: DEFAULT_PICKUP_LOCATION,
    dropoff_location: DEFAULT_DROPOFF_LOCATION,

    // Calculated values
    total_hours: 0,
    total_rent: 0,
    deposit_amount: 0,
    gst_amount: 0,
    gst_rate: DEFAULT_GST_RATE,
    carepal_fee: 0,
    carepal_coverage: 0,
    handling_charges: 0,

    // Discounts and wallet
    applied_coupon_code: "",
    coupon_discount: 0,
    wallet_balance: 0,
    wallet_used: false,
    wallet_balance_used: 0,

    // Final amounts
    total_amount: 0,
    final_amount: 0,
    total_goods_value: 0,

    // Additional options
    gst_claimed: false,
    gst_number: "",
    payment_mode: "full",
    carepal_selected: false,
    extra_helmet_selected: false,

    orderData: {},
    setOrderData: (order_data: OrderData) => set({ orderData: order_data }),

    calculateFinalAmount: () => {
      const {
        total_rent,
        deposit_amount,
        gst_amount,
        handling_charges,
        coupon_discount,
        wallet_balance_used,
        carepal_fee,
        carepal_selected,
        extra_helmet_selected,
      } = get()

      return roundValue(
        total_rent +
          deposit_amount +
          gst_amount +
          (carepal_selected ? carepal_fee : 0) +
          (extra_helmet_selected ? DEFAULT_EXTRA_HELMET_FEE : 0) +
          handling_charges -
          coupon_discount -
          wallet_balance_used,
      )
    },

    /**
     * Recalculate all amounts and update final amount
     * This is a centralized method to avoid calculation duplication
     */
    recalculateAmounts: () => {
      set((state) => {
        if (!state.selected_bike || !state.rental_params) return state

        // Calculate total hours and rent
        const total_hours = calculateRentalDuration(
          state.rental_params.pickup_timestamp,
          state.rental_params.dropoff_timestamp,
        )
        const total_rent = calculateBikeRentalAmount(
          state.selected_bike.price_per_hour,
          total_hours,
        )

        const gst_amount = calculateGSTAmount(total_rent, state.gst_rate)
        const carepal_coverage = calculateCarepalCoverage(
          state.selected_bike.goods_value,
        )
        const carepal_fee = calculateCarepalFee(state.selected_bike.goods_value)

        const final_amount = roundValue(
          total_rent +
            state.deposit_amount +
            gst_amount +
            (state.carepal_selected ? carepal_fee : 0) +
            (state.extra_helmet_selected ? DEFAULT_EXTRA_HELMET_FEE : 0) +
            state.handling_charges -
            state.coupon_discount -
            state.wallet_balance_used,
        )

        return {
          total_hours,
          total_rent,
          gst_amount,
          carepal_coverage,
          carepal_fee,
          total_goods_value: state.selected_bike.goods_value,
          final_amount,
        }
      })
    },

    /**
     * Validate the current checkout state
     */
    validateCheckoutState: () => {
      const state = get()
      const errors: string[] = []

      // Validate bike selection and rental params
      if (!state.selected_bike) {
        errors.push("No bike selected for rental")
      } else {
        if (!state.selected_bike.bike_code) {
          errors.push("Invalid bike selection")
        }
      }

      if (!state.rental_params) {
        errors.push("Rental parameters are missing")
      } else {
        if (!state.rental_params.hub_code) {
          errors.push("No pickup location selected")
        }
        if (state.rental_params.pickup_timestamp <= Date.now() / 1000) {
          errors.push("Pickup time must be in the future")
        }

        // Use comprehensive rental period validation
        const rentalValidation = validateRentalPeriod(
          state.rental_params.pickup_timestamp,
          state.rental_params.dropoff_timestamp,
        )

        if (!rentalValidation.isValid) {
          errors.push(...rentalValidation.errors)
        }
      }

      // Validate contact details
      if (!state.contact_details) {
        errors.push("Contact details are required")
      } else {
        if (!state.contact_details.first_name?.trim()) {
          errors.push("First name is required")
        }
        if (!state.contact_details.last_name?.trim()) {
          errors.push("Last name is required")
        }
        if (!state.contact_details.calling_number?.trim()) {
          errors.push("Phone number is required")
        }
        if (!state.contact_details.email?.trim()) {
          errors.push("Email is required")
        }
        if (!state.contact_details.whatsapp_number?.trim()) {
          errors.push("WhatsApp number is required")
        }
      }

      // Validate payment option
      if (!state.payment_option) {
        errors.push("Payment method is required")
      }

      // Validate amounts
      if (state.final_amount < 0) {
        errors.push("Invalid final amount")
      }

      return {
        isValid: errors.length === 0,
        errors,
      }
    },

    /**
     * Check if checkout can proceed to payment
     */
    canProceedToPayment: () => {
      const validation = get().validateCheckoutState()
      return validation.isValid
    },

    // Methods
    setSeonFingerprint: (fingerprint: string) =>
      set({ seon_fingerprint: fingerprint }),
    setActiveSection: (section: CheckoutSection) =>
      set({ active_section: section }),
    setStepCompleted: (step: CheckoutSection, completed: boolean) =>
      set(() => ({
        ...(step === "contact" && { is_contact_completed: completed }),
        ...(step === "review" && { is_review_completed: completed }),
        ...(step === "carepal" && { is_carepal_completed: completed }),
      })),
    goToNextSection: () =>
      set((state) => {
        const current = state.active_section
        if (current === "contact") return { active_section: "carepal" }
        if (current === "carepal") return { active_section: "review" }
        return state
      }),
    goToPrevSection: () =>
      set((state) => {
        const current = state.active_section
        if (current === "review") return { active_section: "carepal" }
        if (current === "carepal") return { active_section: "contact" }
        return state
      }),

    setContactDetails: (details: ContactDetails) =>
      set({ contact_details: details }),
    setPaymentType: (type: number) => set({ payment_type: type }),
    setPaymentOption: (paymentOption: PaymentOption) =>
      set({ payment_option: paymentOption }),

    // Bike rental specific methods
    /**
     * Initialize bike rental with Bike and rental parameters
     * This is the preferred method for setting up bike rental checkout
     */
    initializeBikeRental: (bike: Bike, rentalParams: BikeRentalParams) => {
      try {
        const validation = validateBikeRentalParams(bike, rentalParams)
        if (!validation.isValid) {
          throw new Error(
            `Invalid bike rental parameters: ${validation.errors.join(", ")}`,
          )
        }

        set(() => ({
          selected_bike: bike,
          rental_params: rentalParams,
        }))

        // Use centralized recalculation
        get().recalculateAmounts()
      } catch (error) {
        console.error("Failed to initialize bike rental:", error)
        throw error
      }
    },
    setPickupLocation: (location: string) => set({ pickup_location: location }),
    setDropoffLocation: (location: string) =>
      set({ dropoff_location: location }),

    // Charges and discounts
    setHandlingCharges: (charges: number) => set({ handling_charges: charges }),
    setAppliedCouponCode: (code: string, discount: number) => {
      set((state) => {
        // Calculate final amount with the new coupon discount
        const final_amount = roundValue(
          state.total_rent +
            state.gst_amount +
            state.carepal_fee +
            state.handling_charges -
            discount - // Use the new discount value
            0, // Reset wallet usage when coupon is applied
        )

        return {
          applied_coupon_code: code,
          coupon_discount: discount,
          wallet_used: false,
          wallet_balance_used: 0,
          wallet_balance: state.wallet_balance + state.wallet_balance_used,
          final_amount,
        }
      })
    },
    setWalletBalance: (balance: number) => set({ wallet_balance: balance }),
    setCouponDiscount: (discount: number) => {
      set((state) => {
        // Calculate final amount with the new coupon discount
        const final_amount = roundValue(
          state.total_rent +
            state.gst_amount +
            state.carepal_fee +
            state.handling_charges -
            discount - // Use the new discount value
            state.wallet_balance_used,
        )

        return {
          coupon_discount: discount,
          final_amount,
        }
      })
    },

    // Wallet methods
    setUseWallet: (use: boolean) =>
      set((state) => {
        if (use) {
          // Calculate maximum wallet usage (can't exceed rent amount after coupon discount)
          const wallet_balance_used = Math.min(
            state.wallet_balance ?? 0,
            Math.max(0, state.total_rent - state.coupon_discount),
          )

          // Calculate final amount with wallet usage
          const final_amount = roundValue(
            state.total_rent +
              state.deposit_amount +
              state.gst_amount +
              state.carepal_fee +
              state.handling_charges -
              state.coupon_discount -
              wallet_balance_used,
          )

          return {
            wallet_used: true,
            wallet_balance_used,
            wallet_balance: state.wallet_balance - wallet_balance_used,
            final_amount,
          }
        } else {
          // Reset wallet usage
          const final_amount = roundValue(
            state.total_rent +
              state.deposit_amount +
              state.gst_amount +
              state.carepal_fee +
              state.handling_charges -
              state.coupon_discount -
              0, // No wallet usage
          )

          return {
            wallet_used: false,
            wallet_balance_used: 0,
            wallet_balance: state.wallet_balance + state.wallet_balance_used,
            final_amount,
          }
        }
      }),
    setWalletBalanceUsed: (amount: number) => {
      set((state) => {
        // Calculate final amount with the new wallet balance used
        const final_amount = roundValue(
          state.total_rent +
            state.deposit_amount +
            state.gst_amount +
            state.carepal_fee +
            state.handling_charges -
            state.coupon_discount -
            amount, // Use the new wallet amount
        )

        return {
          wallet_balance_used: amount,
          final_amount,
        }
      })
    },

    // GST methods
    setUseGST: (use: boolean) => {
      set((state) => {
        // Calculate final amount (GST claiming doesn't affect the amount calculation)
        const final_amount = roundValue(
          state.total_rent +
            state.deposit_amount +
            state.gst_amount +
            state.carepal_fee +
            state.handling_charges -
            state.coupon_discount -
            state.wallet_balance_used,
        )

        return {
          gst_claimed: use,
          final_amount,
        }
      })
    },
    setGSTNumber: (number: string) => set({ gst_number: number }),
    setPaymentMode: (mode: "full" | "partial") => set({ payment_mode: mode }),
    setCarepalSelected: (selected) => {
      set({ carepal_selected: selected })
      get().recalculateAmounts()
    },
    setExtraHelmetSelected: (selected) => {
      set({ extra_helmet_selected: selected })
      get().recalculateAmounts()
    },

    applyGst: (apply: boolean, gstRate: number = DEFAULT_GST_RATE) => {
      set({
        gst_claimed: apply,
        gst_rate: gstRate,
      })
      get().recalculateAmounts()
    },

    setCheckoutRedirecting: (loading: boolean) =>
      set({ checkout_redirect_loading: loading }),

    resetCheckout: () => {
      set({
        active_section: "contact",
        is_contact_completed: false,
        is_review_completed: false,
        contact_details: null,
        payment_type: DEFAULT_PAYMENT_OPTION.payment_type,
        payment_option: DEFAULT_PAYMENT_OPTION,
        selected_bike: null,
        rental_params: null,
        pickup_location: DEFAULT_PICKUP_LOCATION,
        dropoff_location: DEFAULT_DROPOFF_LOCATION,
        total_hours: 0,
        total_rent: 0,
        deposit_amount: 0,
        gst_amount: 0,
        gst_rate: DEFAULT_GST_RATE,
        handling_charges: 0,
        applied_coupon_code: "",
        coupon_discount: 0,
        wallet_balance: 0,
        wallet_used: false,
        wallet_balance_used: 0,
        total_amount: 0,
        final_amount: 0,
        total_goods_value: 0,
        gst_claimed: false,
        gst_number: "",
        payment_mode: "full",
        checkout_redirect_loading: false,
        orderData: {} as OrderData,
        carepal_selected: false,
        carepal_fee: 0,
        carepal_coverage: 0,
        extra_helmet_selected: false,
      })
    },
  })),
)
