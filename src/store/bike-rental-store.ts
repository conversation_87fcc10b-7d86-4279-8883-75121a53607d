import { toSeconds } from "@/lib/bike-rental-url-utils"
import { validateRentalPeriod } from "@/lib/bike-rental-utils"
import { create } from "zustand"

interface BikeRentalState {
  pickup_date: Date | null
  dropoff_date: Date | null
  pickup_time: string | null
  dropoff_time: string | null
  pickup_timestamp: number | null
  dropoff_timestamp: number | null
  total_hours: number | null
  surge_factor: number
  same_day_surge: number
  same_day_surge_loading: boolean

  // Bike rental selector dialog state
  isSelectorOpen: boolean

  setPickupDate: (date: Date | null) => void
  setDropoffDate: (date: Date | null) => void
  setPickupTime: (time: string | null) => void
  setDropoffTime: (time: string | null) => void
  setSurgeFactor: (surge_factor: number) => void
  setSameDaySurge: (same_day_surge: number) => void
  setSameDaySurgeLoading: (same_day_surge_loading: boolean) => void

  setFromUrlParams: (data: {
    pickup_date: Date
    dropoff_date: Date
    pickup_time: string
    dropoff_time: string
    pickup_timestamp: number
    dropoff_timestamp: number
  }) => void
  reset: () => void
  getPickupTimestamp: () => number | null
  getDropoffTimestamp: () => number | null
  validateTimestamps: () => { isValid: boolean; errors: string[] }
  getRentalDurationHours: () => number | null

  // Bike rental selector methods
  setSelectorOpen: (open: boolean) => void
  openSelector: () => void
  closeSelector: () => void
}

// Create timestamp in seconds from date and time - example: createTimestamp(new Date(), "16:00") returns Unix timestamp
const createTimestamp = (
  date: Date | null,
  time: string | null,
): number | null => {
  if (!date || !time) return null

  try {
    // Parse time string in HH:mm format
    const timeMatch = time.match(/^(\d{1,2}):(\d{2})$/)
    if (!timeMatch) {
      console.error("Invalid time format, expected HH:mm:", time)
      return null
    }

    const hours = parseInt(timeMatch[1], 10)
    const minutes = parseInt(timeMatch[2], 10)

    // Validate time values are within valid range
    if (hours < 0 || hours > 23 || minutes < 0 || minutes > 59) {
      console.error("Invalid time values:", { hours, minutes })
      return null
    }

    // Create timestamp with specified date and time in Indian timezone
    const timestamp = new Date(date)
    timestamp.setHours(hours, minutes, 0, 0)

    // Return timestamp in seconds for URL compatibility
    return toSeconds(timestamp.getTime())
  } catch (error) {
    console.error("Error creating timestamp:", error, { date, time })
    return null
  }
}

// Calculate rental duration in hours from timestamps - example: calculateTotalHours(1640995200, 1641081600) returns 24
const calculateTotalHours = (
  pickup_timestamp: number | null,
  dropoff_timestamp: number | null,
): number | null => {
  if (!pickup_timestamp || !dropoff_timestamp) return null
  return (dropoff_timestamp - pickup_timestamp) / 3600
}

export const useBikeRentalStore = create<BikeRentalState>((set, get) => ({
  pickup_date: null,
  dropoff_date: null,
  pickup_time: null,
  dropoff_time: null,
  pickup_timestamp: null,
  dropoff_timestamp: null,
  total_hours: null,
  surge_factor: 1,
  same_day_surge: 1,
  isSelectorOpen: false,
  same_day_surge_loading: false,

  setPickupDate: (date) => {
    set((state) => {
      const pickup_timestamp = createTimestamp(date, state.pickup_time)
      const total_hours = calculateTotalHours(
        pickup_timestamp,
        state.dropoff_timestamp,
      )
      return {
        pickup_date: date,
        pickup_timestamp,
        total_hours,
      }
    })
  },

  setDropoffDate: (date) => {
    set((state) => {
      const dropoff_timestamp = createTimestamp(date, state.dropoff_time)
      const total_hours = calculateTotalHours(
        state.pickup_timestamp,
        dropoff_timestamp,
      )
      return {
        dropoff_date: date,
        dropoff_timestamp,
        total_hours,
      }
    })
  },

  setPickupTime: (time) => {
    set((state) => {
      const pickup_timestamp = createTimestamp(state.pickup_date, time)
      const total_hours = calculateTotalHours(
        pickup_timestamp,
        state.dropoff_timestamp,
      )
      return {
        pickup_time: time,
        pickup_timestamp,
        total_hours,
      }
    })
  },

  setDropoffTime: (time) => {
    set((state) => {
      const dropoff_timestamp = createTimestamp(state.dropoff_date, time)
      const total_hours = calculateTotalHours(
        state.pickup_timestamp,
        dropoff_timestamp,
      )
      return {
        dropoff_time: time,
        dropoff_timestamp,
        total_hours,
      }
    })
  },

  setFromUrlParams: (data) => {
    // Use timestamps directly from URL params (already normalized in parsing)
    const pickup_timestamp = data.pickup_timestamp
    const dropoff_timestamp = data.dropoff_timestamp

    const total_hours = calculateTotalHours(pickup_timestamp, dropoff_timestamp)

    set({
      pickup_date: data.pickup_date,
      dropoff_date: data.dropoff_date,
      pickup_time: data.pickup_time,
      dropoff_time: data.dropoff_time,
      pickup_timestamp,
      dropoff_timestamp,
      total_hours,
    })
  },

  reset: () =>
    set({
      pickup_date: null,
      dropoff_date: null,
      pickup_time: null,
      dropoff_time: null,
      pickup_timestamp: null,
      dropoff_timestamp: null,
      total_hours: null,
      isSelectorOpen: false,
    }),

  // Bike rental selector dialog controls
  setSelectorOpen: (open: boolean) => set({ isSelectorOpen: open }),
  openSelector: () => set({ isSelectorOpen: true }),
  closeSelector: () => set({ isSelectorOpen: false }),

  // Get current pickup timestamp from date and time
  getPickupTimestamp: () => {
    const { pickup_date, pickup_time } = get()
    return createTimestamp(pickup_date, pickup_time)
  },

  // Get current dropoff timestamp from date and time
  getDropoffTimestamp: () => {
    const { dropoff_date, dropoff_time } = get()
    return createTimestamp(dropoff_date, dropoff_time)
  },

  // Validate bike rental timestamps and duration
  validateTimestamps: () => {
    const { pickup_timestamp, dropoff_timestamp } = get()
    const errors: string[] = []

    if (!pickup_timestamp) {
      errors.push("Pickup timestamp is required")
    }

    if (!dropoff_timestamp) {
      errors.push("Dropoff timestamp is required")
    }

    if (pickup_timestamp && dropoff_timestamp) {
      // Use the comprehensive validateRentalPeriod function
      const validation = validateRentalPeriod(
        pickup_timestamp,
        dropoff_timestamp,
      )

      if (!validation.isValid) {
        errors.push(...validation.errors)
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
    }
  },

  // Get total rental duration in hours
  getRentalDurationHours: () => {
    const { pickup_timestamp, dropoff_timestamp } = get()
    return calculateTotalHours(pickup_timestamp, dropoff_timestamp)
  },

  setSurgeFactor: (surge_factor) => set({ surge_factor }),
  setSameDaySurge: (same_day_surge) => set({ same_day_surge }),
  setSameDaySurgeLoading: (same_day_surge_loading) => {
    set({ same_day_surge_loading })
  },
}))

interface CalendarStore {
  isOpen: boolean
  openCalendar: () => void
  closeCalendar: () => void
}

// Calendar dialog state store for bike rental date picker
export const useCalendarStore = create<CalendarStore>((set) => ({
  isOpen: false,
  openCalendar: () => set({ isOpen: true }),
  closeCalendar: () => set({ isOpen: false }),
}))
