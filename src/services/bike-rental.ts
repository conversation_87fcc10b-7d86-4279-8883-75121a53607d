import { encryptData } from "@/utils/encryption"
import { fetchWithAuth } from "@/utils/fetchWithAuth"
import { handleFetchError } from "."

// New payload structure for create bike order API
export interface CreateBikeOrderPayload {
  pickup_time: Date | null
  dropoff_time: Date | null
  bike_code: string
  hub_code: string
  coupon_code: string
  total_amount: number
  gst_amount: number
  wallet_applied: boolean
  city_url: string
  wallet_balance_used: number
  payment_option: string
  coupon_discount: number
  bike_rental_charges: number
  carepal_applied: boolean
  carepal_amount: number
  total_hours: number
  extra_helmet_applied: boolean
}

/**
 * Create a bike rental order using the new API endpoint
 */
export const createBikeRentalOrder = async (order: CreateBikeOrderPayload) => {
  try {
    const data = await encryptData(JSON.stringify(order))
    const response = await fetchWithAuth(
      "https://api.sharepal.in/api:KAKxb4r4/bike/checkout/create-order",
      {
        method: "POST",
        body: JSON.stringify({
          data: data,
        }),
      },
    )
    return response
  } catch (error) {
    throw handleFetchError(
      "Failed to create Bike Rental Order",
      error instanceof Response ? error : undefined,
    )
  }
}

// New payload structure for process bike order API
export interface ProcessBikeOrderPayload {
  bike_order_id: string | number
}

/**
 * Process a bike rental order after payment using the new API endpoint
 */
export const processBikeRentalOrder = async (
  payload: ProcessBikeOrderPayload,
) => {
  try {
    const data = await encryptData(JSON.stringify(payload))
    const response = await fetchWithAuth(
      "https://api.sharepal.in/api:KAKxb4r4/bike/checkout/process-order",
      {
        method: "POST",
        body: JSON.stringify({
          data: data,
        }),
      },
    )
    return response
  } catch (error) {
    throw handleFetchError(
      "Failed to process Bike Rental Order",
      error instanceof Response ? error : undefined,
    )
  }
}
