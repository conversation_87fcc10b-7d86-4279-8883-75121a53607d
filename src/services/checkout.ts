import { CheckoutParams, ProcessOrderParams } from "@/types/checkout"
import { customFetch } from "@/utils/customFetch"
import { encryptData } from "@/utils/encryption"
import { fetchWithAuth } from "@/utils/fetchWithAuth"
import { handleFetchError } from "."
export const fetchPaymentOptions = async (backend_order?: string) => {
  try {
    const response = await customFetch(
      "https://api.sharepal.in/api:Lx-P60bj/bikes/payment-options",
      {
        method: "POST",
        body: JSON.stringify({
          backend_order: Bo<PERSON>an(backend_order),
        }),
      },
    )
    return response
  } catch (error) {
    throw handleFetchError(
      "Failed to fetch Payment Options",
      error instanceof Response ? error : undefined,
    )
  }
}

export const verifyCoupon = async ({
  applied_coupon,
  pickup_date,
  dropoff_date,
  total_hours,
  pickup_timestamp,
  dropoff_timestamp,
}: {
  applied_coupon: string
  pickup_date: Date
  dropoff_date: Date
  total_hours: number
  pickup_timestamp: number
  dropoff_timestamp: number
}) => {
  try {
    const data = await customFetch(
      "https://api.sharepal.in/api:Lx-P60bj/bikes/coupon/verify",
      {
        method: "POST",
        body: JSON.stringify({
          dicount_coupons_name: applied_coupon,
          pickup_date: pickup_date,
          dropoff_date: dropoff_date,
          total_hours: total_hours,
          coupon_type: "bikes",
          dropoff_timestamp,
          pickup_timestamp,
        }),
      },
    )
    return data
  } catch (error) {
    console.error(error)
    throw handleFetchError(
      "Failed to verify Coupon",
      error instanceof Response ? error : undefined,
    )
  }
}

export const processOrder = async (order: ProcessOrderParams) => {
  try {
    const data = await encryptData(JSON.stringify(order))
    const response = await fetchWithAuth(
      "https://api.sharepal.in/api:AIoqxnqr/checkout/process-order",
      {
        method: "POST",
        body: JSON.stringify({
          data: data,
        }),
      },
    )
    return response
  } catch (error) {
    throw handleFetchError(
      "Failed to process Order",
      error instanceof Response ? error : undefined,
    )
  }
}

export const createOrder = async (order: CheckoutParams) => {
  try {
    const data = await encryptData(JSON.stringify(order))
    const response = await fetchWithAuth(
      "https://api.sharepal.in/api:AIoqxnqr/checkout/create-order",
      {
        method: "POST",
        body: JSON.stringify({
          data: data,
        }),
      },
    )
    return response
  } catch (error) {
    throw handleFetchError(
      "Failed to create Order",
      error instanceof Response ? error : undefined,
    )
  }
}
