import {
  BikeFilterPayload,
  BikeOrderResponse,
  BikeOrderSummary,
  CancellationReason,
  CancellationReasons,
  CreateBikeOrder,
  FilterValue,
  OrderStatusResponse,
  OrderTimelineResponse,
} from "@/types/order"
import {
  fetchWithAuth,
  fetchWithAuthGet,
  fetchWithAuthPost,
} from "@/utils/fetchWithAuth"

export const fetchOrderSummary = async (
  orderId: string,
): Promise<BikeOrderSummary | null> => {
  try {
    const data = await fetchWithAuth(
      "https://api.sharepal.in/api:KAKxb4r4/bike/order-summary",
      {
        method: "POST",
        body: JSON.stringify({ order_id: orderId }),
      },
    )
    return data
  } catch (error) {
    console.error("error", error)
    return null
  }
  //   const data = await fetchWithAuth(`/order/${orderId}`)
  //   return data
}

export const fetchOrderWithoutCart = async (
  orderId: string,
): Promise<CreateBikeOrder | null> => {
  try {
    const data = await fetchWithAuth(
      `https://api.sharepal.in/api:KAKxb4r4/bike/order`,
      {
        method: "POST",
        body: JSON.stringify({ order_id: orderId }),
      },
    )
    return data
  } catch (error) {
    console.error("error", error)
    return null
  }
}

export const fetchOrderStatus = async (
  orderId: string,
): Promise<OrderStatusResponse | null> => {
  try {
    const data = await fetchWithAuth(
      `https://api.sharepal.in/api:AIoqxnqr/order/order-status`,
      {
        method: "POST",
        body: JSON.stringify({ order_id: orderId }),
      },
    )
    return data
  } catch (error) {
    console.error("error", error)
    return null
  }
}

export const fetchOrderTimeline = async (
  orderId: string,
): Promise<OrderTimelineResponse | null> => {
  try {
    //old
    // const data = await fetchWithAuth(`/order_status_update`, {
    //   method: 'POST',
    //   body: JSON.stringify({ order_id: orderId }),
    // })

    //new
    const data = await fetchWithAuth(
      `https://api.sharepal.in/api:qsuyzexA/order_timeline`,
      {
        method: "POST",
        body: JSON.stringify({ order_id: orderId }),
      },
    )
    return data
  } catch (error) {
    console.error("error", error)
    return null
  }
}

export const fetchCancellationReasons = async (): Promise<
  CancellationReason[]
> => {
  const data = await fetchWithAuthGet<CancellationReasons[]>(
    "https://api.sharepal.in/api:AIoqxnqr/website/cancellation-reasons",
  )
  return data
}

// New bike orders API - single API call with pagination
export const fetchBikeOrders = async (
  page: number,
  perPage: number = 5,
  filters: FilterValue[],
): Promise<BikeOrderResponse | null> => {
  const payload: BikeFilterPayload = {
    order_type: "bike",
    paging: {
      page,
      per_page: perPage,
    },
    filters,
  }

  const response = await fetchWithAuthPost(
    "https://api.sharepal.in/api:KAKxb4r4/bike/order/fetch/all",
    payload,
  )
  try {
    return response as BikeOrderResponse
  } catch (error) {
    console.error("Error fetching bike orders:", error)
    return null
  }
}

// New bike order status API
export const fetchBikeOrderStatus = async (
  orderId: string | null,
): Promise<OrderStatusResponse | null> => {
  if (!orderId) return null
  try {
    const data = await fetchWithAuth(
      `https://api.sharepal.in/api:KAKxb4r4/bike/bike-status`,
      {
        method: "POST",
        body: JSON.stringify({ order_id: orderId }),
      },
    )
    return data
  } catch (error) {
    console.error("error", error)
    return null
  }
}
