import { normalizeTimestamp } from "@/functions/date"
import { AvailableHub, Bike } from "@/types/common"
import { customFetchGet } from "@/utils/customFetch"

//get bikes in a city
export const fetchBikesInCity = async (city: string): Promise<Bike[] | []> => {
  try {
    const data = await customFetchGet(
      `https://api.sharepal.in/api:Lx-P60bj/city/bikes?city_url=${city}`,
    )
    if (!data) return []
    return data as Bike[]
  } catch (error) {
    console.error(error)
    return []
  }
}

// {
//   "pickup_time": "1755784800",
//   "dropoff_time": "1755873000",
//   "city_url": "bangalore",
//   "bike_code": "HIMALAYAN-450",
// }
//get available hubs for each bike code
// https://api.sharepal.in/api:Lx-P60bj/bike/available/hubs

interface AvailabeHubsParam {
  pickup_time: string
  dropoff_time: string
  city_url: string
  bike_code: string
}

export const fetchAvailableHub = async ({
  pickup_time,
  dropoff_time,
  city_url,
  bike_code,
}: AvailabeHubsParam): Promise<AvailableHub[] | []> => {
  const pickupTime = normalizeTimestamp(Number(pickup_time))
  const dropoffTime = normalizeTimestamp(Number(dropoff_time))
  try {
    const data = await customFetchGet(
      `https://api.sharepal.in/api:Lx-P60bj/bike/available/hubs?pickup_time=${pickupTime}&dropoff_time=${dropoffTime}&city_url=${city_url}&bike_code=${bike_code}`,
    )
    if (!data) return []
    return data as AvailableHub[]
  } catch (error) {
    console.error("Error fetching available hubs:", error)
    return []
  }
}

// Helper function to determine availability type
export const getHubAvailabilityType = (
  availableBikes: number,
): "full" | "partial" => {
  return availableBikes > 1 ? "full" : "partial"
}

// Transform available hubs to format used by hub selector component
export const transformAvailableHubsForSelector = (
  hubs: AvailableHub[],
): {
  fullyAvailableHubs: Array<{
    name: string
    hub_code: string
    available_bikes: number
  }>
  partiallyAvailableHubs: Array<{
    name: string
    hub_code: string
    available_bikes: number
  }>
} => {
  const fullyAvailableHubs = hubs.filter((hub) => hub.available_bikes > 1)
  const partiallyAvailableHubs = hubs.filter((hub) => hub.available_bikes === 1)

  return {
    fullyAvailableHubs,
    partiallyAvailableHubs,
  }
}

// Fetch bikes with availability for specific pickup/dropoff times
// export const fetchBikesWithAvailability = async (
//   pickup_time: number,
//   dropoff_time: number,
//   city_url: string,
// ): Promise<Bike[] | []> => {
//   try {
//     const data = await customFetchGet(
//       `https://api.sharepal.in/api:Lx-P60bj/get_bikes?pickup_time=${pickup_time}&dropoff_time=${dropoff_time}&city_url=${city_url}`,
//     )
//     if (!data) return []
//     return data as Bike[]
//   } catch (error) {
//     console.error("Error fetching bikes with availability:", error)
//     return []
//   }
// }

// export const fetchBikes = async (
//   hubCode: string,
// ): Promise<Bike[] | []> => {
//   try {
//     const data = await customFetchGet(
//       `https://api.sharepal.in/api:Lx-P60bj/hub/bikes?hub_code=${hubCode}`,
//     )
//     if (!data) return []
//     return data as Bike[]
//   } catch (error) {
//     console.error(error)
//     return []
//   }
// }

export const fetchBikeDetails = async (id: number): Promise<Bike | null> => {
  try {
    const data = await customFetchGet(
      `https://api.sharepal.in/api:Lx-P60bj/bike?id=${id}`,
    )
    if (!data) return null
    return data as Bike
  } catch (error) {
    console.error(error)
    return null
  }
}

// Fetch bike by bike_code
export const fetchBikeByCode = async (
  bike_code: string,
): Promise<Bike | null> => {
  try {
    const data = await customFetchGet(
      `https://api.sharepal.in/api:Lx-P60bj/bike?bike_code=${bike_code}`,
    )
    if (!data) return null
    return data as Bike
  } catch (error) {
    console.error(error)
    return null
  }
}

// Get bike details using bike_code for checkout (updated API endpoint)
export const fetchBikeDetailsForCheckout = async (
  bike_code: string,
  city: string,
): Promise<Bike | null> => {
  try {
    const data = await customFetchGet(
      `https://api.sharepal.in/api:Lx-P60bj/bike_details?bike_code=${bike_code}&city=${city}`,
    )
    if (!data) return null
    return data as Bike
  } catch (error) {
    console.error("Error fetching bike details:", error)
    return null
  }
}

// Check bike availability for checkout validation
export const checkBikeAvailability = async (
  pickup_time: number,
  dropoff_time: number,
  bike_code: string,
  city_url: string,
  hub_code: string,
): Promise<boolean> => {
  try {
    const response = await fetch(
      `https://api.sharepal.in/api:Lx-P60bj/bike/availibility`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          pickup_time: pickup_time.toString(),
          dropoff_time: dropoff_time.toString(),
          bike_code,
          city_url,
          hub_code,
        }),
      },
    )

    if (!response.ok) {
      return false
    }

    const data = await response.json()
    // Return the actual boolean response from the API
    return Boolean(data)
  } catch (error) {
    console.error("Error checking bike availability:", error)
    return false
  }
}
