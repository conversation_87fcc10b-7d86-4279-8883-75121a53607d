import { Hub } from "@/types/common"
import { customFetchGet } from "@/utils/customFetch"

export const fetchHubDetails = async (hubCode: string): Promise<Hub | null> => {
  try {
    const data = await customFetchGet(
      `https://api.sharepal.in/api:Lx-P60bj/hub?hub_code=${hubCode}`,
    )
    if (!data) return null
    return data as Hub
  } catch (error) {
    console.error(error)
    return null
  }
}
