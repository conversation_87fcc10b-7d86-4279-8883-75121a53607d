import { FaqType } from "@/types"
import { Coupon } from "@/types/coupon"
import { customFetch } from "@/utils/customFetch"
import { handleFetchError } from "@/utils/error-handling"

export const fetchFaqs = async (
  type?: string,
  city?: string,
  category?: string,
): Promise<FaqType[]> => {
  try {
    const url = `https://api.sharepal.in/api:EhQQ9F-L/faqs/all`
    const body = JSON.stringify({
      type: type?.trim(),
      city: city?.trim(),
      category: category?.trim(),
    })

    const response = await fetch(url, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body,
      cache: "force-cache",
      next: {
        revalidate: 3600,
      },
    })

    if (!response.ok) throw handleFetchError("Failed to fetch FAQs", response)
    const data: FaqType[] = await response.json()
    return data
  } catch (error) {
    throw handleFetchError(
      "Failed to fetch FAQs",
      error instanceof Response ? error : undefined,
    )
  }
}

export const fetchGoogleReviews = async (category: string) => {
  try {
    const url = `https://api.sharepal.in/api:EhQQ9F-L/product/reviews`
    const response = await fetch(url, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ category_name: category.replace(/-/g, " ") }),

      cache: "force-cache",
      next: {
        revalidate: 3600,
      },
    })
    if (!response.ok)
      throw handleFetchError("Failed to fetch Google Reviews", response)
    return response.json()
  } catch (error) {
    throw handleFetchError(
      "Failed to fetch Google Reviews",
      error instanceof Response ? error : undefined,
    )
  }
}

export const fetchDiscountCoupons = async () => {
  try {
    const response = await customFetch(
      "https://api.sharepal.in/api:EhQQ9F-L/coupons/type-all",
      {
        method: "POST",
        body: JSON.stringify({
          coupon_type: "bikes",
        }),
      },
    )
    return response
  } catch (error) {
    throw handleFetchError(
      "Failed to fetch discount Coupons",
      error instanceof Response ? error : undefined,
    )
  }
}

export const fetchAllFaqs = async () => {
  try {
    const response = await customFetch(
      `https://api.sharepal.in/api:KlLQg1sH/faqs`,
    )
    // const data = await response.json()
    return response
  } catch (error) {
    console.error(error)
  }
}

export const getOfferCoupon = async (): Promise<Coupon> => {
  try {
    const result = await customFetch(
      "https://api.sharepal.in/api:EhQQ9F-L/offer-coupon",
    )
    return result
  } catch (error) {
    throw handleFetchError(
      "Failed to fetch Offer Coupon",
      error instanceof Response ? error : undefined,
    )
  }
}
