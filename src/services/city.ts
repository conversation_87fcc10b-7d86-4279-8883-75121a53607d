//fetch all citeis

import { City } from "@/types/common"
import { customFetchGet } from "@/utils/customFetch"

export const fetchCities = async (): Promise<City[] | []> => {
  try {
    const data = await customFetchGet(
      `https://api.sharepal.in/api:Lx-P60bj/get_bike_cities`,
    )
    if (!data) return []
    return data as City[]
  } catch (error) {
    console.error(error)
    return []
  }
}
