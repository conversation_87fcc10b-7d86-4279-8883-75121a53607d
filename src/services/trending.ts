import { capitalizeFirstLetter } from "@/functions/small-functions"

type TrendingType = "bike" | "buy" | "rf"

export const fetchTrending = async (
  city: string,
  type: TrendingType = "bike",
  pageNumber: number = 1,
) => {
  try {
    const response = await fetch(
      `https://api.sharepal.in/api:EhQQ9F-L/products/trendings`,
      {
        cache: "force-cache",
        method: "POST", // Assuming it's a POST request, if not, change accordingly
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          city: capitalizeFirstLetter(city),
          type: type,
          paging: { page: pageNumber, per_page: 10 },
        }),
      },
    )
    if (!response.ok) {
      throw new Error("Reponse Failed to fetch trending items")
    }

    return response.json()
  } catch (error) {
    console.error(error)
    // throw new Error("Failed to fetch trending items");
  }
}

export const fetchRentalItemForCategory = async (
  city: string,
  catId: number,
  pageNumber: number = 1,
) => {
  try {
    const response = await fetch(
      `https://api.sharepal.in/api:EhQQ9F-L/rentalitem/pagination`,
      {
        method: "POST", // Assuming it's a POST request, if not, change accordingly
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          city: capitalizeFirstLetter(city),
          category_id: catId,
          paging: { page: pageNumber, per_page: 10 },
        }),
      },
    )
    if (!response.ok) {
      throw new Error("Reponse Failed to fetch trending items")
    }

    return response.json()
  } catch (error) {
    console.error(error)
    // throw new Error("Failed to fetch trending items");
  }
}
