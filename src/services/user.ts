import { getCookie } from "@/functions/cookies"
import { capitalizeFirstLetter } from "@/functions/small-functions"
import { ContactDetails } from "@/types/checkout"
import { User, Wallet } from "@/types/user"
import { decryptData } from "@/utils/decryption"
import { encryptData } from "@/utils/encryption"
import { fetchWithAuth } from "@/utils/fetchWithAuth"

interface ProfileFormData {
  first_name: string
  last_name: string
  email: string
}

export const getLoggedInUser = async (city: string) => {
  if (typeof window === "undefined") return
  const uid = getCookie("uid")

  try {
    const encryptedData = await fetchWithAuth(
      `https://api.sharepal.in/api:AIoqxnqr/user/me`,
    )

    if (!encryptedData) {
      return null
    }

    const decryptedData = await decryptData(encryptedData)

    // Parse the decrypted data into a User object
    const user = JSON.parse(decryptedData) as User

    // console.log("Decrypted User Data:", user)

    if (uid && user?.user_uid !== uid) {
      await updateUserUid()
      await updateUserCity(capitalizeFirstLetter(city))
    }

    return user
  } catch (error) {
    console.error(error)
    // throw new Error('Error fetching user data')
    throw error
  }
}

export const updateUserUid = async () => {
  try {
    await fetchWithAuth(
      `https://api.sharepal.in/api:AIoqxnqr/user/update-user-uid`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          user_uid: getCookie("uid"),
        }),
      },
    )
  } catch (error) {
    console.error("Error updating user uid:", error)
  }
}

// const { data } = await api.put("/update_user_new", formValues);

export const updateUser = async (
  userDetails: ContactDetails | ProfileFormData,
) => {
  try {
    const encryptedData = await encryptData(JSON.stringify(userDetails))

    const result = await fetchWithAuth(
      `https://api.sharepal.in/api:AIoqxnqr/user/update-user-new`,
      {
        method: "PUT",
        body: JSON.stringify({ data: encryptedData }),
      },
    )

    const decryptedData = await decryptData(result)

    // Parse the decrypted data into a User object
    const user = JSON.parse(decryptedData) as User

    return user
  } catch (error) {
    console.error(error)
  }
}

export const updateUserCity = async (user_city: string) => {
  try {
    const data = await fetchWithAuth(
      `https://api.sharepal.in/api:AIoqxnqr/user/update-user-city`,
      {
        method: "PATCH",
        body: JSON.stringify({ city: user_city }),
      },
    )
    return data
  } catch (error) {
    console.error(error)
  }
}

//get user saved addresses
export const getUserAddresses = async () => {
  try {
    const data = await fetchWithAuth(
      `https://api.sharepal.in/api:AIoqxnqr/user/all-address`,
    )
    return data
  } catch (error) {
    console.error(error)
  }
}

export const getUserWallet = async (): Promise<Wallet> => {
  try {
    const data = await fetchWithAuth(
      `https://api.sharepal.in/api:AIoqxnqr/user/wallet`,
    )

    return data
  } catch (error) {
    console.error("Error fetching user wallet:", error)
    throw error // Rethrow the error for handling by the caller
  }
}

export const gerUserWishlist = async () => {
  try {
    const data = await fetchWithAuth(
      `https://api.sharepal.in/api:AIoqxnqr/user/wishlist`,
    )
    return data
  } catch (error) {
    console.error(error)
  }
}

export const getUserVerification = async () => {
  try {
    const result = await fetchWithAuth(
      "https://api.sharepal.in/api:qsuyzexA/user_verification",
    )
    return result
  } catch (error) {
    console.error(error)
    return null
  }
}
