export interface Bike {
  id: number
  created_at: number
  name: string
  type: string
  fuel_type: string
  status: string
  out_of_stack: boolean
  bike_code: "available" | "booked" | "maintainance"
  city: string
  bike_images: string
  bike_modal: string
  price_per_hour: number
  surge: number
  goods_value: number
}

export interface BikeItem {
  id: number
  created_at: number
  name: string
  reg_no: string
  status: string
  is_available: boolean
  bike_code: string
  current_hub_id: number
  city: string
  creator_id: string
  engine_no: string
  chassis_no: string
  hub_code: string
}

export interface City {
  id: number
  city_name: string
  city_url: string
  sequence: number
}

export interface Hub {
  id: number
  city_id: number
  name: string
  lat: number
  lng: number
  is_active: boolean
  address: string
  type: string
  hub_code: string
}

// Hub availability information from API
export interface HubAvailability {
  hub_id: number
  hub_name: string
}

// Available hub response from fetchAvailableHub API
export interface AvailableHub {
  name: string
  hub_code: string
  available_bikes: number
}

// Enhanced bike type with pricing and hub availability
// export interface Bike extends Bike {
//   pricing: Pricing
//   fully_available_hubs: HubAvailability[]
//   partially_available_hubs: HubAvailability[]
// }
