export interface SideViewProps {
  openSideView: boolean
  setOpenSideView: React.Dispatch<React.SetStateAction<boolean>>
}

export interface DiscountCoupons {
  id: number
  created_at: number
  cart_active: boolean
  cart_discount: number
  cart_max_discount: number
  cart_minimum_value: number
  category: string
  city_type: string
  coupon_active: boolean
  coupon_code: string
  coupon_description: string
  coupon_header: string
  coupon_owner_email: string
  coupon_type: string
  days_to_deliver: number
  days_to_deliver_active: boolean
  delivery_discount: boolean
  delivery_discount_value: number
  show_on_website: boolean
  coupon_cat: string
}

export interface Inclusions {
  id: number
  created_at: number
  active: boolean
  product_short_name: string
  sub_product_code: string
  sub_product_image: string
  sub_product_name: string
}

export interface ProductSurge {
  id: number
  created_at: number
  delivery_date: string
  ri_name: string
  city: string
  surge_factor: number
  out_of_stock: boolean
  booked_count: string
  rating: number
  decoration_text: string
  super_category_short_name: string
  sub_category_short_name: string
}

export interface Partner {
  id: number
  created_at: number
  partner_name: string
  discount: number
  partner_logo: string
  surge_factor: number
  remove_surge: boolean
  partner_coupons: boolean
}

export type FaqType = {
  id: number
  question: string
  answer: string
  category: string
  faq_type: string
}

export type DealAdditionalDetails = {
  id: number
  created_at: number
  deal_name: string
  delay_rental_charge: number
  timeslot_charges: number
  total_return_charges: number
  tsa_applied: boolean
  ziplock_applied: boolean
  return_images: string[]
  return_payment_option: string
  return_charge_received: number
}
