import { Bike } from "./common"

export type OrderStatus =
  | "verification-required"
  | "received"
  | "confirmed"
  | "cancelled"
  | "not-ready"
  | "return-completed"
  | "Order Cancelled"

export type PaymentStatus = "pre-paid" | "pending"

export type FilterValue = "cancelled" | "active" | "completed"

export interface FilterPayload {
  filters: FilterValue[]
  order_type: "bike"
  paging: {
    page: number
    per_page: number
  }
}

export interface BikeFilterPayload {
  filters: FilterValue[]
  order_type: "bike"
  paging: {
    page: number
    per_page: number
  }
}

export interface Booking {
  id: number
  bike_id: number
  user_id: number
  hub_id: number
  pickup_time: number
  dropoff_time: number
  payment_option: number
  total_hours: number
  total_amount: number
  final_surge: number
  deposit_amount: number
  gst_amount: number
  coupon_code?: string
  coupon_discount?: number
  wallet_balance_used?: number
  seon_fingerprint: string
}

export interface BikeDetails {
  id: number
  created_at: number
  order_id: string
  stage: string
  first_name: string
  last_name: string
  start_time: number
  end_time: number
  total_hours: number
  coupon_code: string
  total_rent_per_hour: number
  carepal_fee: number
  total_rent: number
  bub_advance: number
  due_amount: number
  balance_payment_received: number
  coupon_discount: number
  calling_number: string
  whatsapp_number: string
  crm_id: string
  bike_code: string
}

export interface BikeOrder {
  id: number
  created_at: number
  order_id: string
  user_id: number
  bike_id: number
  bike_hubs_id: number
  pickup_time: number
  dropoff_time: number
  status: string
  total_rent: number
  gst_amount: number
  carepal_fee: number
  total_amount: number
  bub_advance: number
  final_surge: number
  updated_at: number
  city_url: string
  coupon_code: string
  coupon_discount: number
  wallet_balance_used: number
  payment_option: string
  rzp_order_id: string
  processed: boolean
  rent_per_hour: number
  total_hours: number
  bike_code: string
  first_name: string
  last_name: string
  calling_number: string
  email: string
  whatsapp_number: string
  additional_items_amount: number
}

export interface BikeOrderDetails {
  bike_order_detail: BikeDetails
  bike_detail: Bike
}

export interface BikeOrderSummary {
  bike_details: BikeDetails
  bike_order: BikeOrder
  bike: Bike
}

export interface BikeOrderResponse {
  itemsReceived: number
  curPage: number
  nextPage: number | null
  prevPage: number | null
  offset: number
  items: BikeOrderDetails[]
}

export interface ActionButton {
  active: boolean
  button_text: string
}

// New
// Define the type for a single action
export interface Action {
  text: string // Text displayed on the button
  variant:
    | "default"
    | "outline"
    | "outline-primary"
    | "primary"
    | "outline-destructive" // Variant of the button (e.g., default, outline)
  action_type: string // Variant of the button (e.g., default, outline)
  disabled: true
  message: string
}

// Define the type for stage information
interface StageInfo {
  title: string // Title of the stage
  message: string // Message describing the stage
  actions: Action[] // List of actions available for this stage
  warning?: boolean
  error?: boolean
  specialMessage?: {
    title: string
    description: string
    type: "warning" | "error" | "success" | "info" | "neutral-info"
    show: boolean
  }
}

interface AdditionalStageInfo {
  warning?: boolean
  error?: boolean
  specialMessage?: {
    title: string
    description: string
    type: "warning" | "error" | "success" | "info" | "neutral-info"
    show: boolean
  }
}

// Define the main type for the JSON structure
export interface OrderStatusResponse {
  stages: string[] // List of all possible stages
  activeStage: string // The currently active stage
  stageInfo: StageInfo // Information about the active stage
  additionalStages: string[]
  additionalActiveStage: string
  additionalStageInfo: AdditionalStageInfo
  showAdditionalStages: boolean
  order_timeline: { [key: string]: string }
}

export interface OrderTimelineResponse {
  order_timeline: { [key: string]: string }
}

export interface CancellationReason {
  reason: string
  id: number
}

export interface CancellationReasons {
  id: number
  created_at: number
  reason: string
  auto_cancel: boolean
  reason_type: string
}

export interface CreateBikeOrder {
  id: number
  created_at: number
  order_id: string
  user_id: number
  bike_id: number
  bike_hubs_id: number
  pickup_time: number
  dropoff_time: number
  status: string
  total_rent: number
  gst_amount: number
  carepal_fee: number
  total_amount: number
  bub_advance: number
  final_surge: number
  updated_at: number
  city_url: string
  coupon_code: string
  coupon_discount: number
  wallet_balance_used: number
  payment_option: string
  rzp_order_id: string
  processed: boolean
  rent_per_hour: number
  total_hours: number
  bike_code: string
  first_name: string
  last_name: string
  calling_number: string
  email: string
  whatsapp_number: string
  additional_items_amount: number
}

export interface OrderData {
  bike_order: CreateBikeOrder
  rzp_order_id: string
}
