import { Bike } from "./common"

export interface BikeSearchParams {
  pickup_timestamp: number
  dropoff_timestamp: number
  pickup_date: Date
  dropoff_date: Date
  pickup_time: string
  dropoff_time: string
}

export interface BikeSearchPageData {
  city: string
  searchParams?: BikeSearchParams
  bikes?: Bike[]
  error?: string
  isLoading?: boolean
}

export interface BikeSearchError {
  type: "VALIDATION" | "FETCH" | "NETWORK" | "SERVER"
  message: string
  retryable: boolean
}

export interface FilterState {
  bikeCategory: "all" | "electric"
  priceSort: "" | "low_to_high" | "high_to_low"
  availabilityFilter: "all" | "fully_available" | "partially_available"
  hubLocations: string[]
}

export interface BikeSearchState {
  bikes: Bike[]
  filteredBikes: Bike[]
  filters: FilterState
  showMobileFilters: boolean
}
