import {
  getFilteredTimeSlots,
  isTimeAfter,
  isTimeBefore,
  shouldDisableDate,
  TimeSlot,
} from "@/lib/bike-rental-utils"
import { useBikeRentalStore } from "@/store/bike-rental-store"
import { isSameDay } from "date-fns"
import { useMemo } from "react"

export const useBikeRentalHandlers = () => {
  const {
    pickup_date,
    dropoff_date,
    pickup_time,
    dropoff_time,

    setPickupDate,
    setDropoffDate,
    setPickupTime,
    setDropoffTime,
    total_hours,
  } = useBikeRentalStore()

  const handleDateSelect = (
    date: Date | undefined,
    type: "pickup" | "dropoff",
  ) => {
    if (!date) return

    if (type === "pickup") {
      if (isDateValid(date)) {
        setPickupDate(date)

        // If changing to same day as dropoff, validate times
        if (
          dropoff_date &&
          isSameDay(date, dropoff_date) &&
          pickup_time &&
          dropoff_time
        ) {
          // Clear dropoff time if it's invalid for same day
          if (!isTimeAfter(dropoff_time, pickup_time)) {
            setDropoffTime(null)
          }
        }
      }
    } else {
      if (isDropoffDateValid(date, pickup_date)) {
        setDropoffDate(date)

        // If changing to same day as pickup, validate times
        if (
          pickup_date &&
          isSameDay(pickup_date, date) &&
          pickup_time &&
          dropoff_time
        ) {
          // Clear pickup time if it's invalid for same day
          if (!isTimeBefore(pickup_time, dropoff_time)) {
            setPickupTime(null)
          }
        }
      }
    }
  }

  const handleTimeSelect = (time: string, type: "pickup" | "dropoff") => {
    if (type === "pickup") {
      setPickupTime(time)

      // If same day and dropoff time is now invalid, clear it
      if (
        pickup_date &&
        dropoff_date &&
        isSameDay(pickup_date, dropoff_date) &&
        dropoff_time
      ) {
        // Clear dropoff time if it's before or equal to new pickup time
        if (!isTimeAfter(dropoff_time, time)) {
          setDropoffTime(null)
        }
      }
    } else {
      setDropoffTime(time)

      // If same day and pickup time is now invalid, clear it
      if (
        pickup_date &&
        dropoff_date &&
        isSameDay(pickup_date, dropoff_date) &&
        pickup_time
      ) {
        // Clear pickup time if it's after or equal to new dropoff time
        if (!isTimeBefore(pickup_time, time)) {
          setPickupTime(null)
        }
      }
    }
  }

  const getTimeSlots = (type: "pickup" | "dropoff"): TimeSlot[] => {
    const date = type === "pickup" ? pickup_date : dropoff_date
    return getFilteredTimeSlots(
      date,
      type,
      pickup_date,
      dropoff_date,
      pickup_time,
      dropoff_time,
    )
  }

  // Date validation functions using shouldDisableDate
  const isDateValid = (date: Date) => {
    return !shouldDisableDate(date, "pickup")
  }

  const isDropoffDateValid = (
    date: Date,
    pickupDate: Date | null = pickup_date,
  ) => {
    return !shouldDisableDate(date, "dropoff", pickupDate)
  }

  // Always call useMemo hooks to maintain consistent hook order
  const pickupTimeSlots = useMemo(
    () => getTimeSlots("pickup"),
    [pickup_date, dropoff_date, pickup_time, dropoff_time],
  )

  const dropoffTimeSlots = useMemo(
    () => getTimeSlots("dropoff"),
    [pickup_date, dropoff_date, pickup_time, dropoff_time],
  )

  return {
    // State
    pickup_date,
    dropoff_date,
    pickup_time,
    dropoff_time,
    total_hours,

    // Handlers
    handleDateSelect,
    handleTimeSelect,
    getTimeSlots: (type: "pickup" | "dropoff") =>
      type === "pickup" ? pickupTimeSlots : dropoffTimeSlots,

    // Validation
    isDateValid,
    isDropoffDateValid: (date: Date) => isDropoffDateValid(date, pickup_date),

    // Timestamp utilities for backend integration
    getTimestamps: () => {
      const { getPickupTimestamp, getDropoffTimestamp } =
        useBikeRentalStore.getState()
      return {
        pickup_timestamp: getPickupTimestamp(),
        dropoff_timestamp: getDropoffTimestamp(),
      }
    },

    validateRental: () => {
      const { validateTimestamps } = useBikeRentalStore.getState()
      return validateTimestamps()
    },

    getRentalDuration: () => {
      const { getRentalDurationHours } = useBikeRentalStore.getState()
      return getRentalDurationHours()
    },
  }
}
