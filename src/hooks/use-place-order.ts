/* eslint-disable @typescript-eslint/no-explicit-any */

import { trackExtension, trackPaymentFailed } from "@/lib/gtag-event"
import {
  CreateBikeOrderPayload,
  createBikeRentalOrder,
  processBikeRentalOrder,
} from "@/services/bike-rental"
import { useCheckoutStore } from "@/store/checkout-store"
import { useCityStore } from "@/store/city-store"
import { useUserStore } from "@/store/user-store"
import { OrderData } from "@/types/order"
import { fetchWithAuthPost } from "@/utils/fetchWithAuth"
import { handleRazorpay } from "@/utils/razorpay" // Import Razorpay handler
import { useMutation, useQueryClient } from "@tanstack/react-query"
import { useRouter } from "next/navigation"
import { useState } from "react"
import { toast } from "sonner"

interface ProccesOrderType extends OrderData {
  razorpay_payment_id?: string
  razorpay_order_id?: string
  razorpay_signature?: string
  payment_made?: true
}

type ErrorMetadata = {
  order_id?: string
  payment_id?: string
}

type ErrorResponse = {
  code: string
  description: string
  source: string
  step: string
  reason: string
  metadata: ErrorMetadata
}

const usePlaceOrder = () => {
  const [isLoading, setIsLoading] = useState(false)
  const [isExtensionPaymentLoading, setIsExtensionPaymentLoading] =
    useState(false)
  const [isReturnPaymentLoading, setIsReturnPaymentLoading] = useState(false)
  const queryClient = useQueryClient()
  const { user } = useUserStore()
  const {
    // Bike rental specific data
    selected_bike,
    // pickup_location,
    // dropoff_location,
    // deposit_amount,
    gst_amount,

    // Common checkout data
    total_rent,
    final_amount,
    applied_coupon_code,
    payment_type,

    wallet_used,
    wallet_balance_used,
    contact_details,
    coupon_discount,
    setCheckoutRedirecting,
    setActiveSection,
    setOrderData,
    carepal_selected,
    carepal_fee,
    extra_helmet_selected,
    rental_params,
    total_hours,
  } = useCheckoutStore()

  const router = useRouter()

  const { selectedCity } = useCityStore()

  // Validate bike rental checkout data
  const validateBikeRentalCheckout = () => {
    if (!payment_type) {
      toast.error("Please select a payment option")
      setActiveSection("review")
      return false
    }

    if (
      !contact_details ||
      !contact_details?.first_name ||
      !contact_details?.last_name ||
      !contact_details?.email ||
      !contact_details?.calling_number ||
      !contact_details?.whatsapp_number
    ) {
      toast.error("Please enter and save your contact details")
      setActiveSection("contact")
      return false
    }

    if (!selected_bike) {
      toast.error("No bike selected for rental")
      return false
    }

    if (total_rent === 0) {
      toast.error("Invalid rental amount")
      return false
    }

    return true
  }

  // Validate checkout - now handles bike rental
  const validateCheckout = () => {
    return validateBikeRentalCheckout()
  }

  // Process bike rental order and notify the user after successful payment
  const processBikeRentalOrderAndNotify = async (
    orderData: ProccesOrderType,
  ) => {
    // Use the order id as booking_id for the new API
    const processedOrder = await processBikeRentalOrder({
      bike_order_id: orderData?.bike_order?.id,
    })

    if (processedOrder.success) {
      // Track bike rental completion (simplified tracking for bike rental)
      if (user) {
        // Simplified tracking for bike rental - no cart items, delivery charges, etc.
        // trackCheckoutCompleted can be updated later for bike rental specific tracking
      }

      toast.success("Bike rental booked successfully!")
      window.dataLayer = window.dataLayer || []

      window?.dataLayer.push({
        event: "order_success",
        total_value: final_amount,
        projected_value: Math.round(final_amount * 0.1),
        order_id: orderData.bike_order.order_id,
        currencyCode: "INR",
        bike_code: selected_bike?.bike_code,
        pickup_time: rental_params?.pickup_timestamp,
        dropoff_time: rental_params?.dropoff_timestamp,
        hub_code: rental_params?.hub_code,
        emailId: user?.email,
      })

      //  event: "order_success",
      //   total_value: finalAmount(),
      //   order_id: orderData.rent_order.order_id,
      //   currencyCode: "INR",
      //   projected_value: finalAmount() * 0.1,
      //   emailId: user?.email,

      router.replace(
        "/order/" +
          orderData?.bike_order?.order_id +
          "?status=process_confirmed",
      )
    } else {
      router.replace(
        "/order/" + orderData?.bike_order?.order_id + "?status=process_failed",
      )
      throw new Error("Bike rental order processing failed")
    }
  }

  // Process order function - now handles bike rental
  const processOrderAndNotify = async (orderData: any) => {
    return processBikeRentalOrderAndNotify(orderData)
  }

  //
  // Handle Razorpay payment
  const handleRazorpayPayment = async (orderData: OrderData) => {
    if (user) {
      await handleRazorpay(
        orderData.rzp_order_id,
        user,
        // On Success
        async (response) => {
          const verifyPayment = await fetchWithAuthPost(
            "https://api.sharepal.in/api:AIoqxnqr/payments/verify-razorypay-signature",
            {
              order_id: orderData?.bike_order?.order_id,
              razorpay_order_id: orderData.rzp_order_id,
              razorpay_payment_id: response.razorpay_payment_id,
              razorpay_signature: response.razorpay_signature,
            },
          )
          if (verifyPayment) {
            await processOrderAndNotify({
              ...orderData,
              payment_made: true,
              razorpay_payment_id: response.razorpay_payment_id,
              razorpay_order_id: response.razorpay_order_id,
              razorpay_signature: response.razorpay_signature,
            })
            toast.success("Payment successful!")
            router.replace(
              "/order/" +
                orderData?.bike_order?.order_id +
                "?status=payment_confrimed",
            )
            setTimeout(() => {
              setCheckoutRedirecting(false)
            })
          } else {
            toast.error("Payment verification failed")
            router.replace(
              "/order/" +
                orderData?.bike_order?.order_id +
                "?status=payment_failed",
            )
            setTimeout(() => {
              setCheckoutRedirecting(false)
            })
          }
        },
        // On Failure
        async (error?: ErrorResponse) => {
          console.error("Payment failed:", error)
          toast.error(
            `Payment failed: ${error?.description || "Please try again"}`,
          )
          trackPaymentFailed(
            final_amount,
            "Payment Modal Closed",
            orderData?.bike_order?.order_id,
          )
          await fetchWithAuthPost(
            "https://api.sharepal.in/api:AIoqxnqr/order/payment-failed",
            {
              rzp_id: orderData?.rzp_order_id,
              order_id: orderData?.bike_order?.order_id,
              amount: orderData?.bike_order?.total_amount,
              error_desc: error?.description,
              error_code: error?.code,
              error_reason: error?.reason,
              payment_id: error?.metadata.payment_id,
            },
          )
          router.push(
            "/order/" + orderData?.bike_order?.order_id + "?status=cancelled",
          )
          setTimeout(() => {
            setCheckoutRedirecting(false)
          })
        },
        // OnDismiss
        () => {
          setCheckoutRedirecting(false)
        },
      )
    } else {
      throw new Error("User not found")
    }
  }

  // Main function to handle payment
  const handlePayment = async () => {
    setIsLoading(true)

    // Return early if checkout validation fails
    if (!validateCheckout()) {
      setIsLoading(false)
      return
    }

    try {
      if (!user) {
        throw new Error("User not found")
      }

      if (!selected_bike || !contact_details) {
        throw new Error("Missing bike rental or contact details")
      }

      setCheckoutRedirecting(true)

      if (!rental_params) {
        throw new Error("Missing Rental Parameters !")
      }
      // Create bike rental order with new API payload structure
      const createOrderPayload: CreateBikeOrderPayload = {
        pickup_time: new Date(rental_params?.pickup_timestamp * 1000),
        dropoff_time: new Date(rental_params?.dropoff_timestamp * 1000),
        hub_code: rental_params?.hub_code,
        bike_code: selected_bike.bike_code,
        coupon_code: applied_coupon_code || "",
        total_amount: final_amount,
        gst_amount: gst_amount,
        wallet_applied: wallet_used,
        city_url: selectedCity?.city_url || "bangalore",
        wallet_balance_used: wallet_balance_used || 0,
        payment_option: payment_type?.toString() || "",
        coupon_discount: coupon_discount || 0,
        bike_rental_charges: total_rent,
        carepal_applied: carepal_selected,
        carepal_amount: carepal_fee,
        total_hours: total_hours,
        extra_helmet_applied: extra_helmet_selected,
      }

      const orderData = await createBikeRentalOrder(createOrderPayload)

      setOrderData(orderData)

      if (
        payment_type === 8 || // Payment option 3 is for COD (Cash on Delivery)
        (final_amount === 0 && // Zero amount order
          orderData.bike_order)
      ) {
        await processOrderAndNotify(orderData)
      } else {
        await handleRazorpayPayment(orderData)
      }
    } catch (error) {
      console.error("Payment error:", error)
      toast.error(
        error instanceof Error
          ? error.message
          : "Unable to create order. Please try again. refresh the page if the issue persists.",
      )
      setCheckoutRedirecting(false)
    } finally {
      setIsLoading(false)
    }
  }

  const { mutate: hanldePaymentForOrder } = useMutation({
    mutationFn: async ({
      order_id,
      amount,
      type = "BP",
    }: {
      order_id: string
      amount: number
      type?: "BP" | "EX"
    }) => {
      setIsLoading(true)
      try {
        const rzp_id = await fetchWithAuthPost<string>(
          "https://api.sharepal.in/api:AIoqxnqr/payments/create",
          {
            order_id: order_id,
            amount: amount,
            type,
          },
        )
        if (user)
          await handleRazorpay(
            rzp_id,
            user,
            // On Success
            async (response) => {
              const verifyPayment = await fetchWithAuthPost(
                "https://api.sharepal.in/api:AIoqxnqr/payments/verify-razorypay-signature",
                {
                  order_id: order_id,
                  razorpay_order_id: rzp_id,
                  razorpay_payment_id: response.razorpay_payment_id,
                  razorpay_signature: response.razorpay_signature,
                },
              )
              if (verifyPayment) {
                const data = await fetchWithAuthPost<{ status: string }>(
                  "https://api.sharepal.in/api:AIoqxnqr/return/process-payment",
                  {
                    rzp_id: rzp_id,
                    order_id: order_id,
                  },
                )
                if (data.status == "paid") {
                  toast.success("Payment successful!")
                  queryClient.invalidateQueries({
                    queryKey: ["order-details-fetch"],
                  })
                }
              } else {
                toast.error("Payment verification failed")
              }
            },
            // On Failure
            (error) => {
              console.error("Payment failed:", error)
              toast.error(
                `Payment failed: ${error?.message || "Please try again"}`,
              )
            },
            // OnDismiss
            () => {
              toast.error("Payment Cancelled")
            },
          )
        return {}
      } catch (error) {
        console.error(error)
      } finally {
        setIsLoading(false)
      }
    },
  })

  const { mutate: hanldePaymentForExtensionOrder } = useMutation({
    mutationFn: async ({
      order_id,
      amount,
      type = "OEX",
      days,
      pickup_date,
      new_pickup_date,
    }: {
      order_id: string
      amount: number
      type?: "OEX"
      days: number
      pickup_date?: Date
      new_pickup_date: Date
    }) => {
      setIsExtensionPaymentLoading(true)
      try {
        const rzp_id = await fetchWithAuthPost<string>(
          "https://api.sharepal.in/api:AIoqxnqr/payments/create",
          {
            order_id: order_id,
            amount: amount,
            type,
          },
        )

        try {
          await fetchWithAuthPost<string>(
            "https://api.sharepal.in/api:qsuyzexA/order/extension-request",
            {
              order_id,
              amount,
              days,
              rzp_id,
            },
          )
          trackExtension({
            order_id,
            amount,
            pickup_date: pickup_date || new Date(),
            user_id: user?.id || 0,
            new_pickup_date,
            days,
            eventName: "Extension Requested",
          })
        } catch {
          toast.error("Failed to create extension request. Please try again.")
          return
        }

        if (user)
          await handleRazorpay(
            rzp_id,
            user,
            // On Success
            async (response) => {
              const verifyPayment = await fetchWithAuthPost(
                "https://api.sharepal.in/api:AIoqxnqr/payments/verify-razorypay-signature",
                {
                  order_id: order_id,
                  razorpay_order_id: rzp_id,
                  razorpay_payment_id: response.razorpay_payment_id,
                  razorpay_signature: response.razorpay_signature,
                },
              )
              if (verifyPayment) {
                const data = await fetchWithAuthPost<{ status: string }>(
                  "https://api.sharepal.in/api:qsuyzexA/extension/payment-process",
                  {
                    rzp_id,
                  },
                )
                if (data.status == "paid") {
                  toast.success("Payment successful!")
                  queryClient.invalidateQueries({
                    queryKey: ["order-details-fetch"],
                  })
                  router.push(
                    "/extend-order/" +
                      order_id +
                      "?status=payment_confirmed&days=" +
                      days,
                  )
                  trackExtension({
                    order_id,
                    amount,
                    pickup_date: pickup_date || new Date(),
                    user_id: user?.id || 0,
                    new_pickup_date,
                    days,
                    eventName: "Extension Payment Success",
                  })
                }
              } else {
                toast.error("Payment verification failed")
                router.push(
                  "/extend-order/" + order_id + "?status=payment_failed",
                )
                trackExtension({
                  order_id,
                  amount,
                  pickup_date: pickup_date || new Date(),
                  user_id: user?.id || 0,
                  new_pickup_date,
                  days,
                  eventName: "Extension Payment Failed",
                })
              }
            },
            // On Failure
            async (error: ErrorResponse) => {
              console.error("Payment failed:", error)
              toast.error(
                `Payment failed: ${error?.reason || "Please try again"}`,
              )
              trackPaymentFailed(final_amount, "Payment Modal Closed", order_id)
              trackExtension({
                order_id,
                amount,
                pickup_date: pickup_date || new Date(),
                user_id: user?.id || 0,
                new_pickup_date,
                days,
                eventName: "Extension Payment Failed",
              })
              await fetchWithAuthPost(
                "https://api.sharepal.in/api:AIoqxnqr/order/payment-failed",
                {
                  rzp_id,
                  order_id,
                  amount,
                  error_desc: error?.description,
                  error_code: error?.code,
                  error_reason: error?.reason,
                  payment_id: error?.metadata.payment_id,
                },
              )
              router.push(
                "/extend-order/" + order_id + "?status=payment_failed",
              )
            },
            // OnDismiss
            () => {
              toast.error("Payment Cancelled")
              trackExtension({
                order_id,
                amount,
                pickup_date: pickup_date || new Date(),
                user_id: user?.id || 0,
                new_pickup_date,
                days,
                eventName: "Extension Payment Failed",
              })
              setIsExtensionPaymentLoading(false)
            },
          )
        return {}
      } catch (error) {
        console.error(error)
        setIsExtensionPaymentLoading(false)
      }
    },
  })

  const { mutate: hanldePaymentForReturnOrder } = useMutation({
    mutationFn: async ({
      order_id,
      amount,
      onSuccess,
    }: {
      order_id: string
      amount: number
      onSuccess?: () => void
    }) => {
      setIsReturnPaymentLoading(true)
      try {
        const rzp_id = await fetchWithAuthPost<string>(
          "https://api.sharepal.in/api:AIoqxnqr/payments/create",
          {
            order_id: order_id,
            amount: amount,
            type: "ORT",
          },
        )
        if (user)
          await handleRazorpay(
            rzp_id,
            user,
            // On Success
            async (response) => {
              const verifyPayment = await fetchWithAuthPost(
                "https://api.sharepal.in/api:AIoqxnqr/payments/verify-razorypay-signature",
                {
                  order_id: order_id,
                  razorpay_order_id: rzp_id,
                  razorpay_payment_id: response.razorpay_payment_id,
                  razorpay_signature: response.razorpay_signature,
                },
              )
              if (verifyPayment) {
                const data = await fetchWithAuthPost<{ status: string }>(
                  "https://api.sharepal.in/api:AIoqxnqr/return/process-payment",
                  {
                    rzp_id,
                    order_id,
                    amount,
                  },
                )
                if (data.status == "paid") {
                  toast.success("Payment successful!")
                  queryClient.invalidateQueries({
                    queryKey: ["order-details-fetch"],
                  })
                  if (onSuccess) onSuccess()
                  router.push(
                    "/return-payment/" + order_id + "?status=payment_confirmed",
                  )
                  setIsReturnPaymentLoading(false)
                }
              } else {
                toast.error("Payment verification failed")
                router.push(
                  "/return-payment/" + order_id + "?status=payment_failed",
                )
                setIsReturnPaymentLoading(false)
              }
            },
            // On Failure
            async (error: ErrorResponse) => {
              console.error("Payment failed:", error)
              toast.error(
                `Payment failed: ${error?.reason || "Please try again"}`,
              )
              trackPaymentFailed(amount, "Payment Modal Closed", order_id)
              await fetchWithAuthPost(
                "https://api.sharepal.in/api:AIoqxnqr/order/payment-failed",
                {
                  rzp_id,
                  order_id,
                  amount,
                  error_desc: error?.description,
                  error_code: error?.code,
                  error_reason: error?.reason,
                  payment_id: error?.metadata.payment_id,
                },
              )
              router.push(
                "/return-payment/" + order_id + "?status=payment_failed",
              )
              setIsReturnPaymentLoading(false)
            },
            // OnDismiss
            () => {
              toast.error("Payment Dismissed")
              setIsReturnPaymentLoading(false)
            },
          )
        return {}
      } catch (error) {
        console.error(error)
        setIsReturnPaymentLoading(false)
      }
    },
  })

  // const { mutate: hanldePaymentForCarepal } = useMutation({
  //   mutationFn: async ({
  //     order_id,
  //     amount,
  //   }: {
  //     order_id: string
  //     amount: number
  //   }) => {
  //     setIsLoading(true)
  //     try {
  //       const rzp_id = await fetchWithAuthPost<string>(
  //         "https://api.sharepal.in/api:AIoqxnqr/payments/create",
  //         {
  //           order_id: order_id,
  //           amount: amount,
  //           type: "CP",
  //         },
  //       )

  //       if (user)
  //         await handleRazorpay(
  //           rzp_id,
  //           user,
  //           // On Success
  //           async (response) => {
  //             const verifyPayment = await fetchWithAuthPost(
  //               "https://api.sharepal.in/api:AIoqxnqr/payments/verify-razorypay-signature",
  //               {
  //                 order_id: order_id,
  //                 razorpay_order_id: rzp_id,
  //                 razorpay_payment_id: response.razorpay_payment_id,
  //                 razorpay_signature: response.razorpay_signature,
  //               },
  //             )
  //             if (verifyPayment) {
  //               const data = await fetchWithAuthPost<{ status: string }>(
  //                 "https://api.sharepal.in/api:BV_IWA_a/carepal/payment-process",
  //                 {
  //                   rzp_id,
  //                 },
  //               )
  //               if (data.status == "paid") {
  //                 toast.success("Payment successful!")
  //                 router.push(
  //                   "/carepal/" + order_id + "?status=payment_confirmed",
  //                 )
  //               }
  //             } else {
  //               toast.error("Payment verification failed")
  //               router.push("/carepal/" + order_id + "?status=payment_failed")
  //             }
  //           },
  //           // On Failure
  //           async (error: ErrorResponse) => {
  //             console.error("Payment failed:", error)
  //             toast.error(
  //               `Payment failed: ${error?.reason || "Please try again"}`,
  //             )
  //             trackPaymentFailed(
  //               final_amount,
  //               "Payment Modal Closed",
  //               order_id,
  //             )
  //             await fetchWithAuthPost(
  //               "https://api.sharepal.in/api:AIoqxnqr/order/payment-failed",
  //               {
  //                 rzp_id,
  //                 order_id,
  //                 amount,
  //                 error_desc: error?.description,
  //                 error_code: error?.code,
  //                 error_reason: error?.reason,
  //                 payment_id: error?.metadata.payment_id,
  //               },
  //             )
  //             router.push("/carepal/" + order_id + "?status=payment_failed")
  //           },
  //           // OnDismiss
  //           () => {
  //             toast.error("Payment Cancelled")
  //             setIsLoading(false)
  //           },
  //         )
  //       return {}
  //     } catch (error) {
  //       console.log(error)
  //       setIsLoading(false)
  //     }
  //   },
  // })

  return {
    handlePayment,
    isLoading,
    isExtensionPaymentLoading,
    processOrderAndNotify,
    handleRazorpayPayment,
    validateCheckout,
    hanldePaymentForOrder,
    hanldePaymentForExtensionOrder,
    hanldePaymentForReturnOrder,
    isReturnPaymentLoading,
    // hanldePaymentForCarepal,
  }
}

export default usePlaceOrder
