import { fetchAvailableHub } from "@/services/bikes"
import { AvailableHub } from "@/types/common"
import { useEffect, useState } from "react"

interface UseAvailableHubsParams {
  pickup_time: string
  dropoff_time: string
  city_url: string
  bike_code: string
  enabled?: boolean
}

export const useAvailableHubs = ({
  pickup_time,
  dropoff_time,
  city_url,
  bike_code,
  enabled = true,
}: UseAvailableHubsParams) => {
  const [hubs, setHubs] = useState<AvailableHub[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [hasInitialized, setHasInitialized] = useState(false)

  const fetchHubs = async () => {
    if (!enabled || !pickup_time || !dropoff_time || !city_url || !bike_code) {
      return
    }

    setLoading(true)
    setError(null)
    try {
      const availableHubs = await fetchAvailableHub({
        pickup_time,
        dropoff_time,
        city_url,
        bike_code,
      })
      setHubs(availableHubs)
      setHasInitialized(true)
    } catch (err) {
      setError("Failed to fetch available hubs")
      console.error("Error fetching available hubs:", err)
    } finally {
      setLoading(false)
    }
  }

  // Only fetch on manual trigger, not on mount
  useEffect(() => {
    // Don't auto-fetch on mount
  }, [pickup_time, dropoff_time, city_url, bike_code, enabled])

  return {
    hubs,
    loading,
    error,
    hasInitialized,
    fetchHubs,
    refetch: fetchHubs,
  }
}
