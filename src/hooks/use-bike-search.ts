import { Bike } from "@/types/common"
import { useCallback, useEffect, useMemo, useState } from "react"

interface UseBikeSearchProps {
  initialBikes: Bike[]
}

interface UseBikeSearchReturn {
  filteredBikes: Bike[]
  showMobileFilters: boolean
  setShowMobileFilters: (show: boolean) => void
  handleFiltersChange: (filtered: Bike[]) => void
}

export const useBikeSearch = ({
  initialBikes,
}: UseBikeSearchProps): UseBikeSearchReturn => {
  const [showMobileFilters, setShowMobileFilters] = useState(false)

  // Initialize filtered bikes with initial bikes
  const [filteredBikes, setFilteredBikes] = useState<Bike[]>([])

  // Update filtered bikes when initialBikes changes
  useEffect(() => {
    console.log(
      "🔄 [useBikeSearch] Updating filtered bikes:",
      initialBikes.length,
      "bikes",
    )
    setFilteredBikes(initialBikes)
  }, [initialBikes])

  // Memoized filter change handler to prevent unnecessary re-renders
  const handleFiltersChange = useCallback((filtered: Bike[]) => {
    console.log(
      "🔍 [useBikeSearch] Filters changed, new count:",
      filtered.length,
    )
    setFilteredBikes(filtered)
  }, [])

  // Memoize the return object to prevent unnecessary re-renders
  return useMemo(
    () => ({
      filteredBikes,
      showMobileFilters,
      setShowMobileFilters,
      handleFiltersChange,
    }),
    [filteredBikes, showMobileFilters, handleFiltersChange],
  )
}
