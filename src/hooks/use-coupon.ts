import { verifyCoupon } from "@/services/checkout"
import { useBikeRentalStore } from "@/store/bike-rental-store"
import { useCheckoutStore } from "@/store/checkout-store"
import * as Sentry from "@sentry/nextjs"
import { useEffect, useState } from "react"
import { toast } from "sonner"

const useCoupon = () => {
  const {
    total_rent,
    applied_coupon_code,
    setAppliedCouponCode,
    final_amount,
  } = useCheckoutStore()
  const [couponCode, setCouponCode] = useState(applied_coupon_code || "")
  const [isLoading, setIsLoading] = useState(false)
  const [showViewCoupons, setShowViewCoupons] = useState(false)

  const {
    pickup_date,
    dropoff_date,
    total_hours,
    pickup_timestamp,
    dropoff_timestamp,
  } = useBikeRentalStore()

  const handleRemoveCoupon = () => {
    setAppliedCouponCode("", 0)
  }
  const [adminOnly, setAdminOnly] = useState(false)
  useEffect(() => {
    setAdminOnly(
      ((window &&
        window.sessionStorage &&
        window.sessionStorage.getItem("backend_order")) ??
        "") == "true",
    )
  }, [])

  const handleApplyCoupon = async (
    code: string,
    total_rental_amount?: number,
    removeCoupon?: true,
  ) => {
    try {
      Sentry.captureMessage("ApplyCoupon", {
        level: "warning",
        extra: {
          user_name: "John Doe",
          email_id: "<EMAIL>",
        },
      })

      if (couponCode == code && removeCoupon) {
        handleRemoveCoupon()
        return
      }

      setIsLoading(true)

      if (!code || code === "") {
        toast.info("Please Enter the Coupon Code")
        return
      }
      if (
        !pickup_timestamp ||
        !dropoff_timestamp ||
        !total_hours ||
        !pickup_date ||
        !dropoff_date
      ) {
        toast.error("Select your rental period again!")
        return
      }

      const verified = await verifyCoupon({
        applied_coupon: code,
        pickup_date: pickup_date,
        dropoff_date: dropoff_date,
        total_hours: total_hours,
        pickup_timestamp: pickup_timestamp,
        dropoff_timestamp: dropoff_timestamp,
      })

      if (!verified) {
        toast.error("Coupon is Not Verified")
        return
      }

      const {
        coupon_active,
        delivery_discount,
        cart_discount,
        cart_max_discount,
        coupon_code,
        admin,
      } = verified

      if (admin && !adminOnly) {
        toast.error("Coupon is not applicable for this order")
        return
      }

      if (coupon_code === "PAIDREPLACEMENT") {
        toast.info("Applying paid replacement coupon")
        //logic for paid replacement
        if (coupon_active) {
          setAppliedCouponCode(coupon_code, 0)
        }
        // toast.success("Coupon Applied Successfully")
        return
      } else if (
        coupon_code === "ZEROORDER" ||
        coupon_code === "SPDAMAGEMIG" ||
        coupon_code === "SNBPZ"
      ) {
        toast.info("Applying special coupon")
        //logic
        if (coupon_active) {
          setAppliedCouponCode(coupon_code, final_amount)
        }
        toast.success("Coupon Applied Successfully")
        return
      }

      let couponDiscount = 0
      if (delivery_discount) {
        couponDiscount = 299
      } else {
        const partner_discount = 0 // handle partner discount
        const discount = Math.min(
          ((total_rent || total_rental_amount || 0) - (partner_discount ?? 0)) *
            cart_discount,
          cart_max_discount,
        )
        couponDiscount = discount
      }
      // toast.success("Coupon Applied Successfully")
      setAppliedCouponCode(coupon_code, couponDiscount)
      return true
    } catch (error) {
      console.error("Error applying coupon:", error)
      toast.error(
        error instanceof Error ? error.message : "Failed to apply coupon",
      )
    } finally {
      setIsLoading(false)
    }
  }
  useEffect(() => {
    setCouponCode(applied_coupon_code)
  }, [applied_coupon_code])

  return {
    couponCode,
    isLoading,
    showViewCoupons,
    setShowViewCoupons,
    handleApplyCoupon,
    setCouponCode,
    handleRemoveCoupon,
  }
}

export default useCoupon
