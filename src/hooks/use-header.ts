// useHeaderLogic.ts
import { getCookie, setCookie } from "@/functions/cookies"
import generateUniqueId from "@/functions/generate-uuid"
import { fetchSameDaySurge, fetchSurgeFactor } from "@/services/surge"
import { getUserWallet } from "@/services/user"
import { useBikeRentalStore } from "@/store/bike-rental-store"
import { useCityStore } from "@/store/city-store"
import { useUserStore } from "@/store/user-store"
import { areDatesEqual, isDateBeforeToday } from "@/utils/date-logics"
import { useQuery } from "@tanstack/react-query"
import { addDays, differenceInDays } from "date-fns"
import { useParams } from "next/navigation"
import { useEffect, useState } from "react"

export const useHeaderLogic = () => {
  const [isWhatsappSupportModalOpen, setIsWhatsappSupportModalOpen] =
    useState(false)
  const [isCityModalOpen, setCityModalOpen] = useState(false)
  const [isProfileOpen, setIsProfileOpen] = useState(false)
  const [isSearchOpen, setIsSearchOpen] = useState(false)

  // Get bike rental dates and selector state from bike rental store
  const {
    pickup_date: bike_pickup_date,
    dropoff_date: bike_dropoff_date,
    setPickupDate: setBikePickupDate,
    setDropoffDate: setBikeDropoffDate,
    isSelectorOpen: isBikeRentalOpen,
    openSelector,
    setSurgeFactor,
    setSameDaySurgeLoading,
  } = useBikeRentalStore()

  const openBikeRental = () => openSelector()

  const { selectedCity } = useCityStore()

  // Use bike rental dates as the primary dates for header display
  const delivery_date = bike_pickup_date
  const pickup_date = bike_dropoff_date

  // Calculate total days from bike rental dates
  const total_days =
    bike_pickup_date && bike_dropoff_date
      ? Math.max(1, differenceInDays(bike_dropoff_date, bike_pickup_date))
      : 0

  const { user, fetchUser, setIsLoggedIn, isLoggedIn, setWallet } =
    useUserStore()
  // const { items_count, setCartItems, setWalletBalance } = useCheckoutStore() // Commented out - no cart for bike rentals

  const { city, cat, subcat, product } = useParams<{
    cat: string
    city: string
    subcat: string
    product: string
  }>()

  // Fetch user data
  const { isLoading: isUserLoading } = useQuery({
    queryKey: ["user"],
    queryFn: () => fetchUser(selectedCity.city_url),
    enabled: !user,
    refetchInterval: 60 * 60 * 1000, // 1 hour
  })

  const { data: surgeFactorData } = useQuery({
    queryKey: ["surgeFactor", delivery_date],
    queryFn: async () => {
      if (!delivery_date) return { surge_factor: 1 }
      const response = await fetchSurgeFactor({
        delivery_date: addDays(delivery_date, 1),
      })
      return response?.surge_date ? response : { surge_factor: 1 }
    },
    enabled: !!delivery_date, // Only fetch when delivery_date exists
    staleTime: 1000 * 60 * 5, // Cache results for 5 minutes
  })

  const { data: sameDaySurgeData, isLoading: isSameDaySurgeLoading } = useQuery(
    {
      queryKey: ["sameDaySurge", selectedCity?.city_url, delivery_date],
      queryFn: async () => {
        if (!selectedCity?.city_url) return { same_day_surge: 1 }
        const response = await fetchSameDaySurge(selectedCity.city_url)
        if (
          response &&
          delivery_date &&
          areDatesEqual(delivery_date, new Date())
        ) {
          return response
        }
        return { same_day_surge: 1 }
      },
      enabled: !!selectedCity?.city_url, // Only fetch when city is selected
    },
  )

  const {} = useQuery({
    queryKey: ["user_wallet"], // Unique query key
    queryFn: async () => {
      const wallet = await getUserWallet()
      // setWalletBalance(roundValue(wallet.amount)) // Commented out - no cart for bike rentals
      setWallet(wallet)
      return wallet
    },

    refetchOnWindowFocus: false, // Prevent refetch on window focus
    enabled: !isUserLoading, // Enable the query when user data is loaded
  })

  // Set surge values when queries complete
  useEffect(() => {
    setSameDaySurgeLoading(isSameDaySurgeLoading)
    setSurgeFactor(surgeFactorData?.surge_factor ?? 1)
  }, [
    setSameDaySurgeLoading,
    sameDaySurgeData,
    surgeFactorData,
    setSurgeFactor,
    isSameDaySurgeLoading,
  ])

  //handling uuid
  useEffect(() => {
    const localUid = getCookie("uid")
    if (!localUid) {
      setCookie("uid", generateUniqueId(), { expires: 365 })
    }
  }, [])

  // handle no city
  useEffect(() => {
    if (!city && !selectedCity.city_url) {
      setCityModalOpen(true)
    } else if (city === "india") {
      setCityModalOpen(true)
    } else {
      setCityModalOpen(false)
    }
  }, [city, selectedCity])

  // testing to reduce unncessary re renders
  // const setCityModaCallback = useCallback(
  //   (value: boolean) => setCityModalOpen(value),
  //   [setCityModalOpen]
  // )

  //if home page no city and we have city then redirec to city page
  // useEffect(() => {
  //   if (pathname === '/' && selectedCity.city_url) {
  //     router.push(`/${selectedCity.city_url}`)
  //   }
  // }, [pathname, router, selectedCity])

  // Only need to run onLoad for date check
  useEffect(() => {
    if (delivery_date && pickup_date) {
      if (isDateBeforeToday(delivery_date)) {
        setBikePickupDate(null)
        setBikeDropoffDate(null)
      }
    }
  }, [delivery_date, pickup_date, setBikePickupDate, setBikeDropoffDate])

  return {
    isWhatsappSupportModalOpen,
    setIsWhatsappSupportModalOpen,
    isCityModalOpen,
    setCityModalOpen,
    isProfileOpen,
    setIsProfileOpen,
    delivery_date,
    pickup_date,
    total_days,
    selectedCity,
    user,
    isLoggedIn,
    setIsLoggedIn,
    // items_count, // Commented out - no cart for bike rentals
    cat,
    subcat,
    product,
    isSearchOpen,
    setIsSearchOpen,
    specialRoute: Boolean((cat || subcat) && !product),
    isBikeRentalOpen,
    openBikeRental,
  }
}
