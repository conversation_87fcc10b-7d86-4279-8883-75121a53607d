// // import { fetchBikesWithAvailability } from "@/services/bikes"
// // import { Bike } from "@/types/common"
// import { Bike } from "@/types/common"
// import { useQuery } from "@tanstack/react-query"

// interface UseBikesQueryProps {
//   pickup_timestamp: number | null
//   dropoff_timestamp: number | null
//   city: string
//   enabled?: boolean
// }

// // Custom hook for fetching bikes with TanStack Query
// export const useBikesQuery = ({
//   pickup_timestamp,
//   dropoff_timestamp,
//   city,
//   enabled = true,
// }: UseBikesQueryProps) => {
//   return useQuery<Bike[], Error>({
//     queryKey: ["bikes", pickup_timestamp, dropoff_timestamp, city],
//     queryFn: async () => {
//       console.log("🚲 [useBikesQuery] Fetching bikes with params:", {
//         pickup_timestamp,
//         dropoff_timestamp,
//         city,
//       })

//       if (!pickup_timestamp || !dropoff_timestamp) {
//         throw new Error("Missing pickup or dropoff timestamp")
//       }

//       const bikes = await fetchBikesWithAvailability(
//         pickup_timestamp,
//         dropoff_timestamp,
//         city,
//       )

//       console.log(
//         "✅ [useBikesQuery] Bikes fetched successfully:",
//         bikes.length,
//         "bikes",
//       )
//       return bikes
//     },
//     enabled: enabled && !!pickup_timestamp && !!dropoff_timestamp && !!city,
//     staleTime: 1000 * 60 * 2, // 2 minutes - bikes availability can change
//     gcTime: 1000 * 60 * 5, // 5 minutes
//     retry: 3,
//     retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
//     refetchOnWindowFocus: false,
//   })
// }
