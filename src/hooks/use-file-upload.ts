"use client"

import { authHeader } from "@/utils/fetchWithAuth"
import { useMutation } from "@tanstack/react-query"
import axios, { AxiosProgressEvent } from "axios"
import { useState } from "react"
import { toast } from "sonner"

const FILE_UPLOAD_API_URL = `https://api.sharepal.in/api:AIoqxnqr/verification/multiple-file-upload/v1`

export enum FILE_TYPE {
  DL = "dl",
  IDENTITY = "identity",
  OCCUPATION = "occupation",
  PAN = "pan",
}

interface SingleFileUploadOptions {
  fileType: FILE_TYPE
  onSuccess?: (response: any) => void
  onError?: (error: any) => void
  occupationType?: string
}

export function useSingleFileUpload({
  fileType,
  onSuccess,
  onError,
  occupationType,
}: SingleFileUploadOptions) {
  const [uploadProgress, setUploadProgress] = useState(0)
  const [uploadError, setUploadError] = useState("")
  const [isUploading, setIsUploading] = useState(false)
  const [isUploadSuccess, setIsUploadSuccess] = useState(false)

  const resetUploadState = () => {
    setUploadProgress(0)
    setUploadError("")
    setIsUploading(false)
    setIsUploadSuccess(false)
  }

  const { mutate: uploadFile, isPending } = useMutation({
    mutationFn: async (file: File) => {
      const token = localStorage.getItem("token")

      if (!token) throw new Error("Authentication required")
      if (!file) throw new Error("File is required")

      setIsUploading(true)
      setUploadError("")

      const formData = new FormData()
      formData.append("file1", file)
      formData.append("file_type", fileType)
      if (occupationType) {
        formData.append("occupation_type", occupationType)
      }

      try {
        const res = await axios.post(FILE_UPLOAD_API_URL, formData, {
          headers: authHeader(token, "multipart/form-data"),
          onUploadProgress: ({ loaded, total }: AxiosProgressEvent) => {
            if (total) {
              const percent = Math.round((loaded * 100) / total)
              setUploadProgress(percent)
            }
          },
        })

        return res
      } finally {
        setIsUploading(false)
      }
    },

    onSuccess: (res) => {
      const isSuccess = res.status === 200

      setIsUploadSuccess(isSuccess)
      setUploadError(isSuccess ? "" : "Upload failed. Please try again.")

      if (isSuccess) {
        toast.success("File uploaded successfully")
        onSuccess?.(res)
      } else {
        toast.error("Upload failed. Please try again.")
        onError?.(new Error("Upload failed"))
      }
    },

    onError: (error) => {
      setIsUploading(false)
      setIsUploadSuccess(false)
      setUploadError("Upload failed. Please try again.")
      toast.error("Upload failed. Please try again.")
      onError?.(error)
    },
  })

  return {
    uploadFile,
    isPending,
    uploadProgress,
    uploadError,
    isUploading,
    isUploadSuccess,
    resetUploadState,
  }
}

// For backward compatibility and multiple file uploads
interface MultiFileUploadOptions {
  fileType: string
  onSuccess?: (response: any) => void
  onError?: (error: any) => void
  occupationType?: string
}

export function useMultiFileUpload({
  fileType,
  onSuccess,
  onError,
  occupationType,
}: MultiFileUploadOptions) {
  const [uploadProgress, setUploadProgress] = useState<Record<string, number>>(
    {},
  )
  const [uploadError, setUploadError] = useState<Record<string, string>>({})
  const [isUploading, setIsUploading] = useState<Record<string, boolean>>({})
  const [isUploadSuccess, setIsUploadSuccess] = useState<
    Record<string, boolean>
  >({})

  const resetUploadState = () => {
    setUploadProgress({})
    setUploadError({})
    setIsUploading({})
    setIsUploadSuccess({})
  }

  const { mutate: uploadFiles, isPending } = useMutation({
    mutationFn: async ({
      frontFile,
      backFile,
    }: {
      frontFile: File
      backFile: File
    }) => {
      const token = localStorage.getItem("token")

      if (!token) throw new Error("Authentication required")
      if (!frontFile || !backFile) throw new Error("Both files are required")

      setIsUploading({ frontFile: true, backFile: true })

      const formData = new FormData()
      formData.append("front_image", frontFile)
      formData.append("back_image", backFile)
      formData.append("file_type", fileType)
      if (occupationType) {
        formData.append("occupation_type", occupationType)
      }

      try {
        const res = await axios.post(FILE_UPLOAD_API_URL, formData, {
          headers: authHeader(token, "multipart/form-data"),
          onUploadProgress: ({ loaded, total }: AxiosProgressEvent) => {
            if (total) {
              const percent = Math.round((loaded * 100) / total)
              setUploadProgress({ frontFile: percent, backFile: percent })
            }
          },
        })

        return res
      } finally {
        setIsUploading({ frontFile: false, backFile: false })
      }
    },

    onSuccess: (res) => {
      const isSuccess = res.status === 200
      const commonUpdate = {
        frontFile: isSuccess,
        backFile: isSuccess,
      }

      setIsUploadSuccess(commonUpdate)
      setUploadError({
        frontFile: isSuccess ? "" : "Upload failed. Please try again.",
        backFile: isSuccess ? "" : "Upload failed. Please try again.",
      })

      if (isSuccess) {
        toast.success("Files uploaded successfully")
        onSuccess?.(res)
      } else {
        toast.error("Upload failed. Please try again.")
        onError?.(new Error("Upload failed"))
      }
    },

    onError: (error) => {
      setIsUploading({ frontFile: false, backFile: false })
      setIsUploadSuccess({ frontFile: false, backFile: false })
      setUploadError({
        frontFile: "Upload failed. Please try again.",
        backFile: "Upload failed. Please try again.",
      })
      toast.error("Upload failed. Please try again.")
      onError?.(error)
    },
  })

  return {
    uploadFiles,
    isPending,
    uploadProgress,
    uploadError,
    isUploading,
    isUploadSuccess,
    resetUploadState,
  }
}
