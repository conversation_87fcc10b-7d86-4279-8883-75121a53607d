import { validateRentalPeriod } from "@/lib/bike-rental-utils"
import { BikeSearchParams } from "@/types/bike-search"
import { useMemo } from "react"

interface ValidationResult {
  isValid: boolean
  errors: string[]
  warnings: string[]
}

interface UseSearchParamsValidationProps {
  searchParams: BikeSearchParams | undefined
}

interface UseSearchParamsValidationReturn extends ValidationResult {
  hasExpired: boolean
  timeUntilPickup: number | null
  durationHours: number | null
}

/**
 * Hook to validate search parameters and provide helpful error messages
 */
export const useSearchParamsValidation = ({
  searchParams,
}: UseSearchParamsValidationProps): UseSearchParamsValidationReturn => {
  return useMemo(() => {
    const errors: string[] = []
    const warnings: string[] = []
    let hasExpired = false
    let timeUntilPickup: number | null = null
    let durationHours: number | null = null

    if (!searchParams) {
      errors.push("Search parameters are missing")
      return {
        isValid: false,
        errors,
        warnings,
        hasExpired,
        timeUntilPickup,
        durationHours,
      }
    }

    const now = Math.floor(Date.now() / 1000) // Current time in seconds
    const { pickup_timestamp, dropoff_timestamp } = searchParams

    // Calculate derived values
    timeUntilPickup = pickup_timestamp - now
    durationHours = (dropoff_timestamp - pickup_timestamp) / 3600

    // Validate timestamps
    if (pickup_timestamp <= 0 || dropoff_timestamp <= 0) {
      errors.push("Invalid timestamp values")
    }

    if (pickup_timestamp >= dropoff_timestamp) {
      errors.push("Pickup time must be before dropoff time")
    }

    // Check if pickup time has passed
    if (pickup_timestamp < now) {
      hasExpired = true
      errors.push("The selected pickup time has already passed")
    }

    // Check if pickup is too soon (less than 1 hour from now)
    if (timeUntilPickup > 0 && timeUntilPickup < 3600) {
      warnings.push("Pickup time is less than 1 hour away")
    }

    // Use comprehensive rental period validation
    if (
      pickup_timestamp > 0 &&
      dropoff_timestamp > 0 &&
      pickup_timestamp < dropoff_timestamp
    ) {
      const rentalValidation = validateRentalPeriod(
        pickup_timestamp,
        dropoff_timestamp,
      )

      if (!rentalValidation.isValid) {
        errors.push(...rentalValidation.errors)
      }

      // Add warnings for very long rentals (7 days)
      if (durationHours !== null && durationHours > 7 * 24) {
        warnings.push("Long rental duration - consider our weekly rates")
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      hasExpired,
      timeUntilPickup,
      durationHours,
    }
  }, [searchParams])
}
