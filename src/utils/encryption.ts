export const getKeys = async () => {
  const aesKeyBase64 = "oniaCwjim1zEYvHtsPOWKdCeMgiAgwpFACiboYM6lJY="
  const encryptionKey = Buffer.from(aesKeyBase64, "base64")
  const ivBase64 = "hdEbFHkR3WKOA5UkCYrM2Q=="
  const initVector = Buffer.from(ivBase64, "base64")
  return { encryptionKey, initVector }
}
export const encryptData = async (plainData: string) => {
  // Use provided key or get from environment
  // const encryptionKey = await generateAESKeyFromEnv()
  const { encryptionKey, initVector } = await getKeys()
  // Encode the data to be encrypted
  const encodedData = new TextEncoder().encode(plainData)

  // Prepare the encryption key
  const cryptoKey = await crypto.subtle.importKey(
    "raw",
    encryptionKey,
    {
      name: "AES-GCM",
      length: 256,
    },
    true,
    ["encrypt", "decrypt"],
  )

  // Encrypt the encoded data with the key
  const encryptedData = await crypto.subtle.encrypt(
    {
      name: "AES-GCM",
      iv: initVector,
    },
    cryptoKey,
    encodedData,
  )

  // Return the encrypted data and the IV, both in base64 format
  return Buffer.from(encryptedData).toString("base64")

  // {
  //   encryptedData: Buffer.from(encryptedData).toString("base64"),
  //   initVector: Buffer.from(initVector).toString("base64"),
  // }
}
