import {
  ORDER_TYPES,
  USER_VERIFICATION,
  VERIFICATION_CASE,
  VERIFICATION_STATUS,
} from "@/types/verification"

export const isStatusRequested = (status: VERIFICATION_STATUS | "") =>
  status === VERIFICATION_STATUS.Requested || status === ""

export const isStatusVerified = (status: VERIFICATION_STATUS) =>
  status === VERIFICATION_STATUS.Verified

export const isStatusNotRequested = (status: VERIFICATION_STATUS | "") =>
  status !== VERIFICATION_STATUS.Requested && status !== ""

export const isPanAutoFetched = (
  creditVerifiedType: "auto_fetched" | "fetched_with_pan",
) => creditVerifiedType === "auto_fetched"

export const isAllSubmitted = (userVerification: USER_VERIFICATION) => {
  if (!userVerification) {
    return false
  }
  const { identity_status, occupation_status, credit_status } = userVerification
  return (
    isStatusNotRequested(identity_status) &&
    isStatusNotRequested(occupation_status) &&
    isStatusNotRequested(credit_status)
  )
}

// export const getVerificationStatuses = (
//   userVerification: USER_VERIFICATION,
// ) => {
//   if (!userVerification) {
//     return {
//       isIdentityVerified: false,
//       isOccupationVerified: false,
//       isPanVerified: false,
//     }
//   }
//   const { identity_status, occupation_status, credit_status, dl_status } =
//     userVerification
//   return {
//     isIdentityVerified:
//       isStatusVerified(identity_status) && isStatusVerified(dl_status),
//     isOccupationVerified: isStatusVerified(occupation_status),
//     isPanVerified: isStatusVerified(credit_status),
//   }
// }

export const getShowChoice = (userVerification: USER_VERIFICATION) => {
  if (!userVerification) return false

  const { last_order_type, occupation_status, credit_status } = userVerification

  const verification_case = getVerificationCaseFromOrderType(last_order_type)

  const isIdentityAndChoiceCase =
    verification_case === VERIFICATION_CASE.IdentityAndChoice

  const ChoiceInRequestStatus =
    isStatusRequested(occupation_status) && isStatusRequested(credit_status)

  return isIdentityAndChoiceCase && ChoiceInRequestStatus
}

export const getDefaultExpandedStep = (
  userVerification: USER_VERIFICATION,
): number | null => {
  const {
    identity_status,
    last_order_type,
    occupation_status,
    credit_status,
    dl_status,
  } = userVerification

  const verification_case = getVerificationCaseFromOrderType(last_order_type)

  if (verification_case === VERIFICATION_CASE.IdentityOnly) {
    return isStatusRequested(identity_status) ? 1 : null
  }

  if (verification_case === VERIFICATION_CASE.IdentityAndChoice) {
    return isStatusRequested(identity_status) ? 1 : 2
  }

  if (verification_case === VERIFICATION_CASE.AllRequired) {
    if (isStatusRequested(identity_status) || isStatusRequested(dl_status)) {
      return 1
    }
    if (isStatusRequested(occupation_status)) {
      return 3
    }
    if (isStatusRequested(credit_status)) {
      return 4
    }
  }

  return null
}

export const getVerificationCaseFromOrderType = (
  orderType: ORDER_TYPES,
): VERIFICATION_CASE => {
  switch (orderType) {
    case ORDER_TYPES.LRLV:
      return VERIFICATION_CASE.IdentityOnly
    case ORDER_TYPES.LRHV:
      return VERIFICATION_CASE.IdentityAndChoice
    case ORDER_TYPES.HRLV:
      return VERIFICATION_CASE.IdentityAndChoice
    case ORDER_TYPES.HRHV:
      return VERIFICATION_CASE.AllRequired
    default:
      return VERIFICATION_CASE.AllRequired // fallback to most basic case
  }
}

export const getIdentityStatus = (userVerification: USER_VERIFICATION) => {
  const { identity_status, dl_status } = userVerification
  if (
    isStatusNotRequested(identity_status) &&
    isStatusNotRequested(dl_status ?? "")
  ) {
    return VERIFICATION_STATUS.Received
  } else {
    return VERIFICATION_STATUS.Requested
  }
}
