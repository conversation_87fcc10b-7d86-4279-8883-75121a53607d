import { ReadonlyURLSearchParams } from "next/navigation"

export interface CheckoutQueryParams {
  bike_code?: string
  hub_id?: string
}

export interface ValidatedCheckoutParams {
  bike_code: string
  hub_id: string
  isValid: boolean
  errors: string[]
}

/**
 * Parse and validate checkout query parameters
 */
export function parseCheckoutQueryParams(
  searchParams: ReadonlyURLSearchParams | URLSearchParams,
): CheckoutQueryParams {
  return {
    bike_code: searchParams.get("bike_code") || undefined,
    hub_id: searchParams.get("hub_id") || undefined,
  }
}

/**
 * Validate checkout query parameters with business logic
 */
export function validateCheckoutQueryParams(
  params: CheckoutQueryParams,
): ValidatedCheckoutParams {
  const errors: string[] = []

  // Validate required parameters
  if (!params.bike_code) {
    errors.push("bike_code is required")
  }

  if (!params.hub_id) {
    errors.push("hub_id is required")
  }

  return {
    bike_code: params.bike_code || "",
    hub_id: params.hub_id || "",
    isValid: errors.length === 0,
    errors,
  }
}

/**
 * Generate checkout URL with query parameters
 */
export function generateCheckoutUrl(params: {
  bike_code: string
  hub_id: string
}): string {
  const searchParams = new URLSearchParams({
    bike_code: params.bike_code,
    hub_id: params.hub_id,
  })

  return `/checkout?${searchParams.toString()}`
}
