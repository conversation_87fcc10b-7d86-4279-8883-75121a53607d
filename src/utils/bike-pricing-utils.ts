import { BUSINESS_HOURS } from "@/lib/bike-rental-utils"

export interface PricingCalculationParams {
  price_per_hour: number
  total_hours?: number
  surge?: number
}

export interface PricingResult {
  totalPrice: number
  effectiveHourlyRate: number
}

export function calculateBikePricing(
  params: PricingCalculationParams,
): PricingResult {
  const { price_per_hour, total_hours, surge = 1 } = params
  const hours = total_hours || BUSINESS_HOURS.MIN_RENTAL_HOURS
  const totalPrice = price_per_hour * hours * surge
  const effectiveHourlyRate = price_per_hour * surge

  return {
    totalPrice,
    effectiveHourlyRate,
  }
}

export function isSurgeActive(surgeMultiplier?: number): boolean {
  return (surgeMultiplier ?? 1) > 1
}

export function formatSurgeMultiplier(surgeMultiplier: number): string {
  return `${surgeMultiplier}x`
}
