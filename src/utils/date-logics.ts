import {
  addDays,
  differenceInDays,
  format,
  isAfter,
  isBefore,
  isToday,
  setHours,
  setMinutes,
  setSeconds,
  startOfDay,
} from "date-fns"

// Format date to short format - example: formatShortDate(new Date()) returns "31st Dec"
export const formatShortDate = (date: Date) => {
  if (!date) return

  const actualDate = new Date(date)

  const day = actualDate.getDate()
  const suffix = getDaySuffix(day)
  const month = actualDate.toLocaleString("en-US", { month: "short" })

  return `${day}${suffix} ${month}`
}

// Get ordinal suffix for day number - example: getDaySuffix(1) returns "st"
const getDaySuffix = (day: number) => {
  if (day >= 11 && day <= 13) return "th"
  switch (day % 10) {
    case 1:
      return "st"
    case 2:
      return "nd"
    case 3:
      return "rd"
    default:
      return "th"
  }
}

// Check if two dates are equal (same day) - example: areDatesEqual(date1, date2) returns boolean
export const areDatesEqual = (date1: Date, date2: Date) => {
  if (date1 && date2) {
    date1 = startOfDay(date1)
    date2 = startOfDay(date2)
    return date1.getTime() === date2.getTime()
  }
}

// Format date to standard format - example: formatDate(new Date()) returns "Dec 31, 2021"
export const formatDate = (date: Date) =>
  format(date ?? new Date(), "MMM dd, yyyy")

// Format date for transactions - example: formatTransactionDate(new Date()) returns "Dec 31, 2021 04:00 pm"
export const formatTransactionDate = (date: Date) =>
  format(date, "MMM dd, yyyy hh:mm a")

// Format date with ordinal suffix - example: formatDateWithOrdinal(new Date()) returns "31st Dec"
export const formatDateWithOrdinal = (date: Date) =>
  format(date || new Date(), "do MMM")

// Get day name from date - example: getDay(new Date()) returns "Friday"
export const getDay = (date: Date) =>
  new Date(date).toLocaleDateString("en-US", { weekday: "long" })

export const isValidDateRange = (start: Date, end: Date) => {
  const startDay = startOfDay(start)
  const endDay = startOfDay(end)
  return !isBefore(endDay, startDay)
}

export const calculateDaysBetween = (start: Date, end: Date) => {
  const startDay = startOfDay(start)
  const endDay = startOfDay(end)
  const days = Math.round(
    (endDay.getTime() - startDay.getTime()) / (1000 * 60 * 60 * 24),
  )
  return Math.max(0, days)
}

// Adjust date by days with 90-day limit - example: adjustDate(5, new Date()) returns date 5 days from now
export const adjustDate = (days: number, date: Date | undefined): Date => {
  // Default to today's date if undefined
  const currentDate = new Date()
  date = date ? new Date(date) : currentDate

  // Set date range limits (today to 90 days future)
  const minDate = currentDate
  const maxDate = addDays(currentDate, 90)

  // Calculate adjusted date
  const newDate = new Date(date)
  newDate.setDate(newDate.getDate() + days)

  if (newDate < minDate) {
    return minDate // Don't allow past dates
  }

  if (newDate > maxDate) {
    return maxDate // Don't allow dates beyond 90 days
  }

  return newDate
}

export const updateRentalDates = (
  deliveryDate: Date,
  pickupDate: Date,
  type: "delivery" | "pickup",
  days_type: "add" | "minus",
): {
  newDeliveryDate: Date
  newPickupDate: Date
  newTotalDays: number
} => {
  const today = startOfDay(new Date())
  const maxDate = addDays(today, 90)

  let newDeliveryDate = new Date(deliveryDate)
  let newPickupDate = new Date(pickupDate)

  if (type === "delivery") {
    if (days_type === "add") {
      const potentialNewDeliveryDate = addDays(newDeliveryDate, 1)
      if (differenceInDays(newPickupDate, potentialNewDeliveryDate) >= 2) {
        newDeliveryDate = potentialNewDeliveryDate
      } else {
        newDeliveryDate = potentialNewDeliveryDate
        newPickupDate = addDays(newDeliveryDate, 2)
      }
    } else if (days_type === "minus") {
      const potentialNewDeliveryDate = addDays(newDeliveryDate, -1)
      if (potentialNewDeliveryDate >= today) {
        newDeliveryDate = potentialNewDeliveryDate
        // Ensure pickup date maintains 2-day gap
        if (differenceInDays(newPickupDate, newDeliveryDate) < 2) {
          newPickupDate = addDays(newDeliveryDate, 2)
        }
      }
    }
  }

  if (type === "pickup") {
    if (days_type === "add") {
      const potentialNewPickupDate = addDays(newPickupDate, 1)
      if (potentialNewPickupDate <= maxDate) {
        newPickupDate = potentialNewPickupDate
      }
    } else if (days_type === "minus") {
      const potentialNewPickupDate = addDays(newPickupDate, -1)
      if (differenceInDays(newPickupDate, newDeliveryDate) > 2) {
        newPickupDate = potentialNewPickupDate
      } else if (differenceInDays(newPickupDate, newDeliveryDate) === 2) {
        // If the gap is exactly 2 days, move both dates back by 1 day if possible
        const potentialNewDeliveryDate = addDays(newDeliveryDate, -1)
        if (potentialNewDeliveryDate >= today) {
          newDeliveryDate = potentialNewDeliveryDate
          newPickupDate = potentialNewPickupDate
        }
      }
    }
  }

  // Calculate total rental days (excluding delivery & pickup days)
  const newTotalDays = Math.max(
    differenceInDays(newPickupDate, newDeliveryDate) - 1,
    1,
  )

  return { newDeliveryDate, newPickupDate, newTotalDays }
}

// Check if date should be disabled based on criteria - example: disableDates(new Date(), 2, "delivery") returns boolean
export const disableDates = (
  date: Date,
  min_th: number = 0,
  type: "delivery" | "pickup" = "delivery",
  deliveryDate?: Date | null,
) => {
  // Calculate earliest allowed date based on minimum threshold
  const transitDate = calculateTransitDate(min_th)

  // Normalize date for comparison
  const dateToCheck = startOfDay(new Date(date))

  // Check if date is before minimum allowed date
  const isBeforeTransitDate = isDateBefore(dateToCheck, transitDate)

  // Set maximum allowed date (90 days from today)
  const today = new Date()
  const maxDate = addDays(today, 90)

  // Handle delivery date validation
  if (type === "delivery") {
    return isBeforeTransitDate || isAfter(dateToCheck, maxDate)
  }

  // Handle pickup dates
  if (type === "pickup" && deliveryDate) {
    // Pickup must be at least 1 day after delivery
    const minPickupDate = addDays(deliveryDate, 1)

    // Maximum pickup date is 90 days after delivery
    const maxPickupDate = addDays(deliveryDate, 90)

    // Use the later of transit date or day after delivery
    const effectiveMinPickupDate = isAfter(minPickupDate, transitDate)
      ? minPickupDate
      : transitDate

    return (
      dateToCheck < effectiveMinPickupDate ||
      isAfter(dateToCheck, maxPickupDate)
    )
  }

  if (isToday(date)) {
    const eightPM = setSeconds(setMinutes(setHours(new Date(), 20), 0), 0)
    // console.log("todayCheck", eightPM, new Date(), isAfter(new Date(), eightPM))
    return isAfter(new Date(), eightPM)
  }

  // Default case
  return isBeforeTransitDate || isAfter(dateToCheck, maxDate)
}

const isDateBefore = (date: Date, compareDate: Date): boolean =>
  date.getFullYear() < compareDate.getFullYear() ||
  (date.getFullYear() === compareDate.getFullYear() &&
    date.getMonth() < compareDate.getMonth()) ||
  (date.getFullYear() === compareDate.getFullYear() &&
    date.getMonth() === compareDate.getMonth() &&
    date.getDate() < compareDate.getDate())

// Calculate transit date with minimum threshold hours - example: calculateTransitDate(2) returns date 2 hours from now
export const calculateTransitDate = (minTh: number): Date => {
  const today = new Date()
  const currentHour = today.getHours()

  // Create new date object to avoid mutation
  const transitDate = new Date(today)

  // Move to next day if threshold hours exceed current day
  if (currentHour + minTh >= 24) {
    transitDate.setDate(transitDate.getDate() + 1) // Next day
    transitDate.setHours(0, 0, 0, 0) // Start of day
  }

  // Add threshold hours to current time
  transitDate.setTime(transitDate.getTime() + minTh * 60 * 60 * 1000)

  return transitDate
}

export const isDateBeforeToday = (date: Date | null | undefined): boolean => {
  if (!date) {
    return false
  }

  const today = startOfDay(new Date())
  const normalizedDate = startOfDay(date)

  return normalizedDate.getTime() < today.getTime()
}
