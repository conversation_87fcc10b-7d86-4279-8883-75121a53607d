// function combineDateWithCurrentTime(passedDate: Date) {
//   const datePart = new Date(passedDate)
//   const now = new Date()

//   datePart.setHours(now.getHours())
//   datePart.setMinutes(now.getMinutes())
//   datePart.setSeconds(now.getSeconds())
//   datePart.setMilliseconds(now.getMilliseconds())

//   return datePart
// }

import {
  addHours,
  format,
  isBefore,
  isToday,
  setHours,
  setMinutes,
} from "date-fns"

export function generateTimeSlots(
  isPickup: boolean,
  checkingDate: Date,
  newPickup: boolean = false,
  cost?: number,
) {
  const slots: {
    value: string
    label: string
    disabled: boolean
    cost: number
  }[] = []

  const now = new Date()
  // const checkingDateWithCurrentTime = isToday(checkingDate)
  //   ? combineDateWithCurrentTime(checkingDate)
  //   : checkingDate
  // console.log("checking", checkingDateWithCurrentTime)

  // Default slots from 9:00 AM to 2:00 PM
  for (let hour = 9; hour <= 14; hour++) {
    const startDate = setMinutes(setHours(checkingDate, hour), 0)
    const endDate = addHours(startDate, 1)

    // For today, disable slots that have already passed
    const isDisabled = isToday(checkingDate) ? isBefore(startDate, now) : false

    slots.push({
      value: format(startDate, "HH"),
      label: `${format(startDate, "hh:00 aa")} - ${format(endDate, "hh:00 aa")}`,
      disabled: isDisabled,
      cost: 0,
    })
  }

  if (isPickup) {
    // Add 3:00 PM - 4:00 PM slot if it's a pickup
    const pickupStart = setMinutes(setHours(checkingDate, 15), 0)
    const pickupEnd = addHours(pickupStart, 1)
    const isPickupDisabled = isToday(checkingDate)
      ? isBefore(pickupStart, now)
      : false

    slots.push({
      value: format(pickupStart, "HH"),
      label: `${format(pickupStart, "hh:00 aa")} - ${format(pickupEnd, "hh:00 aa")}`,
      disabled: isPickupDisabled,
      cost: cost || 0,
    })

    if (newPickup) {
      for (let hour = 16; hour <= 21; hour++) {
        const startDate = setMinutes(setHours(checkingDate, hour), 0)
        const endDate = addHours(startDate, 1)
        const isSlotDisabled = isToday(checkingDate)
          ? isBefore(startDate, now)
          : false

        slots.push({
          value: format(startDate, "HH"),
          label: `${format(startDate, "hh:00 aa")} - ${format(endDate, "hh:00 aa")}`,
          disabled: isSlotDisabled,
          cost: cost || 0,
        })
      }
    }
  } else {
    // Add delivery slots from 4:00 PM to 9:00 PM
    for (let hour = 16; hour <= 21; hour++) {
      const startDate = setMinutes(setHours(checkingDate, hour), 0)
      const endDate = addHours(startDate, 1)
      const isSlotDisabled = isToday(checkingDate)
        ? isBefore(startDate, now)
        : false

      slots.push({
        value: format(startDate, "HH"),
        label: `${format(startDate, "hh:00 aa")} - ${format(endDate, "hh:00 aa")}`,
        disabled: isSlotDisabled,
        cost: 0,
      })
    }
  }

  return slots
}
