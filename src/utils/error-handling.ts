// Custom error type
export type FetchError = {
  message: string
  statusCode?: number
  details?: Record<string, unknown>
}

// Utility function to handle errors
export const handleFetchError = (
  message: string,
  response?: Response,
): FetchError => {
  const error: FetchError = { message }
  if (response) {
    error.statusCode = response.status
    error.details = {
      statusText: response.statusText,
      url: response.url,
    }
  }
  return error
}
