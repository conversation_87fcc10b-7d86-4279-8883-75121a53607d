{"name": "next-web-app", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "format": "prettier --write .", "format:check": "prettier --check .", "typecheck": "tsc --noEmit", "clean": "rm -rf .next out node_modules", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "prepare": "husky install", "preinstall": "npx only-allow pnpm", "check:all": "pnpm run lint && pnpm run format:check && pnpm run typecheck", "fix:all": "pnpm run lint:fix && pnpm run format", "analyze": "ANALYZE=true pnpm build", "analyze:server": "ANALYZE=true BUNDLE_ANALYZE=server pnpm build", "analyze:browser": "ANALYZE=true BUNDLE_ANALYZE=browser pnpm build"}, "dependencies": {"@hookform/resolvers": "^3.9.1", "@lottiefiles/react-lottie-player": "^3.6.0", "@next/bundle-analyzer": "^15.2.5", "@next/third-parties": "15.2.3", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-tooltip": "^1.2.7", "@sentry/nextjs": "^8", "@tanstack/react-query": "^5.60.2", "@tanstack/react-query-devtools": "^5.60.2", "@vercel/analytics": "^1.4.0", "axios": "^1.7.9", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "cmdk": "1.0.0", "date-fns": "^4.1.0", "embla-carousel-autoplay": "^8.5.1", "embla-carousel-react": "^8.3.1", "framer-motion": "^12.0.0-alpha.1", "import-in-the-middle": "^1.12.0", "input-otp": "^1.4.1", "lottie-react": "^2.4.0", "lucide-react": "^0.456.0", "next": "15.2.3", "next-themes": "^0.4.3", "nextjs-toploader": "^3.7.15", "posthog-js": "^1.253.4", "react": "19.0.0", "react-day-picker": "8.10.1", "react-dom": "19.0.0", "react-dropzone": "^14.3.8", "react-hook-form": "^7.54.2", "react-intersection-observer": "^9.15.0", "react-scroll": "^1.9.3", "react-swipeable": "^7.0.2", "require-in-the-middle": "^7.4.0", "sharepal-icons": "^1.0.0", "sonner": "^1.7.0", "swiper": "^11.2.4", "tailwind-merge": "^2.5.4", "tailwind-scrollbar": "^3.1.0", "vaul": "^1.1.1", "zod": "^3.24.1", "zustand": "^5.0.2"}, "devDependencies": {"@types/googlemaps": "^3.43.3", "@types/node": "^20", "@types/react": "19.0.12", "@types/react-dom": "19.0.4", "@types/react-scroll": "^1.8.10", "autoprefixer": "^10.4.20", "encoding": "^0.1.13", "eslint": "^8.57.1", "eslint-config-next": "15.2.3", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.2.1", "eslint-plugin-react": "^7.37.2", "husky": "^9.1.6", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "lint-staged": "^15.2.10", "msw": "^2.6.4", "postcss": "^8", "prettier": "^3.3.3", "prettier-plugin-tailwindcss": "^0.6.8", "tailwindcss": "^3.4.1", "tailwindcss-animate": "^1.0.7", "typescript": "^5"}, "msw": {"workerDirectory": ["public"]}, "pnpm": {"overrides": {"@types/react": "19.0.12", "@types/react-dom": "19.0.4"}}}