import withBundleAnalyzer from "@next/bundle-analyzer"
import { withSentryConfig } from "@sentry/nextjs"
import type { NextConfig } from "next"

// Initialize bundle analyzer
const bundleAnalyzer = withBundleAnalyzer({
  enabled: process.env.ANALYZE === "true",
})

const nextConfig: NextConfig = {
  logging: {
    fetches: {
      fullUrl: true,
    },
  },
  /* config options here */
  images: {
    // dangerouslyAllowSVG: true,
    remotePatterns: [
      {
        protocol: "https",
        hostname: "images.sharepal.in",
        port: "",
        pathname: "/**",
      },
      // https://utfs.io/f/qPlpyBmwd8UN0uIgOPHjiT1qY2szFub9aEHfI6BdlnMVcQGe
      {
        protocol: "https",
        hostname: "utfs.io",
        port: "",
        pathname: "/**",
      },
      {
        protocol: "https",
        hostname: "ik.imagekit.io",
        port: "",
        pathname: "/**",
      },
      {
        protocol: "https",
        hostname: "images.unsplash.com",
        port: "",
        pathname: "/**",
      },
      {
        protocol: "https",
        hostname: "drive.google.com",
        port: "",
        pathname: "/**",
      },
    ],
  },

  reactStrictMode: false,

  experimental: {
    serverActions: {
      bodySizeLimit: "10mb", // Increase this limit as needed
      allowedOrigins: [
        "gateway.zoop.one",
        "staging-gateway.zoop.one", // Codespaces
        "gateway.zoop.plus",
        "staging-gateway.zoop.plus", // Codespaces
        "admin.sharepal.in",
      ],
    },
  },

  async headers() {
    return [
      {
        source: "/(.*)",
        headers: [
          { key: "Access-Control-Allow-Origin", value: "*" },
          {
            key: "Access-Control-Allow-Headers",
            value: "X-Forwarded-Host, Origin, Content-Type",
          },
        ],
      },
    ]
  },
}

// Apply bundle analyzer and Sentry config
const configWithBundleAnalyzer = bundleAnalyzer(nextConfig)

export default withSentryConfig(configWithBundleAnalyzer, {
  org: "sharepal-dn",
  project: "sharepal-development",

  // Only print logs for uploading source maps in CI
  silent: !process.env.CI,
  // disable source maps
  // sourcemaps: {
  //   disable: true,
  // },

  // For all available options, see:
  // https://docs.sentry.io/platforms/javascript/guides/nextjs/manual-setup/

  // Upload a larger set of source maps for prettier stack traces (increases build time)
  // widenClientFileUpload: true,

  // Uncomment to route browser requests to Sentry through a Next.js rewrite to circumvent ad-blockers.
  // This can increase your server load as well as your hosting bill.
  // Note: Check that the configured route will not match with your Next.js middleware, otherwise reporting of client-
  // side errors will fail.
  // tunnelRoute: "/monitoring",

  // Hides source maps from generated client bundles

  hideSourceMaps: true,

  // Automatically tree-shake Sentry logger statements to reduce bundle size
  disableLogger: true,

  // Enables automatic instrumentation of Vercel Cron Monitors. (Does not yet work with App Router route handlers.)
  // See the following for more information:
  // https://docs.sentry.io/product/crons/
  // https://vercel.com/docs/cron-jobs
  automaticVercelMonitors: true,
})
