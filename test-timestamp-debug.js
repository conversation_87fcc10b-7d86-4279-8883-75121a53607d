// Test script to debug timestamp conversion issues

// Your problematic timestamps
const rawPickupTimestamp = 1755144000
const rawDropoffTimestamp = 1755338400

console.log("🔍 Testing timestamp conversion...")
console.log("Raw timestamps:", { rawPickupTimestamp, rawDropoffTimestamp })

// Convert to milliseconds
const toMilliseconds = (seconds) => seconds * 1000

const pickupMs = toMilliseconds(rawPickupTimestamp)
const dropoffMs = toMilliseconds(rawDropoffTimestamp)

console.log("Milliseconds:", { pickupMs, dropoffMs })

// Create Date objects
const pickup_date = new Date(pickupMs)
const dropoff_date = new Date(dropoffMs)

console.log("Date objects:")
console.log("pickup_date:", pickup_date)
console.log("dropoff_date:", dropoff_date)
console.log("pickup_date.toString():", pickup_date.toString())
console.log("dropoff_date.toString():", dropoff_date.toString())

// Check if dates are valid
console.log("Date validity:")
console.log("pickup_date valid:", !isNaN(pickup_date.getTime()))
console.log("dropoff_date valid:", !isNaN(dropoff_date.getTime()))

// Get hours and minutes
console.log("Time components:")
console.log("pickup hours:", pickup_date.getHours())
console.log("pickup minutes:", pickup_date.getMinutes())
console.log("dropoff hours:", dropoff_date.getHours())
console.log("dropoff minutes:", dropoff_date.getMinutes())

// Check what these timestamps represent in human readable format
console.log("Human readable dates:")
console.log("Pickup:", pickup_date.toLocaleString())
console.log("Dropoff:", dropoff_date.toLocaleString())
console.log(
  "Pickup IST:",
  pickup_date.toLocaleString("en-IN", { timeZone: "Asia/Kolkata" }),
)
console.log(
  "Dropoff IST:",
  dropoff_date.toLocaleString("en-IN", { timeZone: "Asia/Kolkata" }),
)

// Check if these are reasonable dates (not too far in future)
const now = new Date()
const yearFromNow = new Date()
yearFromNow.setFullYear(yearFromNow.getFullYear() + 1)

console.log("Date range check:")
console.log("Current date:", now.toLocaleString())
console.log("One year from now:", yearFromNow.toLocaleString())
console.log(
  "Pickup is reasonable:",
  pickup_date > now && pickup_date < yearFromNow,
)
console.log(
  "Dropoff is reasonable:",
  dropoff_date > now && dropoff_date < yearFromNow,
)

// Test the exact conversion that's failing
console.log("\n🧪 Testing exact conversion from your error:")
const testTimestamp = 1755144000
console.log("Test timestamp:", testTimestamp)
console.log("Converted to ms:", testTimestamp * 1000)
console.log("Date object:", new Date(testTimestamp * 1000))
console.log("Date string:", new Date(testTimestamp * 1000).toString())
console.log("Is valid:", !isNaN(new Date(testTimestamp * 1000).getTime()))
